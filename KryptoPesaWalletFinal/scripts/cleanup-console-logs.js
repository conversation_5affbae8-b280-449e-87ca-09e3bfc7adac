#!/usr/bin/env node

/**
 * <PERSON>ript to clean up console.log statements for production readiness
 * Replaces console statements with proper logging or development-only guards
 */

const fs = require('fs');
const path = require('path');

// Files to process
const filesToProcess = [
  // Mobile app files
  'mobile/src/screens/ProfileScreen.js',
  'mobile/src/screens/ReceiveScreen.js',
  'mobile/src/screens/TradeDetailsScreen.js',
  'mobile/src/screens/auth/LoginScreen.js',
  'mobile/src/screens/BiometricSettingsScreen.js',
  'mobile/src/screens/QRScannerScreen.js',
  'mobile/src/screens/PaymentProofScreen.js',
  'mobile/src/services/apiService.js',
  'mobile/src/services/biometricService.js',
  'mobile/src/services/appService.js',
  'mobile/src/services/authService.js',
  'mobile/src/services/cameraService.js',
  'mobile/src/services/socketService.js',
  'mobile/src/services/notificationService.js',
  'mobile/src/services/notificationService.expo.js',
  
  // Admin dashboard files
  'admin-dashboard/src/pages/UsersPage.js',
  'admin-dashboard/src/pages/TradesPage.js',
  'admin-dashboard/src/pages/DisputesPage.js',
  'admin-dashboard/src/pages/OffersPage.js',
  'admin-dashboard/src/pages/DashboardPage.js',
  'admin-dashboard/src/pages/AnalyticsPage.js',
  'admin-dashboard/src/services/AuthContext.js',
];

// Patterns to replace
const replacements = [
  // Error logging - keep in development, use proper logging in production
  {
    pattern: /console\.error\(['"`]([^'"`]+)['"`],\s*([^)]+)\);/g,
    replacement: (match, message, variable) => {
      if (match.includes('mobile/src/')) {
        return `if (__DEV__) {\n      console.error('${message}', ${variable});\n    }`;
      } else {
        return `logger.error('${message}', ${variable});`;
      }
    }
  },
  
  // Info/debug logging - development only
  {
    pattern: /console\.log\(['"`]([^'"`]+)['"`],?\s*([^)]*)\);/g,
    replacement: (match, message, variable) => {
      const varPart = variable ? `, ${variable}` : '';
      return `if (__DEV__) {\n      console.log('${message}'${varPart});\n    }`;
    }
  },
  
  // Warning logging - keep in development
  {
    pattern: /console\.warn\(['"`]([^'"`]+)['"`],?\s*([^)]*)\);/g,
    replacement: (match, message, variable) => {
      const varPart = variable ? `, ${variable}` : '';
      return `if (__DEV__) {\n      console.warn('${message}'${varPart});\n    }`;
    }
  }
];

function processFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;
  
  // Apply replacements
  replacements.forEach(({ pattern, replacement }) => {
    const originalContent = content;
    content = content.replace(pattern, replacement);
    if (content !== originalContent) {
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Cleaned up console statements in: ${filePath}`);
  } else {
    console.log(`ℹ️  No console statements found in: ${filePath}`);
  }
}

function main() {
  console.log('🧹 Starting console.log cleanup for production readiness...\n');
  
  filesToProcess.forEach(processFile);
  
  console.log('\n✅ Console cleanup completed!');
  console.log('\nNext steps:');
  console.log('1. Review the changes to ensure they are correct');
  console.log('2. Test the application to ensure functionality is preserved');
  console.log('3. Commit the changes');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, replacements };
