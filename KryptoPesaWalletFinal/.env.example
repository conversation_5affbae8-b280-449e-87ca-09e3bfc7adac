# KryptoPesa Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/kryptopesa
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# Blockchain Configuration
# Polygon Network (Primary)
POLYGON_RPC_URL=https://polygon-rpc.com
POLYGON_CHAIN_ID=137
POLYGON_PRIVATE_KEY=your-polygon-private-key

# Ethereum Network (Fallback)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
ETHEREUM_CHAIN_ID=1
ETHEREUM_PRIVATE_KEY=your-ethereum-private-key

# Bitcoin Network
BITCOIN_NETWORK=mainnet
BITCOIN_RPC_URL=https://blockstream.info/api

# Smart Contract Addresses (Deploy and update these)
ESCROW_CONTRACT_ADDRESS=0x...
USDT_CONTRACT_ADDRESS=******************************************
USDC_CONTRACT_ADDRESS=******************************************

# External APIs
COINGECKO_API_KEY=your-coingecko-api-key
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key

# Push Notifications
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_PROJECT_ID=your-firebase-project-id

# File Upload
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Email Configuration (for admin notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Commission Settings
PLATFORM_COMMISSION_RATE=0.005
MINIMUM_TRADE_AMOUNT=10
MAXIMUM_TRADE_AMOUNT=50000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+254700000000

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_CORS=true
