import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Chat State
class ChatState {
  final bool isLoading;
  final String? error;
  final List<dynamic> chats;
  final List<dynamic> messages;

  const ChatState({
    this.isLoading = false,
    this.error,
    this.chats = const [],
    this.messages = const [],
  });

  ChatState copyWith({
    bool? isLoading,
    String? error,
    List<dynamic>? chats,
    List<dynamic>? messages,
  }) {
    return ChatState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      chats: chats ?? this.chats,
      messages: messages ?? this.messages,
    );
  }
}

/// Chat Notifier
class ChatNotifier extends StateNotifier<ChatState> {
  ChatNotifier() : super(const ChatState());

  Future<void> loadChats() async {
    // TODO: Implement chats loading
    state = state.copyWith(isLoading: true);
    await Future.delayed(const Duration(seconds: 1));
    state = state.copyWith(isLoading: false);
  }
}

/// Chat Provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier();
});
