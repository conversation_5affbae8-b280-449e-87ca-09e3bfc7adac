import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/storage/local_storage.dart';
import '../../../core/storage/secure_storage.dart';
import '../../../core/network/api_client.dart';
import '../../../core/utils/logger.dart';
import '../../../shared/models/user_model.dart';

/// Authentication State
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserModel? user;
  final String? error;
  final bool isEmailVerified;
  final bool isBiometricEnabled;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.error,
    this.isEmailVerified = false,
    this.isBiometricEnabled = false,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    UserModel? user,
    String? error,
    bool? isEmailVerified,
    bool? isBiometricEnabled,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
    );
  }
}

/// Authentication Provider
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState()) {
    _initializeAuth();
  }

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    state = state.copyWith(isLoading: true);

    try {
      final token = LocalStorage.getAuthToken();
      final userData = LocalStorage.getUser();

      if (token != null && userData != null) {
        final user = UserModel.fromJson(userData);
        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isEmailVerified: user.isEmailVerified,
          isLoading: false,
        );

        // Check biometric status
        final biometricEnabled = await SecureStorage.isBiometricEnabled();
        state = state.copyWith(isBiometricEnabled: biometricEnabled);

        AppLogger.info('User authenticated from stored credentials');
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize auth', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize authentication',
      );
    }
  }

  /// Login with email and password
  Future<bool> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await ApiClient.post('/api/auth/login', data: {
        'email': email,
        'password': password,
        'rememberMe': rememberMe,
      });

      final data = response.data;
      final token = data['token'] as String;
      final refreshToken = data['refreshToken'] as String;
      final userData = data['user'] as Map<String, dynamic>;

      // Store tokens
      await LocalStorage.saveAuthToken(token);
      await LocalStorage.saveRefreshToken(refreshToken);

      // Store user data
      await LocalStorage.saveUser(userData);

      final user = UserModel.fromJson(userData);
      state = state.copyWith(
        isAuthenticated: true,
        user: user,
        isEmailVerified: user.isEmailVerified,
        isLoading: false,
      );

      AppLogger.userAction('Login successful', {'email': email});
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Login failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed. Please check your credentials.',
      );
      return false;
    }
  }

  /// Register new user
  Future<bool> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    String? referralCode,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await ApiClient.post('/api/auth/register', data: {
        'email': email,
        'password': password,
        'firstName': firstName,
        'lastName': lastName,
        'phoneNumber': phoneNumber,
        'referralCode': referralCode,
      });

      final data = response.data;
      final message = data['message'] as String;

      state = state.copyWith(isLoading: false);

      AppLogger.userAction('Registration successful', {'email': email});
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Registration failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed. Please try again.',
      );
      return false;
    }
  }

  /// Verify email
  Future<bool> verifyEmail(String verificationCode) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await ApiClient.post('/api/auth/verify-email', data: {
        'verificationCode': verificationCode,
      });

      state = state.copyWith(
        isEmailVerified: true,
        isLoading: false,
      );

      AppLogger.userAction('Email verified', null);
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Email verification failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Email verification failed. Please try again.',
      );
      return false;
    }
  }

  /// Resend verification email
  Future<bool> resendVerificationEmail() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await ApiClient.post('/api/auth/resend-verification');

      state = state.copyWith(isLoading: false);

      AppLogger.userAction('Verification email resent', null);
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Failed to resend verification email', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to resend verification email.',
      );
      return false;
    }
  }

  /// Forgot password
  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await ApiClient.post('/api/auth/forgot-password', data: {
        'email': email,
      });

      state = state.copyWith(isLoading: false);

      AppLogger.userAction('Password reset requested', {'email': email});
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Forgot password failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to send password reset email.',
      );
      return false;
    }
  }

  /// Reset password
  Future<bool> resetPassword({
    required String resetToken,
    required String newPassword,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await ApiClient.post('/api/auth/reset-password', data: {
        'resetToken': resetToken,
        'newPassword': newPassword,
      });

      state = state.copyWith(isLoading: false);

      AppLogger.userAction('Password reset successful', null);
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Password reset failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Password reset failed. Please try again.',
      );
      return false;
    }
  }

  /// Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await ApiClient.post('/api/auth/change-password', data: {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      });

      state = state.copyWith(isLoading: false);

      AppLogger.userAction('Password changed', null);
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Password change failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to change password.',
      );
      return false;
    }
  }

  /// Logout
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      // Call logout API
      await ApiClient.post('/api/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      AppLogger.warning('Logout API call failed', error);
    }

    // Clear local data
    await LocalStorage.clearAuthTokens();
    await LocalStorage.clearUser();

    state = const AuthState();

    AppLogger.userAction('Logout successful', null);
  }

  /// Refresh token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = LocalStorage.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await ApiClient.post('/api/auth/refresh', data: {
        'refreshToken': refreshToken,
      });

      final data = response.data;
      final newToken = data['token'] as String;
      final newRefreshToken = data['refreshToken'] as String;

      await LocalStorage.saveAuthToken(newToken);
      await LocalStorage.saveRefreshToken(newRefreshToken);

      AppLogger.info('Token refreshed successfully');
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Token refresh failed', error, stackTrace);
      await logout();
      return false;
    }
  }

  /// Update user profile
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await ApiClient.put('/api/users/profile', data: profileData);

      final userData = response.data['user'] as Map<String, dynamic>;
      await LocalStorage.saveUser(userData);

      final user = UserModel.fromJson(userData);
      state = state.copyWith(
        user: user,
        isLoading: false,
      );

      AppLogger.userAction('Profile updated', profileData);
      return true;
    } catch (error, stackTrace) {
      AppLogger.error('Profile update failed', error, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update profile.',
      );
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state.isAuthenticated;

  /// Get current user
  UserModel? get currentUser => state.user;
}

/// Auth Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

/// Current User Provider
final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

/// Is Authenticated Provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

/// Is Loading Provider
final isAuthLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

/// Auth Error Provider
final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
