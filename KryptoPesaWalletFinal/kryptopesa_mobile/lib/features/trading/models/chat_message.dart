import 'package:json_annotation/json_annotation.dart';

part 'chat_message.g.dart';

@JsonSerializable()
class ChatMessage {
  final String id;
  final String tradeId;
  final String senderId;
  final String senderName;
  final String message;
  final DateTime timestamp;
  final MessageType type;
  final MessageStatus status;
  final String? attachmentUrl;
  final String? attachmentType;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.tradeId,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    this.type = MessageType.text,
    this.status = MessageStatus.sent,
    this.attachmentUrl,
    this.attachmentType,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);

  ChatMessage copyWith({
    String? id,
    String? tradeId,
    String? senderId,
    String? senderName,
    String? message,
    DateTime? timestamp,
    MessageType? type,
    MessageStatus? status,
    String? attachmentUrl,
    String? attachmentType,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      tradeId: tradeId ?? this.tradeId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      status: status ?? this.status,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      attachmentType: attachmentType ?? this.attachmentType,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get hasAttachment => attachmentUrl != null && attachmentUrl!.isNotEmpty;
  bool get isSystemMessage => type == MessageType.system;
  bool get isImageAttachment => attachmentType?.startsWith('image/') == true;
  bool get isFileAttachment => hasAttachment && !isImageAttachment;
}

enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('file')
  file,
  @JsonValue('system')
  system,
  @JsonValue('payment_proof')
  paymentProof,
}

enum MessageStatus {
  @JsonValue('sending')
  sending,
  @JsonValue('sent')
  sent,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
}

// System message helpers
class SystemMessage {
  static ChatMessage tradeAccepted(String tradeId, String buyerName) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: 'Trade accepted by $buyerName',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage escrowFunded(String tradeId, String amount, String currency) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: 'Escrow funded with $amount $currency',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage paymentSent(String tradeId, String buyerName) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: '$buyerName marked payment as sent',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage paymentConfirmed(String tradeId, String sellerName) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: '$sellerName confirmed payment received',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage tradeCompleted(String tradeId) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: 'Trade completed successfully',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage tradeCancelled(String tradeId, String reason) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: 'Trade cancelled: $reason',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }

  static ChatMessage disputeInitiated(String tradeId, String initiatorName) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      tradeId: tradeId,
      senderId: 'system',
      senderName: 'System',
      message: 'Dispute initiated by $initiatorName',
      timestamp: DateTime.now(),
      type: MessageType.system,
    );
  }
}
