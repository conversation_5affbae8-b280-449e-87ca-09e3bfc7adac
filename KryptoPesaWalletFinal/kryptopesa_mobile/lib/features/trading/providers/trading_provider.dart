import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/logger.dart';
import '../models/trading_models.dart';
import '../services/trading_service.dart';

// Trading State
class TradingState {
  final List<TradingOffer> offers;
  final List<Trade> activeTrades;
  final TradingStats? stats;
  final bool isLoading;
  final String? error;
  final bool isRefreshing;

  const TradingState({
    this.offers = const [],
    this.activeTrades = const [],
    this.stats,
    this.isLoading = false,
    this.error,
    this.isRefreshing = false,
  });

  TradingState copyWith({
    List<TradingOffer>? offers,
    List<Trade>? activeTrades,
    TradingStats? stats,
    bool? isLoading,
    String? error,
    bool? isRefreshing,
  }) {
    return TradingState(
      offers: offers ?? this.offers,
      activeTrades: activeTrades ?? this.activeTrades,
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get hasError => error != null;
  int get totalActiveOffers => offers.where((o) => o.isActive).length;
  int get buyOffersCount => offers.where((o) => o.isBuyOffer && o.isActive).length;
  int get sellOffersCount => offers.where((o) => o.isSellOffer && o.isActive).length;
}

// Trading Notifier
class TradingNotifier extends StateNotifier<TradingState> {
  TradingNotifier() : super(const TradingState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await TradingService.initialize();
      await _refreshInBackground();
    } catch (e) {
      AppLogger.error('Failed to initialize trading', e);
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> _refreshInBackground() async {
    try {
      await Future.wait([
        refreshOffers(),
        refreshActiveTrades(),
        refreshTradingStats(),
      ]);
    } catch (e) {
      AppLogger.error('Background refresh failed', e);
    }
  }

  // Refresh offers
  Future<bool> refreshOffers() async {
    try {
      final offers = await TradingService.getOffers();
      state = state.copyWith(offers: offers);
      return true;
    } catch (e) {
      AppLogger.error('Failed to refresh offers', e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  // Refresh active trades
  Future<bool> refreshActiveTrades() async {
    try {
      final trades = await TradingService.getActiveTrades();
      state = state.copyWith(activeTrades: trades);
      return true;
    } catch (e) {
      AppLogger.error('Failed to refresh active trades', e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  // Refresh trading stats
  Future<bool> refreshTradingStats() async {
    try {
      final stats = await TradingService.getTradingStats();
      state = state.copyWith(stats: stats);
      return true;
    } catch (e) {
      AppLogger.error('Failed to refresh trading stats', e);
      return false;
    }
  }

  // Create offer
  Future<TradingOfferResult> createOffer(CreateOfferRequest request) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await TradingService.createOffer(request);

      if (result.success) {
        // Refresh offers after creation
        await refreshOffers();
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
      }

      return result;
    } catch (e) {
      AppLogger.error('Failed to create offer', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return TradingOfferResult.failure(e.toString());
    }
  }

  // Accept trade
  Future<TradeResult> acceptTrade(AcceptTradeRequest request) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await TradingService.acceptTrade(request);

      if (result.success) {
        // Refresh active trades after acceptance
        await refreshActiveTrades();
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
      }

      return result;
    } catch (e) {
      AppLogger.error('Failed to accept trade', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return TradeResult.failure(e.toString());
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final tradingProvider = StateNotifierProvider<TradingNotifier, TradingState>((ref) {
  return TradingNotifier();
});

// Additional providers for specific data
final tradingOffersProvider = Provider<List<TradingOffer>>((ref) {
  final tradingState = ref.watch(tradingProvider);
  return tradingState.offers;
});

final buyOffersProvider = Provider<List<TradingOffer>>((ref) {
  final tradingState = ref.watch(tradingProvider);
  return tradingState.offers.where((o) => o.isBuyOffer && o.isActive).toList();
});

final sellOffersProvider = Provider<List<TradingOffer>>((ref) {
  final tradingState = ref.watch(tradingProvider);
  return tradingState.offers.where((o) => o.isSellOffer && o.isActive).toList();
});

final activeTradesProvider = Provider<List<Trade>>((ref) {
  final tradingState = ref.watch(tradingProvider);
  return tradingState.activeTrades;
});

final tradingStatsProvider = Provider<TradingStats?>((ref) {
  final tradingState = ref.watch(tradingProvider);
  return tradingState.stats;
});
