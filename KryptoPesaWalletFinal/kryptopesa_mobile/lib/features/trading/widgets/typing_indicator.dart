import 'package:flutter/material.dart';

class TypingIndicator extends StatefulWidget {
  final List<String> typingUsers;
  final String? currentUserId;

  const TypingIndicator({
    super.key,
    required this.typingUsers,
    this.currentUserId,
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Filter out current user from typing users
    final otherTypingUsers = widget.typingUsers
        .where((userId) => userId != widget.currentUserId)
        .toList();

    if (otherTypingUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 12,
            backgroundColor: theme.colorScheme.secondary.withOpacity(0.1),
            child: Text(
              otherTypingUsers.length == 1 
                  ? otherTypingUsers.first[0].toUpperCase()
                  : '${otherTypingUsers.length}',
              style: TextStyle(
                color: theme.colorScheme.secondary,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Typing bubble
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Typing text
                Text(
                  _getTypingText(otherTypingUsers),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.outline,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // Animated dots
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildDot(0, theme),
                        const SizedBox(width: 2),
                        _buildDot(1, theme),
                        const SizedBox(width: 2),
                        _buildDot(2, theme),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(int index, ThemeData theme) {
    final delay = index * 0.2;
    final animationValue = (_animation.value - delay).clamp(0.0, 1.0);
    final opacity = (animationValue * 2).clamp(0.0, 1.0);
    
    return Container(
      width: 4,
      height: 4,
      decoration: BoxDecoration(
        color: theme.colorScheme.outline.withOpacity(opacity),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  String _getTypingText(List<String> typingUsers) {
    if (typingUsers.isEmpty) return '';
    
    if (typingUsers.length == 1) {
      return 'typing';
    } else if (typingUsers.length == 2) {
      return 'typing';
    } else {
      return '${typingUsers.length} people typing';
    }
  }
}
