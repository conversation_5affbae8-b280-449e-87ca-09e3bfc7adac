import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../core/services/websocket_service.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../models/trading_models.dart';
import '../providers/chat_provider.dart';
import '../widgets/chat_message_bubble.dart';
import '../widgets/chat_input_field.dart';
import '../widgets/typing_indicator.dart';
import '../widgets/trade_status_header.dart';

class TradeChatScreen extends ConsumerStatefulWidget {
  final String tradeId;

  const TradeChatScreen({
    super.key,
    required this.tradeId,
  });

  @override
  ConsumerState<TradeChatScreen> createState() => _TradeChatScreenState();
}

class _TradeChatScreenState extends ConsumerState<TradeChatScreen>
    with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _messageFocusNode = FocusNode();
  
  Timer? _typingTimer;
  bool _isTyping = false;
  bool _isAtBottom = true;
  StreamSubscription? _messageSubscription;
  StreamSubscription? _typingSubscription;
  StreamSubscription? _userStatusSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeChat();
    _setupScrollListener();
    _setupWebSocketListeners();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _messageSubscription?.cancel();
    _typingSubscription?.cancel();
    _userStatusSubscription?.cancel();
    _typingTimer?.cancel();
    _scrollController.dispose();
    _messageController.dispose();
    _messageFocusNode.dispose();
    WebSocketService.leaveTradeRoom(widget.tradeId);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _markVisibleMessagesAsRead();
    }
  }

  Future<void> _initializeChat() async {
    final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
    await chatNotifier.initializeChat();
    
    // Join WebSocket room
    await WebSocketService.joinTradeRoom(widget.tradeId);
    
    // Mark messages as read when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _markVisibleMessagesAsRead();
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final isAtBottom = _scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 100;
      
      if (_isAtBottom != isAtBottom) {
        setState(() {
          _isAtBottom = isAtBottom;
        });
      }

      // Mark messages as read when scrolling
      if (isAtBottom) {
        _markVisibleMessagesAsRead();
      }
    });
  }

  void _setupWebSocketListeners() {
    // Listen for new messages
    _messageSubscription = WebSocketService.chatMessages.listen((data) {
      if (data['tradeId'] == widget.tradeId) {
        final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
        
        if (data['type'] == 'messages_read') {
          chatNotifier.markMessagesAsRead(List<String>.from(data['messageIds']));
        } else {
          chatNotifier.addMessage(ChatMessage.fromJson(data));
          
          // Auto-scroll to bottom if user is near bottom
          if (_isAtBottom) {
            _scrollToBottom();
          }
          
          // Mark as read if screen is visible
          if (WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed) {
            _markVisibleMessagesAsRead();
          }
        }
      }
    });

    // Listen for typing indicators
    _typingSubscription = WebSocketService.typingIndicators.listen((data) {
      if (data['tradeId'] == widget.tradeId) {
        final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
        chatNotifier.updateTypingStatus(data['userId'], data['isTyping']);
      }
    });

    // Listen for user status updates
    _userStatusSubscription = WebSocketService.userStatusUpdates.listen((data) {
      final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
      chatNotifier.updateUserStatus(data);
    });
  }

  void _scrollToBottom({bool animated = true}) {
    if (_scrollController.hasClients) {
      if (animated) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      } else {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    }
  }

  void _markVisibleMessagesAsRead() {
    final chatState = ref.read(chatProvider(widget.tradeId));
    final unreadMessages = chatState.messages
        .where((m) => !m.isRead && m.senderId != chatState.currentUserId)
        .map((m) => m.id)
        .toList();

    if (unreadMessages.isNotEmpty) {
      final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
      chatNotifier.markMessagesAsRead(unreadMessages);
      WebSocketService.markMessagesAsRead(widget.tradeId, unreadMessages);
    }
  }

  void _onTypingChanged(String text) {
    if (text.isNotEmpty && !_isTyping) {
      _isTyping = true;
      WebSocketService.startTyping(widget.tradeId);
    }

    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 2), () {
      if (_isTyping) {
        _isTyping = false;
        WebSocketService.stopTyping(widget.tradeId);
      }
    });
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    _messageController.clear();
    _messageFocusNode.requestFocus();

    // Stop typing indicator
    if (_isTyping) {
      _isTyping = false;
      WebSocketService.stopTyping(widget.tradeId);
    }

    // Send via WebSocket
    WebSocketService.sendChatMessage(
      chatId: widget.tradeId,
      message: message,
    );

    // Always assume success for WebSocket (it's fire-and-forget)
    final success = true;
    if (!success) {
      // Fallback to API if WebSocket fails
      final chatNotifier = ref.read(chatProvider(widget.tradeId).notifier);
      await chatNotifier.sendMessage(message);
    }

    // Scroll to bottom
    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final chatState = ref.watch(chatProvider(widget.tradeId));

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBar(
        title: 'Trade Chat',
        subtitle: chatState.trade?.displayAmount,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: () => context.push('/trading/trade/${widget.tradeId}'),
            tooltip: 'Trade Details',
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: chatState.isLoading,
        child: Column(
          children: [
            // Trade status header
            if (chatState.trade != null)
              TradeStatusHeader(trade: chatState.trade!),

            // Messages list
            Expanded(
              child: _buildMessagesList(chatState),
            ),

            // Typing indicator
            if (chatState.typingUsers.isNotEmpty)
              TypingIndicator(
                typingUsers: chatState.typingUsers.keys.toList(),
                currentUserId: chatState.currentUserId,
              ),

            // Message input
            ChatInputField(
              controller: _messageController,
              focusNode: _messageFocusNode,
              onChanged: _onTypingChanged,
              onSend: _sendMessage,
              enabled: !chatState.isLoading,
            ),
          ],
        ),
      ),
      floatingActionButton: !_isAtBottom
          ? FloatingActionButton.small(
              onPressed: () => _scrollToBottom(),
              child: Icon(Icons.keyboard_arrow_down),
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
            )
          : null,
    );
  }

  Widget _buildMessagesList(ChatState chatState) {
    if (chatState.messages.isEmpty && !chatState.isLoading) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: chatState.messages.length,
      itemBuilder: (context, index) {
        final message = chatState.messages[index];
        final previousMessage = index > 0 ? chatState.messages[index - 1] : null;
        final nextMessage = index < chatState.messages.length - 1 
            ? chatState.messages[index + 1] 
            : null;

        final showDateSeparator = _shouldShowDateSeparator(message, previousMessage);
        final showAvatar = _shouldShowAvatar(message, nextMessage, chatState.currentUserId);
        final showTimestamp = _shouldShowTimestamp(message, nextMessage);

        return Column(
          children: [
            if (showDateSeparator)
              _buildDateSeparator(message.timestamp),
            
            ChatMessageBubble(
              message: message,
              isCurrentUser: message.senderId == chatState.currentUserId,
              showAvatar: showAvatar,
              showTimestamp: showTimestamp,
              partnerName: _getPartnerName(chatState),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 80,
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'Start the conversation',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Send a message to begin chatting with your trading partner',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSeparator(DateTime date) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(child: Divider()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              dateFormat.format(date),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(child: Divider()),
        ],
      ),
    );
  }

  bool _shouldShowDateSeparator(ChatMessage message, ChatMessage? previousMessage) {
    if (previousMessage == null) return true;
    
    final messageDate = DateTime(
      message.timestamp.year,
      message.timestamp.month,
      message.timestamp.day,
    );
    
    final previousDate = DateTime(
      previousMessage.timestamp.year,
      previousMessage.timestamp.month,
      previousMessage.timestamp.day,
    );
    
    return !messageDate.isAtSameMomentAs(previousDate);
  }

  bool _shouldShowAvatar(ChatMessage message, ChatMessage? nextMessage, String? currentUserId) {
    if (message.senderId == currentUserId) return false;
    if (nextMessage == null) return true;
    return nextMessage.senderId != message.senderId;
  }

  bool _shouldShowTimestamp(ChatMessage message, ChatMessage? nextMessage) {
    if (nextMessage == null) return true;
    
    final timeDiff = nextMessage.timestamp.difference(message.timestamp);
    return timeDiff.inMinutes > 5 || nextMessage.senderId != message.senderId;
  }

  String _getPartnerName(ChatState chatState) {
    if (chatState.trade == null) return 'Trading Partner';
    
    final currentUserId = chatState.currentUserId;
    if (chatState.trade!.buyer.id == currentUserId) {
      return chatState.trade!.seller.displayName;
    } else {
      return chatState.trade!.buyer.displayName;
    }
  }
}
