import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../providers/trading_provider.dart';
import '../widgets/trading_stats_card.dart';
import '../widgets/offer_list_view.dart';
import '../widgets/active_trades_list.dart';
import '../widgets/trading_quick_actions.dart';

class TradingScreen extends ConsumerStatefulWidget {
  const TradingScreen({super.key});

  @override
  ConsumerState<TradingScreen> createState() => _TradingScreenState();
}

class _TradingScreenState extends ConsumerState<TradingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeTrading();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeTrading() async {
    final tradingNotifier = ref.read(tradingProvider.notifier);
    await _refreshTradingData();
  }

  Future<void> _refreshTradingData() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });

    try {
      final tradingNotifier = ref.read(tradingProvider.notifier);
      await Future.wait([
        tradingNotifier.refreshOffers(),
        tradingNotifier.refreshActiveTrades(),
        tradingNotifier.refreshTradingStats(),
      ]);
    } catch (e) {
      // Error handling is done in the provider
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final tradingState = ref.watch(tradingProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBar(
        title: 'P2P Trading',
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_outlined),
            onPressed: () => context.push('/trading/notifications'),
            tooltip: 'Notifications',
          ),
          IconButton(
            icon: Icon(Icons.history),
            onPressed: () => context.push('/trading/history'),
            tooltip: 'Trading History',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Buy', icon: Icon(Icons.shopping_cart)),
            Tab(text: 'Sell', icon: Icon(Icons.sell)),
            Tab(text: 'My Trades', icon: Icon(Icons.swap_horiz)),
          ],
        ),
      ),
      body: LoadingOverlay(
        isLoading: tradingState.isLoading && !_isRefreshing,
        child: RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _refreshTradingData,
          color: theme.colorScheme.primary,
          child: Column(
            children: [
              // Trading Stats
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TradingStatsCard(
                  stats: tradingState.stats,
                  isLoading: _isRefreshing,
                ),
              ),

              // Quick Actions
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: TradingQuickActions(
                  onCreateBuyOffer: () => context.push('/trading/create-offer/buy'),
                  onCreateSellOffer: () => context.push('/trading/create-offer/sell'),
                  onViewMarket: () => context.push('/trading/market'),
                  onViewWallet: () => context.push('/wallet'),
                ),
              ),

              const SizedBox(height: 16),

              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Buy Tab
                    _buildBuyTab(),
                    
                    // Sell Tab
                    _buildSellTab(),
                    
                    // My Trades Tab
                    _buildMyTradesTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateOfferDialog(),
        icon: Icon(Icons.add),
        label: Text('Create Offer'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  Widget _buildBuyTab() {
    final tradingState = ref.watch(tradingProvider);
    final sellOffers = tradingState.offers.where((offer) => offer.type == 'sell').toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text(
                'Available Sell Offers',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog('buy'),
              ),
            ],
          ),
        ),
        Expanded(
          child: OfferListView(
            offers: sellOffers,
            isLoading: _isRefreshing,
            onOfferTap: (offer) => context.push('/trading/offer/${offer.id}'),
            onRefresh: _refreshTradingData,
            emptyMessage: 'No sell offers available.\nCreate a buy offer to get started!',
            emptyIcon: Icons.shopping_cart_outlined,
          ),
        ),
      ],
    );
  }

  Widget _buildSellTab() {
    final tradingState = ref.watch(tradingProvider);
    final buyOffers = tradingState.offers.where((offer) => offer.type == 'buy').toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text(
                'Available Buy Offers',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(Icons.filter_list),
                onPressed: () => _showFilterDialog('sell'),
              ),
            ],
          ),
        ),
        Expanded(
          child: OfferListView(
            offers: buyOffers,
            isLoading: _isRefreshing,
            onOfferTap: (offer) => context.push('/trading/offer/${offer.id}'),
            onRefresh: _refreshTradingData,
            emptyMessage: 'No buy offers available.\nCreate a sell offer to get started!',
            emptyIcon: Icons.sell_outlined,
          ),
        ),
      ],
    );
  }

  Widget _buildMyTradesTab() {
    final tradingState = ref.watch(tradingProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text(
                'Active Trades',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${tradingState.activeTrades.length} active',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ActiveTradesList(
            trades: tradingState.activeTrades,
            isLoading: _isRefreshing,
            onTradeTap: (trade) => context.push('/trading/trade/${trade.id}'),
            onRefresh: _refreshTradingData,
          ),
        ),
      ],
    );
  }

  void _showCreateOfferDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              const SizedBox(height: 24),
              
              Text(
                'Create Trading Offer',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 24),
              
              Row(
                children: [
                  Expanded(
                    child: _buildOfferTypeCard(
                      context,
                      'Buy Crypto',
                      'Create a buy offer to purchase cryptocurrency',
                      Icons.shopping_cart,
                      Colors.green,
                      () {
                        Navigator.pop(context);
                        context.push('/trading/create-offer/buy');
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildOfferTypeCard(
                      context,
                      'Sell Crypto',
                      'Create a sell offer to sell cryptocurrency',
                      Icons.sell,
                      Colors.orange,
                      () {
                        Navigator.pop(context);
                        context.push('/trading/create-offer/sell');
                      },
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfferTypeCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog(String tab) {
    // TODO: Implement filter dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Filter ${tab.toUpperCase()} Offers'),
        content: Text('Filter options will be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
