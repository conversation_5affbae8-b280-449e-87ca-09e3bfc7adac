import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/api_service.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/utils/logger.dart';
import '../models/trading_models.dart';

class TradingService {
  static const String _offersKey = 'trading_offers';
  static const String _tradesKey = 'active_trades';
  static const String _statsKey = 'trading_stats';
  
  static List<TradingOffer> _cachedOffers = [];
  static List<Trade> _cachedTrades = [];
  static TradingStats? _cachedStats;
  static bool _isInitialized = false;

  // Initialize the trading service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadCachedData();
    _isInitialized = true;
    
    AppLogger.info('TradingService initialized');
  }

  // Get all offers
  static Future<List<TradingOffer>> getOffers({
    String? cryptocurrency,
    String? fiatCurrency,
    OfferType? type,
    double? minAmount,
    double? maxAmount,
  }) async {
    try {
      AppLogger.info('Fetching trading offers...');
      
      Map<String, String> queryParams = {};
      if (cryptocurrency != null) queryParams['cryptocurrency'] = cryptocurrency;
      if (fiatCurrency != null) queryParams['fiatCurrency'] = fiatCurrency;
      if (type != null) queryParams['type'] = type.name;
      if (minAmount != null) queryParams['minAmount'] = minAmount.toString();
      if (maxAmount != null) queryParams['maxAmount'] = maxAmount.toString();
      
      String endpoint = ApiConstants.tradingOffers;
      if (queryParams.isNotEmpty) {
        final query = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
        endpoint += '?$query';
      }

      final response = await ApiService.get<Map<String, dynamic>>(endpoint);

      if (response.success && response.data != null) {
        final offersData = response.data!['data']['offers'] as List;
        final offers = offersData
            .map((o) => TradingOffer.fromJson(o as Map<String, dynamic>))
            .toList();
        
        _cachedOffers = offers;
        await _saveOffersData(offers);
        
        AppLogger.info('Fetched ${offers.length} trading offers');
        return offers;
      }
      
      return _cachedOffers;
    } catch (e) {
      AppLogger.error('Failed to get offers', e);
      return _cachedOffers;
    }
  }

  // Get active trades
  static Future<List<Trade>> getActiveTrades() async {
    try {
      AppLogger.info('Fetching active trades...');
      
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiConstants.tradingActiveTrades,
      );

      if (response.success && response.data != null) {
        final tradesData = response.data!['data']['trades'] as List;
        final trades = tradesData
            .map((t) => Trade.fromJson(t as Map<String, dynamic>))
            .toList();
        
        _cachedTrades = trades;
        await _saveTradesData(trades);
        
        AppLogger.info('Fetched ${trades.length} active trades');
        return trades;
      }
      
      return _cachedTrades;
    } catch (e) {
      AppLogger.error('Failed to get active trades', e);
      return _cachedTrades;
    }
  }

  // Get trading statistics
  static Future<TradingStats?> getTradingStats() async {
    try {
      AppLogger.info('Fetching trading stats...');
      
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiConstants.tradingStats,
      );

      if (response.success && response.data != null) {
        final statsData = response.data!['data']['stats'] as Map<String, dynamic>;
        final stats = TradingStats.fromJson(statsData);
        
        _cachedStats = stats;
        await _saveStatsData(stats);
        
        AppLogger.info('Fetched trading stats');
        return stats;
      }
      
      return _cachedStats;
    } catch (e) {
      AppLogger.error('Failed to get trading stats', e);
      return _cachedStats;
    }
  }

  // Create offer
  static Future<TradingOfferResult> createOffer(CreateOfferRequest request) async {
    try {
      AppLogger.info('Creating trading offer: ${request.type.name} ${request.cryptocurrency}');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.tradingCreateOffer,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final offerData = response.data!['data']['offer'] as Map<String, dynamic>;
        final offer = TradingOffer.fromJson(offerData);
        
        AppLogger.info('Offer created successfully: ${offer.id}');
        return TradingOfferResult.success(offer: offer);
      }
      
      return TradingOfferResult.failure('Failed to create offer');
    } catch (e) {
      AppLogger.error('Failed to create offer', e);
      return TradingOfferResult.failure(e.toString());
    }
  }

  // Accept trade
  static Future<TradeResult> acceptTrade(AcceptTradeRequest request) async {
    try {
      AppLogger.info('Accepting trade for offer: ${request.offerId}');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.tradingAcceptTrade,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final tradeData = response.data!['data']['trade'] as Map<String, dynamic>;
        final trade = Trade.fromJson(tradeData);
        
        AppLogger.info('Trade accepted successfully: ${trade.id}');
        return TradeResult.success(trade: trade);
      }
      
      return TradeResult.failure('Failed to accept trade');
    } catch (e) {
      AppLogger.error('Failed to accept trade', e);
      return TradeResult.failure(e.toString());
    }
  }

  // Fund escrow
  static Future<EscrowResult> fundEscrow(String tradeId) async {
    try {
      AppLogger.info('Funding escrow for trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingFundEscrow}/$tradeId',
      );

      if (response.success && response.data != null) {
        final escrowData = response.data!['data'] as Map<String, dynamic>;
        
        AppLogger.info('Escrow funded successfully: ${escrowData['txHash']}');
        return EscrowResult.success(
          escrowId: escrowData['escrowId'],
          txHash: escrowData['txHash'],
        );
      }
      
      return EscrowResult.failure('Failed to fund escrow');
    } catch (e) {
      AppLogger.error('Failed to fund escrow', e);
      return EscrowResult.failure(e.toString());
    }
  }

  // Mark payment as sent
  static Future<bool> markPaymentSent(String tradeId, String proofUrl) async {
    try {
      AppLogger.info('Marking payment as sent for trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingMarkPaymentSent}/$tradeId',
        data: {'proofUrl': proofUrl},
      );

      if (response.success) {
        AppLogger.info('Payment marked as sent for trade: $tradeId');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to mark payment as sent', e);
      return false;
    }
  }

  // Confirm payment received
  static Future<bool> confirmPaymentReceived(String tradeId) async {
    try {
      AppLogger.info('Confirming payment received for trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingConfirmPayment}/$tradeId',
      );

      if (response.success) {
        AppLogger.info('Payment confirmed for trade: $tradeId');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to confirm payment received', e);
      return false;
    }
  }

  // Cancel trade
  static Future<bool> cancelTrade(String tradeId, String reason) async {
    try {
      AppLogger.info('Cancelling trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingCancelTrade}/$tradeId',
        data: {'reason': reason},
      );

      if (response.success) {
        AppLogger.info('Trade cancelled: $tradeId');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to cancel trade', e);
      return false;
    }
  }

  // Initiate dispute
  static Future<bool> initiateDispute(String tradeId, String reason) async {
    try {
      AppLogger.info('Initiating dispute for trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingInitiateDispute}/$tradeId',
        data: {'reason': reason},
      );

      if (response.success) {
        AppLogger.info('Dispute initiated for trade: $tradeId');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to initiate dispute', e);
      return false;
    }
  }

  // Send trade message
  static Future<bool> sendTradeMessage(String tradeId, String message) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingSendMessage}/$tradeId',
        data: {'message': message},
      );

      return response.success;
    } catch (e) {
      AppLogger.error('Failed to send trade message', e);
      return false;
    }
  }

  // Get trade details
  static Future<Trade?> getTradeDetails(String tradeId) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId',
      );

      if (response.success && response.data != null) {
        final tradeData = response.data!['data']['trade'] as Map<String, dynamic>;
        return Trade.fromJson(tradeData);
      }
      
      return null;
    } catch (e) {
      AppLogger.error('Failed to get trade details', e);
      return null;
    }
  }

  // Get offer details
  static Future<TradingOffer?> getOfferDetails(String offerId) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingOfferDetails}/$offerId',
      );

      if (response.success && response.data != null) {
        final offerData = response.data!['data']['offer'] as Map<String, dynamic>;
        return TradingOffer.fromJson(offerData);
      }
      
      return null;
    } catch (e) {
      AppLogger.error('Failed to get offer details', e);
      return null;
    }
  }

  // Private helper methods
  static Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final offersJson = prefs.getString(_offersKey);
      if (offersJson != null) {
        final offersData = json.decode(offersJson) as List;
        _cachedOffers = offersData
            .map((o) => TradingOffer.fromJson(o as Map<String, dynamic>))
            .toList();
      }
      
      final tradesJson = prefs.getString(_tradesKey);
      if (tradesJson != null) {
        final tradesData = json.decode(tradesJson) as List;
        _cachedTrades = tradesData
            .map((t) => Trade.fromJson(t as Map<String, dynamic>))
            .toList();
      }
      
      final statsJson = prefs.getString(_statsKey);
      if (statsJson != null) {
        final statsData = json.decode(statsJson) as Map<String, dynamic>;
        _cachedStats = TradingStats.fromJson(statsData);
      }
    } catch (e) {
      AppLogger.error('Failed to load cached trading data', e);
    }
  }

  static Future<void> _saveOffersData(List<TradingOffer> offers) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final offersJson = json.encode(offers.map((o) => o.toJson()).toList());
      await prefs.setString(_offersKey, offersJson);
    } catch (e) {
      AppLogger.error('Failed to save offers data', e);
    }
  }

  static Future<void> _saveTradesData(List<Trade> trades) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tradesJson = json.encode(trades.map((t) => t.toJson()).toList());
      await prefs.setString(_tradesKey, tradesJson);
    } catch (e) {
      AppLogger.error('Failed to save trades data', e);
    }
  }

  static Future<void> _saveStatsData(TradingStats stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_statsKey, json.encode(stats.toJson()));
    } catch (e) {
      AppLogger.error('Failed to save stats data', e);
    }
  }

  // Getters
  static List<TradingOffer> get cachedOffers => _cachedOffers;
  static List<Trade> get cachedTrades => _cachedTrades;
  static TradingStats? get cachedStats => _cachedStats;
}

// Result classes
class TradingOfferResult {
  final bool success;
  final TradingOffer? offer;
  final String? error;

  const TradingOfferResult._({
    required this.success,
    this.offer,
    this.error,
  });

  factory TradingOfferResult.success({required TradingOffer offer}) {
    return TradingOfferResult._(success: true, offer: offer);
  }

  factory TradingOfferResult.failure(String error) {
    return TradingOfferResult._(success: false, error: error);
  }
}

class TradeResult {
  final bool success;
  final Trade? trade;
  final String? error;

  const TradeResult._({
    required this.success,
    this.trade,
    this.error,
  });

  factory TradeResult.success({required Trade trade}) {
    return TradeResult._(success: true, trade: trade);
  }

  factory TradeResult.failure(String error) {
    return TradeResult._(success: false, error: error);
  }
}

class EscrowResult {
  final bool success;
  final String? escrowId;
  final String? txHash;
  final String? error;

  const EscrowResult._({
    required this.success,
    this.escrowId,
    this.txHash,
    this.error,
  });

  factory EscrowResult.success({
    required String escrowId,
    required String txHash,
  }) {
    return EscrowResult._(
      success: true,
      escrowId: escrowId,
      txHash: txHash,
    );
  }

  factory EscrowResult.failure(String error) {
    return EscrowResult._(success: false, error: error);
  }
}
