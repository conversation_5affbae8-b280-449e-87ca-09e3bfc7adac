import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/api_service.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/utils/logger.dart';
import '../models/trading_models.dart';
import '../providers/chat_provider.dart';

class ChatService {
  static const String _messagesKey = 'chat_messages_';
  static const String _tradesKey = 'chat_trades_';
  
  static Map<String, List<ChatMessage>> _cachedMessages = {};
  static Map<String, Trade> _cachedTrades = {};
  static bool _isInitialized = false;

  // Initialize the chat service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadCachedData();
    _isInitialized = true;
    
    AppLogger.info('ChatService initialized');
  }

  // Get trade details
  static Future<Trade?> getTradeDetails(String tradeId) async {
    try {
      AppLogger.info('Fetching trade details for: $tradeId');
      
      // Check cache first
      if (_cachedTrades.containsKey(tradeId)) {
        return _cachedTrades[tradeId];
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId',
      );

      if (response.success && response.data != null) {
        final tradeData = response.data!['data']['trade'] as Map<String, dynamic>;
        final trade = Trade.fromJson(tradeData);
        
        _cachedTrades[tradeId] = trade;
        await _saveTradeData(tradeId, trade);
        
        AppLogger.info('Fetched trade details for: $tradeId');
        return trade;
      }
      
      return _cachedTrades[tradeId];
    } catch (e) {
      AppLogger.error('Failed to get trade details', e);
      return _cachedTrades[tradeId];
    }
  }

  // Get chat messages for a trade
  static Future<List<ChatMessage>> getChatMessages(
    String tradeId, {
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      AppLogger.info('Fetching chat messages for trade: $tradeId');
      
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId/messages?limit=$limit&offset=$offset',
      );

      if (response.success && response.data != null) {
        final messagesData = response.data!['data']['messages'] as List;
        final messages = messagesData
            .map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
            .toList();
        
        // Sort by timestamp
        messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
        
        if (offset == 0) {
          // Replace cache for initial load
          _cachedMessages[tradeId] = messages;
        } else {
          // Merge with existing cache for pagination
          final existing = _cachedMessages[tradeId] ?? [];
          final merged = [...messages, ...existing];
          merged.sort((a, b) => a.timestamp.compareTo(b.timestamp));
          _cachedMessages[tradeId] = merged;
        }
        
        await _saveMessagesData(tradeId, _cachedMessages[tradeId]!);
        
        AppLogger.info('Fetched ${messages.length} messages for trade: $tradeId');
        return messages;
      }
      
      return _cachedMessages[tradeId] ?? [];
    } catch (e) {
      AppLogger.error('Failed to get chat messages', e);
      return _cachedMessages[tradeId] ?? [];
    }
  }

  // Send message
  static Future<bool> sendMessage({
    required String tradeId,
    required String message,
    String type = 'text',
    String? attachmentUrl,
  }) async {
    try {
      AppLogger.info('Sending message to trade: $tradeId');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingSendMessage}/$tradeId',
        data: {
          'message': message,
          'type': type,
          'attachmentUrl': attachmentUrl,
        },
      );

      if (response.success) {
        AppLogger.info('Message sent successfully to trade: $tradeId');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to send message', e);
      return false;
    }
  }

  // Mark messages as read
  static Future<bool> markMessagesAsRead(String tradeId, List<String> messageIds) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId/mark-read',
        data: {'messageIds': messageIds},
      );

      if (response.success) {
        // Update local cache
        final messages = _cachedMessages[tradeId];
        if (messages != null) {
          final updatedMessages = messages.map((message) {
            if (messageIds.contains(message.id)) {
              return message.copyWith(isRead: true);
            }
            return message;
          }).toList();
          
          _cachedMessages[tradeId] = updatedMessages;
          await _saveMessagesData(tradeId, updatedMessages);
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to mark messages as read', e);
      return false;
    }
  }

  // Upload attachment
  static Future<String?> uploadAttachment({
    required String tradeId,
    required String filePath,
    required String fileName,
    required String mimeType,
  }) async {
    try {
      AppLogger.info('Uploading attachment for trade: $tradeId');
      
      // This would implement file upload to your storage service
      // For now, return a placeholder URL
      final attachmentUrl = 'https://example.com/uploads/$fileName';
      
      AppLogger.info('Attachment uploaded: $attachmentUrl');
      return attachmentUrl;
    } catch (e) {
      AppLogger.error('Failed to upload attachment', e);
      return null;
    }
  }

  // Get unread message count for a trade
  static Future<int> getUnreadCount(String tradeId) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId/unread-count',
      );

      if (response.success && response.data != null) {
        return response.data!['data']['unreadCount'] as int;
      }
      
      return 0;
    } catch (e) {
      AppLogger.error('Failed to get unread count', e);
      return 0;
    }
  }

  // Get all unread counts for user's trades
  static Future<Map<String, int>> getAllUnreadCounts() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingActiveTrades}/unread-counts',
      );

      if (response.success && response.data != null) {
        final countsData = response.data!['data']['unreadCounts'] as Map<String, dynamic>;
        return countsData.map((key, value) => MapEntry(key, value as int));
      }
      
      return {};
    } catch (e) {
      AppLogger.error('Failed to get all unread counts', e);
      return {};
    }
  }

  // Search messages in a trade
  static Future<List<ChatMessage>> searchMessages({
    required String tradeId,
    required String query,
    int limit = 20,
  }) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.tradingTradeDetails}/$tradeId/messages/search?q=$query&limit=$limit',
      );

      if (response.success && response.data != null) {
        final messagesData = response.data!['data']['messages'] as List;
        return messagesData
            .map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
            .toList();
      }
      
      return [];
    } catch (e) {
      AppLogger.error('Failed to search messages', e);
      return [];
    }
  }

  // Private helper methods
  static Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      // Load cached messages
      for (final key in keys) {
        if (key.startsWith(_messagesKey)) {
          final tradeId = key.substring(_messagesKey.length);
          final messagesJson = prefs.getString(key);
          
          if (messagesJson != null) {
            final messagesData = json.decode(messagesJson) as List;
            _cachedMessages[tradeId] = messagesData
                .map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
                .toList();
          }
        }
        
        // Load cached trades
        if (key.startsWith(_tradesKey)) {
          final tradeId = key.substring(_tradesKey.length);
          final tradeJson = prefs.getString(key);
          
          if (tradeJson != null) {
            final tradeData = json.decode(tradeJson) as Map<String, dynamic>;
            _cachedTrades[tradeId] = Trade.fromJson(tradeData);
          }
        }
      }
    } catch (e) {
      AppLogger.error('Failed to load cached chat data', e);
    }
  }

  static Future<void> _saveMessagesData(String tradeId, List<ChatMessage> messages) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = json.encode(
        messages.map((m) => {
          'id': m.id,
          'senderId': m.senderId,
          'senderUsername': m.senderUsername,
          'message': m.message,
          'type': m.type,
          'timestamp': m.timestamp.toIso8601String(),
          'isRead': m.isRead,
          'attachmentUrl': m.attachmentUrl,
        }).toList(),
      );
      await prefs.setString('$_messagesKey$tradeId', messagesJson);
    } catch (e) {
      AppLogger.error('Failed to save messages data', e);
    }
  }

  static Future<void> _saveTradeData(String tradeId, Trade trade) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('$_tradesKey$tradeId', json.encode(trade.toJson()));
    } catch (e) {
      AppLogger.error('Failed to save trade data', e);
    }
  }

  // Clear cache for a specific trade
  static Future<void> clearTradeCache(String tradeId) async {
    try {
      _cachedMessages.remove(tradeId);
      _cachedTrades.remove(tradeId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_messagesKey$tradeId');
      await prefs.remove('$_tradesKey$tradeId');
    } catch (e) {
      AppLogger.error('Failed to clear trade cache', e);
    }
  }

  // Clear all cache
  static Future<void> clearAllCache() async {
    try {
      _cachedMessages.clear();
      _cachedTrades.clear();
      
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (final key in keys) {
        if (key.startsWith(_messagesKey) || key.startsWith(_tradesKey)) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      AppLogger.error('Failed to clear all cache', e);
    }
  }

  // Getters
  static List<ChatMessage> getCachedMessages(String tradeId) {
    return _cachedMessages[tradeId] ?? [];
  }

  static Trade? getCachedTrade(String tradeId) {
    return _cachedTrades[tradeId];
  }

  static bool get isInitialized => _isInitialized;
}
