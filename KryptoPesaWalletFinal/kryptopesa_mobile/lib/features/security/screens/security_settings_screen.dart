import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/services/biometric_service.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_overlay.dart';
import '../providers/security_provider.dart';
import '../widgets/security_setting_tile.dart';
import '../widgets/biometric_setup_dialog.dart';
import '../widgets/pin_setup_dialog.dart';
import '../widgets/security_info_card.dart';

class SecuritySettingsScreen extends ConsumerStatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  ConsumerState<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends ConsumerState<SecuritySettingsScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSecuritySettings();
    });
  }

  Future<void> _loadSecuritySettings() async {
    setState(() => _isLoading = true);
    
    try {
      final securityNotifier = ref.read(securityProvider.notifier);
      await securityNotifier.loadSecuritySettings();
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final securityState = ref.watch(securityProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: const CustomAppBar(
        title: 'Security Settings',
        subtitle: 'Protect your wallet',
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: RefreshIndicator(
          onRefresh: _loadSecuritySettings,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Security overview card
              SecurityInfoCard(
                securityLevel: _calculateSecurityLevel(securityState),
                recommendations: _getSecurityRecommendations(securityState),
              ),

              const SizedBox(height: 24),

              // Authentication section
              _buildSectionHeader(context, 'Authentication', Icons.security),
              const SizedBox(height: 12),

              // PIN Code setting
              SecuritySettingTile(
                title: 'PIN Code',
                subtitle: securityState.isPinEnabled 
                    ? 'PIN code is enabled' 
                    : 'Set up a PIN code for quick access',
                icon: Icons.pin,
                isEnabled: securityState.isPinEnabled,
                onTap: () => _handlePinSetting(securityState.isPinEnabled),
                trailing: securityState.isPinEnabled 
                    ? TextButton(
                        onPressed: () => _showChangePinDialog(),
                        child: Text('Change'),
                      )
                    : null,
              ),

              const SizedBox(height: 8),

              // Biometric authentication setting
              if (securityState.biometricCapability.isAvailable)
                SecuritySettingTile(
                  title: securityState.biometricCapability.primaryBiometricType,
                  subtitle: securityState.isBiometricEnabled
                      ? '${securityState.biometricCapability.description} is enabled'
                      : 'Enable ${securityState.biometricCapability.description.toLowerCase()} for quick access',
                  icon: _getBiometricIcon(securityState.biometricCapability),
                  isEnabled: securityState.isBiometricEnabled,
                  onTap: () => _handleBiometricSetting(securityState.isBiometricEnabled),
                ),

              const SizedBox(height: 24),

              // App Security section
              _buildSectionHeader(context, 'App Security', Icons.shield),
              const SizedBox(height: 12),

              // Auto-lock setting
              SecuritySettingTile(
                title: 'Auto-lock',
                subtitle: 'Lock app after ${securityState.autoLockDuration} minutes of inactivity',
                icon: Icons.lock_clock,
                isEnabled: securityState.isAutoLockEnabled,
                onTap: () => _showAutoLockDialog(),
                trailing: Icon(Icons.chevron_right),
              ),

              const SizedBox(height: 8),

              // Screenshot protection
              SecuritySettingTile(
                title: 'Screenshot Protection',
                subtitle: securityState.isScreenshotProtectionEnabled
                    ? 'Screenshots are blocked'
                    : 'Allow screenshots of sensitive screens',
                icon: Icons.screenshot_monitor,
                isEnabled: securityState.isScreenshotProtectionEnabled,
                onTap: () => _toggleScreenshotProtection(),
              ),

              const SizedBox(height: 8),

              // App lock on background
              SecuritySettingTile(
                title: 'Lock on Background',
                subtitle: securityState.isLockOnBackgroundEnabled
                    ? 'App locks when moved to background'
                    : 'Keep app unlocked in background',
                icon: Icons.visibility_off,
                isEnabled: securityState.isLockOnBackgroundEnabled,
                onTap: () => _toggleLockOnBackground(),
              ),

              const SizedBox(height: 24),

              // Wallet Security section
              _buildSectionHeader(context, 'Wallet Security', Icons.account_balance_wallet),
              const SizedBox(height: 12),

              // Transaction confirmation
              SecuritySettingTile(
                title: 'Transaction Confirmation',
                subtitle: 'Require authentication for transactions above ${securityState.transactionLimit} USD',
                icon: Icons.verified_user,
                isEnabled: true,
                onTap: () => _showTransactionLimitDialog(),
                trailing: Icon(Icons.chevron_right),
              ),

              const SizedBox(height: 8),

              // Backup reminder
              SecuritySettingTile(
                title: 'Backup Reminder',
                subtitle: securityState.isBackupCompleted
                    ? 'Wallet backup completed'
                    : 'Backup your wallet seed phrase',
                icon: Icons.backup,
                isEnabled: securityState.isBackupCompleted,
                onTap: () => context.push('/wallet/backup'),
                trailing: securityState.isBackupCompleted 
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : const Icon(Icons.warning, color: Colors.orange),
              ),

              const SizedBox(height: 24),

              // Advanced section
              _buildSectionHeader(context, 'Advanced', Icons.settings),
              const SizedBox(height: 12),

              // Security audit
              SecuritySettingTile(
                title: 'Security Audit',
                subtitle: 'Review security settings and recommendations',
                icon: Icons.security,
                isEnabled: false,
                onTap: () => _showSecurityAudit(),
                trailing: const Icon(Icons.chevron_right),
              ),

              const SizedBox(height: 8),

              // Reset security settings
              SecuritySettingTile(
                title: 'Reset Security Settings',
                subtitle: 'Reset all security settings to default',
                icon: Icons.restore,
                isEnabled: false,
                onTap: () => _showResetSecurityDialog(),
                trailing: const Icon(Icons.chevron_right),
                isDestructive: true,
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  IconData _getBiometricIcon(BiometricCapability capability) {
    if (capability.hasFaceId) return Icons.face;
    if (capability.hasFingerprint) return Icons.fingerprint;
    if (capability.hasIris) return Icons.visibility;
    return Icons.security;
  }

  int _calculateSecurityLevel(SecurityState state) {
    int level = 0;
    
    if (state.isPinEnabled) level += 20;
    if (state.isBiometricEnabled) level += 25;
    if (state.isAutoLockEnabled) level += 15;
    if (state.isScreenshotProtectionEnabled) level += 10;
    if (state.isLockOnBackgroundEnabled) level += 10;
    if (state.isBackupCompleted) level += 20;
    
    return level;
  }

  List<String> _getSecurityRecommendations(SecurityState state) {
    final recommendations = <String>[];
    
    if (!state.isPinEnabled) {
      recommendations.add('Set up a PIN code for quick access');
    }
    
    if (state.biometricCapability.isAvailable && !state.isBiometricEnabled) {
      recommendations.add('Enable ${state.biometricCapability.primaryBiometricType.toLowerCase()} authentication');
    }
    
    if (!state.isBackupCompleted) {
      recommendations.add('Backup your wallet seed phrase');
    }
    
    if (!state.isAutoLockEnabled) {
      recommendations.add('Enable auto-lock for better security');
    }
    
    if (!state.isScreenshotProtectionEnabled) {
      recommendations.add('Enable screenshot protection');
    }
    
    return recommendations;
  }

  Future<void> _handlePinSetting(bool isEnabled) async {
    if (isEnabled) {
      _showChangePinDialog();
    } else {
      _showSetupPinDialog();
    }
  }

  Future<void> _handleBiometricSetting(bool isEnabled) async {
    if (isEnabled) {
      await _disableBiometric();
    } else {
      _showBiometricSetupDialog();
    }
  }

  void _showSetupPinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PinSetupDialog(
        onPinSet: (pin) async {
          final success = await SecureStorageService.instance.storePinHash(pin);
          if (success) {
            final securityNotifier = ref.read(securityProvider.notifier);
            await securityNotifier.loadSecuritySettings();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('PIN code set successfully')),
              );
            }
          }
        },
      ),
    );
  }

  void _showChangePinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PinSetupDialog(
        isChanging: true,
        onPinSet: (pin) async {
          final success = await SecureStorageService.instance.storePinHash(pin);
          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('PIN code changed successfully')),
            );
          }
        },
      ),
    );
  }

  void _showBiometricSetupDialog() {
    showDialog(
      context: context,
      builder: (context) => BiometricSetupDialog(
        onBiometricEnabled: () async {
          final securityNotifier = ref.read(securityProvider.notifier);
          await securityNotifier.loadSecuritySettings();
        },
      ),
    );
  }

  Future<void> _disableBiometric() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Disable Biometric Authentication'),
        content: const Text('Are you sure you want to disable biometric authentication?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Disable'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await BiometricService.instance.setBiometricEnabled(false);
      final securityNotifier = ref.read(securityProvider.notifier);
      await securityNotifier.loadSecuritySettings();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Biometric authentication disabled')),
        );
      }
    }
  }

  void _showAutoLockDialog() {
    // TODO: Implement auto-lock duration selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Auto-lock settings coming soon')),
    );
  }

  Future<void> _toggleScreenshotProtection() async {
    final securityNotifier = ref.read(securityProvider.notifier);
    await securityNotifier.toggleScreenshotProtection();
  }

  Future<void> _toggleLockOnBackground() async {
    final securityNotifier = ref.read(securityProvider.notifier);
    await securityNotifier.toggleLockOnBackground();
  }

  void _showTransactionLimitDialog() {
    // TODO: Implement transaction limit setting
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Transaction limit settings coming soon')),
    );
  }

  void _showSecurityAudit() {
    // TODO: Implement security audit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Security audit coming soon')),
    );
  }

  void _showResetSecurityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Security Settings'),
        content: const Text(
          'This will reset all security settings to default values. '
          'You will need to set up authentication again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              await _resetSecuritySettings();
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetSecuritySettings() async {
    try {
      setState(() => _isLoading = true);
      
      final securityNotifier = ref.read(securityProvider.notifier);
      await securityNotifier.resetSecuritySettings();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Security settings reset successfully')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
