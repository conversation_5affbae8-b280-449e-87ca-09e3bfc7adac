import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/services/biometric_service.dart';
import '../../../core/services/secure_storage_service.dart';
import '../providers/security_provider.dart';
import '../widgets/pin_input_widget.dart';

class AppLockScreen extends ConsumerStatefulWidget {
  final VoidCallback onUnlocked;
  final String? reason;

  const AppLockScreen({
    super.key,
    required this.onUnlocked,
    this.reason,
  });

  @override
  ConsumerState<AppLockScreen> createState() => _AppLockScreenState();
}

class _AppLockScreenState extends ConsumerState<AppLockScreen>
    with TickerProviderStateMixin {
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isAuthenticating = false;
  int _failedAttempts = 0;
  static const int _maxFailedAttempts = 5;

  @override
  void initState() {
    super.initState();
    
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );

    _fadeController.forward();
    
    // Auto-trigger biometric if available and enabled
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _tryBiometricAuth();
    });
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final securityState = ref.watch(securityProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                const Spacer(),
                
                // App logo and title
                Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.primary.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.account_balance_wallet,
                        color: theme.colorScheme.onPrimary,
                        size: 40,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    Text(
                      'KryptoPesa',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      widget.reason ?? 'Enter your PIN to unlock',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                
                const SizedBox(height: 48),
                
                // PIN input or biometric prompt
                if (securityState.isPinEnabled)
                  AnimatedBuilder(
                    animation: _shakeAnimation,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                          _shakeAnimation.value * 10 * 
                          ((_shakeController.status == AnimationStatus.forward) ? 1 : -1),
                          0,
                        ),
                        child: PinInputWidget(
                          onPinEntered: _verifyPin,
                          isLoading: _isAuthenticating,
                          failedAttempts: _failedAttempts,
                          maxAttempts: _maxFailedAttempts,
                        ),
                      );
                    },
                  ),
                
                const SizedBox(height: 32),
                
                // Biometric authentication button
                if (securityState.isBiometricEnabled)
                  Column(
                    children: [
                      Container(
                        width: 1,
                        height: 20,
                        color: theme.colorScheme.outline.withOpacity(0.3),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      Text(
                        'or',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      FilledButton.icon(
                        onPressed: _isAuthenticating ? null : _tryBiometricAuth,
                        icon: Icon(_getBiometricIcon(securityState.biometricCapability)),
                        label: Text('Use ${securityState.biometricCapability.primaryBiometricType}'),
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                
                const Spacer(),
                
                // Failed attempts warning
                if (_failedAttempts > 0)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.red.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning,
                          color: Colors.red,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _failedAttempts >= _maxFailedAttempts
                                ? 'Too many failed attempts. Please try again later.'
                                : 'Failed attempts: $_failedAttempts/$_maxFailedAttempts',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                
                const SizedBox(height: 16),
                
                // Emergency access (if too many failed attempts)
                if (_failedAttempts >= _maxFailedAttempts)
                  TextButton(
                    onPressed: _showEmergencyAccess,
                    child: Text(
                      'Emergency Access',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getBiometricIcon(BiometricCapability capability) {
    if (capability.hasFaceId) return Icons.face;
    if (capability.hasFingerprint) return Icons.fingerprint;
    if (capability.hasIris) return Icons.visibility;
    return Icons.security;
  }

  Future<void> _tryBiometricAuth() async {
    final securityState = ref.read(securityProvider);
    
    if (!securityState.isBiometricEnabled || _isAuthenticating) return;

    setState(() => _isAuthenticating = true);

    try {
      final result = await BiometricService.instance.authenticate(
        reason: widget.reason ?? 'Unlock KryptoPesa',
        useErrorDialogs: false,
      );

      if (result.isSuccess) {
        _onAuthenticationSuccess();
      } else {
        // Don't count biometric failures as PIN failures
        setState(() => _isAuthenticating = false);
      }
    } catch (e) {
      setState(() => _isAuthenticating = false);
    }
  }

  Future<void> _verifyPin(String pin) async {
    if (_isAuthenticating || _failedAttempts >= _maxFailedAttempts) return;

    setState(() => _isAuthenticating = true);

    try {
      final isValid = await SecureStorageService.instance.verifyPin(pin);
      
      if (isValid) {
        _onAuthenticationSuccess();
      } else {
        _onAuthenticationFailed();
      }
    } catch (e) {
      _onAuthenticationFailed();
    }
  }

  void _onAuthenticationSuccess() {
    // Reset failed attempts
    _failedAttempts = 0;
    
    // Call the unlock callback
    widget.onUnlocked();
  }

  void _onAuthenticationFailed() {
    setState(() {
      _isAuthenticating = false;
      _failedAttempts++;
    });

    // Trigger shake animation
    _shakeController.forward().then((_) {
      _shakeController.reverse();
    });

    // Show error message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _failedAttempts >= _maxFailedAttempts
              ? 'Too many failed attempts. Please try again later.'
              : 'Incorrect PIN. Try again.',
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showEmergencyAccess() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Emergency Access'),
        content: Text(
          'If you\'ve forgotten your PIN, you can reset the app. '
          'This will delete all local data and you\'ll need to restore '
          'your wallet using your seed phrase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              _showResetConfirmation();
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Reset App'),
          ),
        ],
      ),
    );
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset App'),
        content: Text(
          'Are you sure you want to reset the app? This action cannot be undone. '
          'Make sure you have your wallet seed phrase to restore your wallet.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              await _resetApp();
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text('Reset'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetApp() async {
    try {
      // Clear all secure storage
      await SecureStorageService.instance.clearAllData();
      
      // Reset security settings
      final securityNotifier = ref.read(securityProvider.notifier);
      await securityNotifier.resetSecuritySettings();
      
      // Navigate to onboarding or initial setup
      if (mounted) {
        // TODO: Navigate to onboarding screen
        widget.onUnlocked(); // Temporary - should go to onboarding
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reset app: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
