import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import '../../../core/services/biometric_service.dart';

class BiometricSetupDialog extends StatefulWidget {
  final VoidCallback? onBiometricEnabled;

  const BiometricSetupDialog({
    super.key,
    this.onBiometricEnabled,
  });

  @override
  State<BiometricSetupDialog> createState() => _BiometricSetupDialogState();
}

class _BiometricSetupDialogState extends State<BiometricSetupDialog> {
  bool _isLoading = false;
  BiometricCapability? _capability;

  @override
  void initState() {
    super.initState();
    _loadBiometricCapability();
  }

  Future<void> _loadBiometricCapability() async {
    final capability = await BiometricService.instance.getBiometricCapability();
    if (mounted) {
      setState(() {
        _capability = capability;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_capability == null) {
      return AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('Checking biometric availability...'),
          ],
        ),
      );
    }

    if (!_capability!.isAvailable) {
      return AlertDialog(
        title: Text('Biometric Not Available'),
        content: Text(
          'Biometric authentication is not available on this device. '
          'Please ensure you have set up fingerprint or face recognition in your device settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      );
    }

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            _getBiometricIcon(_capability!),
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text('Enable ${_capability!.primaryBiometricType}'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Enable ${_capability!.description.toLowerCase()} authentication for quick and secure access to your wallet.',
            style: theme.textTheme.bodyMedium,
          ),
          
          const SizedBox(height: 16),
          
          // Benefits list
          _buildBenefitItem(
            context,
            Icons.speed,
            'Quick Access',
            'Unlock your wallet instantly',
          ),
          
          const SizedBox(height: 8),
          
          _buildBenefitItem(
            context,
            Icons.security,
            'Enhanced Security',
            'Your biometric data never leaves your device',
          ),
          
          const SizedBox(height: 8),
          
          _buildBenefitItem(
            context,
            Icons.privacy_tip,
            'Privacy Protection',
            'No passwords to remember or type',
          ),
          
          const SizedBox(height: 16),
          
          // Platform info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.outline,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Platform: ${_capability!.platformSupport}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading ? null : _enableBiometric,
          child: _isLoading
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                )
              : Text('Enable'),
        ),
      ],
    );
  }

  Widget _buildBenefitItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.outline,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getBiometricIcon(BiometricCapability capability) {
    if (capability.hasFaceId) return Icons.face;
    if (capability.hasFingerprint) return Icons.fingerprint;
    if (capability.hasIris) return Icons.visibility;
    return Icons.security;
  }

  Future<void> _enableBiometric() async {
    setState(() => _isLoading = true);

    try {
      final result = await BiometricService.instance.authenticate(
        reason: 'Enable biometric authentication for KryptoPesa',
      );

      if (result.isSuccess) {
        await BiometricService.instance.setBiometricEnabled(true);
        
        if (mounted) {
          Navigator.pop(context);
          widget.onBiometricEnabled?.call();
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${_capability!.primaryBiometricType} authentication enabled'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          String errorMessage = 'Failed to enable biometric authentication';
          
          if (result.error != null) {
            errorMessage = result.error!;
          } else if (!result.isAvailable) {
            errorMessage = 'Biometric authentication is not available';
          } else if (!result.isEnabled) {
            errorMessage = 'Biometric authentication is disabled in settings';
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
