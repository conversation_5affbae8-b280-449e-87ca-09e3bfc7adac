import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PinInputWidget extends StatefulWidget {
  final Function(String) onPinEntered;
  final bool isLoading;
  final int failedAttempts;
  final int maxAttempts;

  const PinInputWidget({
    super.key,
    required this.onPinEntered,
    this.isLoading = false,
    this.failedAttempts = 0,
    this.maxAttempts = 5,
  });

  @override
  State<PinInputWidget> createState() => _PinInputWidgetState();
}

class _PinInputWidgetState extends State<PinInputWidget> {
  final List<String> _pin = List.filled(6, '');
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDisabled = widget.failedAttempts >= widget.maxAttempts;

    return Column(
      children: [
        // PIN dots display
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(6, (index) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _pin[index].isNotEmpty
                      ? theme.colorScheme.primary
                      : Colors.transparent,
                  border: Border.all(
                    color: _pin[index].isNotEmpty
                        ? theme.colorScheme.primary
                        : theme.colorScheme.outline.withOpacity(0.5),
                    width: 2,
                  ),
                ),
              ),
            );
          }),
        ),

        const SizedBox(height: 48),

        // Number pad
        if (!isDisabled)
          _buildNumberPad(theme),

        // Loading indicator
        if (widget.isLoading)
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildNumberPad(ThemeData theme) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 300),
      child: Column(
        children: [
          // Numbers 1-3
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNumberButton('1', theme),
              _buildNumberButton('2', theme),
              _buildNumberButton('3', theme),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Numbers 4-6
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNumberButton('4', theme),
              _buildNumberButton('5', theme),
              _buildNumberButton('6', theme),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Numbers 7-9
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNumberButton('7', theme),
              _buildNumberButton('8', theme),
              _buildNumberButton('9', theme),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 0 and backspace
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                icon: Icons.backspace_outlined,
                onPressed: _onBackspace,
                theme: theme,
              ),
              _buildNumberButton('0', theme),
              _buildActionButton(
                icon: Icons.check,
                onPressed: _currentIndex == 6 ? _onSubmit : null,
                theme: theme,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNumberButton(String number, ThemeData theme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.isLoading ? null : () => _onNumberPressed(number),
        borderRadius: BorderRadius.circular(35),
        child: Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.3),
            ),
          ),
          child: Center(
            child: Text(
              number,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required ThemeData theme,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(35),
        child: Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: onPressed != null
                ? theme.colorScheme.primary.withOpacity(0.1)
                : Colors.transparent,
          ),
          child: Center(
            child: Icon(
              icon,
              color: onPressed != null
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withOpacity(0.5),
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  void _onNumberPressed(String number) {
    if (_currentIndex < 6) {
      setState(() {
        _pin[_currentIndex] = number;
        _currentIndex++;
      });

      // Haptic feedback
      HapticFeedback.lightImpact();

      // Auto-submit when all digits are entered
      if (_currentIndex == 6) {
        _onSubmit();
      }
    }
  }

  void _onBackspace() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _pin[_currentIndex] = '';
      });

      // Haptic feedback
      HapticFeedback.lightImpact();
    }
  }

  void _onSubmit() {
    if (_currentIndex == 6) {
      final pinString = _pin.join();
      widget.onPinEntered(pinString);
      
      // Clear PIN after submission
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _clearPin();
        }
      });
    }
  }

  void _clearPin() {
    setState(() {
      _pin.fillRange(0, 6, '');
      _currentIndex = 0;
    });
  }
}
