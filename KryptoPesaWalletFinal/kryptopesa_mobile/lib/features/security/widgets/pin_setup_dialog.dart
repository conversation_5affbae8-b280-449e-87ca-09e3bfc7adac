import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PinSetupDialog extends StatefulWidget {
  final Function(String) onPinSet;
  final bool isChanging;

  const PinSetupDialog({
    super.key,
    required this.onPinSet,
    this.isChanging = false,
  });

  @override
  State<PinSetupDialog> createState() => _PinSetupDialogState();
}

class _PinSetupDialogState extends State<PinSetupDialog> {
  final List<TextEditingController> _controllers = List.generate(
    6,
    (index) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(
    6,
    (index) => FocusNode(),
  );

  String _pin = '';
  String _confirmPin = '';
  bool _isConfirming = false;
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Text(
        _isConfirming 
            ? 'Confirm PIN' 
            : widget.isChanging 
                ? 'Change PIN' 
                : 'Set PIN',
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _isConfirming
                ? 'Please confirm your PIN'
                : 'Enter a 6-digit PIN for quick access',
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // PIN input fields
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(6, (index) => _buildPinField(index)),
          ),
          
          if (_error != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _error!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Security tips
          if (!_isConfirming)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.tips_and_updates,
                        size: 16,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Security Tips',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• Use a unique PIN that\'s not used elsewhere\n'
                    '• Avoid obvious patterns like 123456\n'
                    '• Don\'t share your PIN with anyone',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading || _getCurrentPin().length != 6 
              ? null 
              : _handlePinSubmit,
          child: _isLoading
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.onPrimary,
                    ),
                  ),
                )
              : Text(_isConfirming ? 'Confirm' : 'Set PIN'),
        ),
      ],
    );
  }

  Widget _buildPinField(int index) {
    final theme = Theme.of(context);
    
    return Container(
      width: 40,
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(
          color: _focusNodes[index].hasFocus
              ? theme.colorScheme.primary
              : theme.colorScheme.outline.withOpacity(0.3),
          width: _focusNodes[index].hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        obscureText: true,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          counterText: '',
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onPinDigitChanged(index, value),
      ),
    );
  }

  void _onPinDigitChanged(int index, String value) {
    setState(() {
      _error = null;
    });

    if (value.isNotEmpty) {
      // Move to next field
      if (index < 5) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, remove focus
        _focusNodes[index].unfocus();
      }
    } else {
      // Move to previous field if current is empty
      if (index > 0) {
        _focusNodes[index - 1].requestFocus();
      }
    }
  }

  String _getCurrentPin() {
    return _controllers.map((controller) => controller.text).join();
  }

  void _clearPin() {
    for (final controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
  }

  Future<void> _handlePinSubmit() async {
    final currentPin = _getCurrentPin();
    
    if (currentPin.length != 6) {
      setState(() {
        _error = 'PIN must be 6 digits';
      });
      return;
    }

    if (!_isConfirming) {
      // First PIN entry
      _pin = currentPin;
      setState(() {
        _isConfirming = true;
        _error = null;
      });
      _clearPin();
    } else {
      // Confirming PIN
      _confirmPin = currentPin;
      
      if (_pin != _confirmPin) {
        setState(() {
          _error = 'PINs do not match. Please try again.';
          _isConfirming = false;
        });
        _clearPin();
        return;
      }

      // PINs match, set the PIN
      setState(() => _isLoading = true);

      try {
        await Future.delayed(const Duration(milliseconds: 500)); // Simulate processing
        widget.onPinSet(_pin);
        
        if (mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        setState(() {
          _error = 'Failed to set PIN. Please try again.';
          _isLoading = false;
          _isConfirming = false;
        });
        _clearPin();
      }
    }
  }
}
