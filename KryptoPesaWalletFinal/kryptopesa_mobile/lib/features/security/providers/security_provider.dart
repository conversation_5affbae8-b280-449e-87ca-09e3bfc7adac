import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/biometric_service.dart';
import '../../../core/services/secure_storage_service.dart';
import '../../../core/utils/logger.dart';

// Security State
class SecurityState {
  final bool isPinEnabled;
  final bool isBiometricEnabled;
  final BiometricCapability biometricCapability;
  final bool isAutoLockEnabled;
  final int autoLockDuration; // in minutes
  final bool isScreenshotProtectionEnabled;
  final bool isLockOnBackgroundEnabled;
  final bool isBackupCompleted;
  final double transactionLimit;
  final bool isLoading;
  final String? error;

  const SecurityState({
    this.isPinEnabled = false,
    this.isBiometricEnabled = false,
    this.biometricCapability = const BiometricCapability(
      isAvailable: false,
      availableTypes: [],
      isEnabled: false,
      hasFingerprint: false,
      hasFaceId: false,
      hasIris: false,
      platformSupport: 'Not available',
    ),
    this.isAutoLockEnabled = true,
    this.autoLockDuration = 5,
    this.isScreenshotProtectionEnabled = true,
    this.isLockOnBackgroundEnabled = true,
    this.isBackupCompleted = false,
    this.transactionLimit = 1000.0,
    this.isLoading = false,
    this.error,
  });

  SecurityState copyWith({
    bool? isPinEnabled,
    bool? isBiometricEnabled,
    BiometricCapability? biometricCapability,
    bool? isAutoLockEnabled,
    int? autoLockDuration,
    bool? isScreenshotProtectionEnabled,
    bool? isLockOnBackgroundEnabled,
    bool? isBackupCompleted,
    double? transactionLimit,
    bool? isLoading,
    String? error,
  }) {
    return SecurityState(
      isPinEnabled: isPinEnabled ?? this.isPinEnabled,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
      biometricCapability: biometricCapability ?? this.biometricCapability,
      isAutoLockEnabled: isAutoLockEnabled ?? this.isAutoLockEnabled,
      autoLockDuration: autoLockDuration ?? this.autoLockDuration,
      isScreenshotProtectionEnabled: isScreenshotProtectionEnabled ?? this.isScreenshotProtectionEnabled,
      isLockOnBackgroundEnabled: isLockOnBackgroundEnabled ?? this.isLockOnBackgroundEnabled,
      isBackupCompleted: isBackupCompleted ?? this.isBackupCompleted,
      transactionLimit: transactionLimit ?? this.transactionLimit,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get hasError => error != null;
  
  int get securityScore {
    int score = 0;
    if (isPinEnabled) score += 20;
    if (isBiometricEnabled) score += 25;
    if (isAutoLockEnabled) score += 15;
    if (isScreenshotProtectionEnabled) score += 10;
    if (isLockOnBackgroundEnabled) score += 10;
    if (isBackupCompleted) score += 20;
    return score;
  }

  String get securityLevel {
    if (securityScore >= 80) return 'Excellent';
    if (securityScore >= 60) return 'Good';
    if (securityScore >= 40) return 'Fair';
    return 'Poor';
  }
}

// Security Notifier
class SecurityNotifier extends StateNotifier<SecurityState> {
  SecurityNotifier() : super(const SecurityState()) {
    _initialize();
  }

  // Storage keys
  static const String _autoLockEnabledKey = 'auto_lock_enabled';
  static const String _autoLockDurationKey = 'auto_lock_duration';
  static const String _screenshotProtectionKey = 'screenshot_protection';
  static const String _lockOnBackgroundKey = 'lock_on_background';
  static const String _backupCompletedKey = 'backup_completed';
  static const String _transactionLimitKey = 'transaction_limit';

  Future<void> _initialize() async {
    try {
      await loadSecuritySettings();
    } catch (e) {
      AppLogger.error('Failed to initialize security settings', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load all security settings
  Future<void> loadSecuritySettings() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Load biometric capability
      final biometricCapability = await BiometricService.instance.getBiometricCapability();
      
      // Load PIN status
      final isPinEnabled = await SecureStorageService.instance.isPinSet();
      
      // Load biometric status
      final isBiometricEnabled = await BiometricService.instance.isBiometricEnabled();
      
      // Load other settings from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final isAutoLockEnabled = prefs.getBool(_autoLockEnabledKey) ?? true;
      final autoLockDuration = prefs.getInt(_autoLockDurationKey) ?? 5;
      final isScreenshotProtectionEnabled = prefs.getBool(_screenshotProtectionKey) ?? true;
      final isLockOnBackgroundEnabled = prefs.getBool(_lockOnBackgroundKey) ?? true;
      final isBackupCompleted = prefs.getBool(_backupCompletedKey) ?? false;
      final transactionLimit = prefs.getDouble(_transactionLimitKey) ?? 1000.0;

      state = state.copyWith(
        isPinEnabled: isPinEnabled,
        isBiometricEnabled: isBiometricEnabled,
        biometricCapability: biometricCapability,
        isAutoLockEnabled: isAutoLockEnabled,
        autoLockDuration: autoLockDuration,
        isScreenshotProtectionEnabled: isScreenshotProtectionEnabled,
        isLockOnBackgroundEnabled: isLockOnBackgroundEnabled,
        isBackupCompleted: isBackupCompleted,
        transactionLimit: transactionLimit,
        isLoading: false,
      );

      AppLogger.info('Security settings loaded successfully');
    } catch (e) {
      AppLogger.error('Failed to load security settings', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Enable biometric authentication
  Future<bool> enableBiometric() async {
    try {
      // First check if biometric is available
      if (!state.biometricCapability.isAvailable) {
        state = state.copyWith(error: 'Biometric authentication is not available');
        return false;
      }

      // Authenticate to enable biometric
      final result = await BiometricService.instance.authenticate(
        reason: 'Enable biometric authentication for KryptoPesa',
      );

      if (result.isSuccess) {
        await BiometricService.instance.setBiometricEnabled(true);
        state = state.copyWith(isBiometricEnabled: true);
        AppLogger.info('Biometric authentication enabled');
        return true;
      } else {
        state = state.copyWith(error: result.error ?? 'Biometric authentication failed');
        return false;
      }
    } catch (e) {
      AppLogger.error('Failed to enable biometric', e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Disable biometric authentication
  Future<bool> disableBiometric() async {
    try {
      await BiometricService.instance.setBiometricEnabled(false);
      state = state.copyWith(isBiometricEnabled: false);
      AppLogger.info('Biometric authentication disabled');
      return true;
    } catch (e) {
      AppLogger.error('Failed to disable biometric', e);
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Toggle auto-lock
  Future<void> toggleAutoLock() async {
    try {
      final newValue = !state.isAutoLockEnabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoLockEnabledKey, newValue);
      
      state = state.copyWith(isAutoLockEnabled: newValue);
      AppLogger.info('Auto-lock ${newValue ? 'enabled' : 'disabled'}');
    } catch (e) {
      AppLogger.error('Failed to toggle auto-lock', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Set auto-lock duration
  Future<void> setAutoLockDuration(int minutes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_autoLockDurationKey, minutes);
      
      state = state.copyWith(autoLockDuration: minutes);
      AppLogger.info('Auto-lock duration set to $minutes minutes');
    } catch (e) {
      AppLogger.error('Failed to set auto-lock duration', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Toggle screenshot protection
  Future<void> toggleScreenshotProtection() async {
    try {
      final newValue = !state.isScreenshotProtectionEnabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_screenshotProtectionKey, newValue);
      
      state = state.copyWith(isScreenshotProtectionEnabled: newValue);
      AppLogger.info('Screenshot protection ${newValue ? 'enabled' : 'disabled'}');
    } catch (e) {
      AppLogger.error('Failed to toggle screenshot protection', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Toggle lock on background
  Future<void> toggleLockOnBackground() async {
    try {
      final newValue = !state.isLockOnBackgroundEnabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_lockOnBackgroundKey, newValue);
      
      state = state.copyWith(isLockOnBackgroundEnabled: newValue);
      AppLogger.info('Lock on background ${newValue ? 'enabled' : 'disabled'}');
    } catch (e) {
      AppLogger.error('Failed to toggle lock on background', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Mark backup as completed
  Future<void> markBackupCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_backupCompletedKey, true);
      
      state = state.copyWith(isBackupCompleted: true);
      AppLogger.info('Backup marked as completed');
    } catch (e) {
      AppLogger.error('Failed to mark backup as completed', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Set transaction limit
  Future<void> setTransactionLimit(double limit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_transactionLimitKey, limit);
      
      state = state.copyWith(transactionLimit: limit);
      AppLogger.info('Transaction limit set to \$${limit.toStringAsFixed(2)}');
    } catch (e) {
      AppLogger.error('Failed to set transaction limit', e);
      state = state.copyWith(error: e.toString());
    }
  }

  /// Reset all security settings
  Future<void> resetSecuritySettings() async {
    try {
      state = state.copyWith(isLoading: true);

      // Clear secure storage
      await SecureStorageService.instance.clearAllData();
      
      // Reset biometric settings
      await BiometricService.instance.resetBiometricSettings();
      
      // Clear SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_autoLockEnabledKey);
      await prefs.remove(_autoLockDurationKey);
      await prefs.remove(_screenshotProtectionKey);
      await prefs.remove(_lockOnBackgroundKey);
      await prefs.remove(_backupCompletedKey);
      await prefs.remove(_transactionLimitKey);

      // Reset state to defaults
      state = const SecurityState();
      
      // Reload settings
      await loadSecuritySettings();
      
      AppLogger.info('Security settings reset successfully');
    } catch (e) {
      AppLogger.error('Failed to reset security settings', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Authenticate user with PIN or biometric
  Future<bool> authenticateUser({
    required String reason,
    bool allowBiometric = true,
  }) async {
    try {
      // Try biometric first if enabled and allowed
      if (allowBiometric && state.isBiometricEnabled) {
        final result = await BiometricService.instance.authenticate(reason: reason);
        if (result.isSuccess) {
          return true;
        }
        
        // If biometric fails but is available, don't fall back to PIN
        if (result.isAvailable) {
          return false;
        }
      }

      // Fall back to PIN if enabled
      if (state.isPinEnabled) {
        // TODO: Show PIN input dialog
        // For now, return false as PIN dialog is not implemented
        return false;
      }

      // No authentication method available
      return false;
    } catch (e) {
      AppLogger.error('Authentication failed', e);
      return false;
    }
  }

  /// Check if authentication is required for transaction
  bool isAuthenticationRequired(double amount) {
    return amount >= state.transactionLimit;
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final securityProvider = StateNotifierProvider<SecurityNotifier, SecurityState>((ref) {
  return SecurityNotifier();
});

// Additional providers for specific data
final securityScoreProvider = Provider<int>((ref) {
  final securityState = ref.watch(securityProvider);
  return securityState.securityScore;
});

final securityLevelProvider = Provider<String>((ref) {
  final securityState = ref.watch(securityProvider);
  return securityState.securityLevel;
});

final biometricCapabilityProvider = Provider<BiometricCapability>((ref) {
  final securityState = ref.watch(securityProvider);
  return securityState.biometricCapability;
});

final isSecuritySetupCompleteProvider = Provider<bool>((ref) {
  final securityState = ref.watch(securityProvider);
  return securityState.isPinEnabled || securityState.isBiometricEnabled;
});
