import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/wallet_models.dart';

class AmountInputField extends StatelessWidget {
  final TextEditingController controller;
  final CryptoBalance? selectedCrypto;
  final Function(String)? onChanged;
  final VoidCallback? onMaxPressed;

  const AmountInputField({
    super.key,
    required this.controller,
    this.selectedCrypto,
    this.onChanged,
    this.onMaxPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
    final cryptoFormat = NumberFormat('#,##0.########');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Enter amount',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            prefixIcon: Icon(Icons.monetization_on),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (selectedCrypto != null)
                  TextButton(
                    onPressed: onMaxPressed,
                    child: Text(
                      'MAX',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                Padding(
                  padding: const EdgeInsets.only(right: 12.0),
                  child: Text(
                    selectedCrypto?.symbol ?? '',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ),
              ],
            ),
            helperText: selectedCrypto != null
                ? 'Available: ${cryptoFormat.format(selectedCrypto!.balanceFormatted)} ${selectedCrypto!.symbol}'
                : null,
          ),
          validator: _validateAmount,
          onChanged: onChanged,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          textInputAction: TextInputAction.next,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            _DecimalTextInputFormatter(decimalRange: 8),
          ],
        ),
        
        // USD equivalent display
        if (selectedCrypto != null && controller.text.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildUSDEquivalent(theme, currencyFormat),
        ],
      ],
    );
  }

  Widget _buildUSDEquivalent(ThemeData theme, NumberFormat currencyFormat) {
    final amount = double.tryParse(controller.text) ?? 0.0;
    final priceUSD = selectedCrypto?.priceUSD ?? 0.0;
    final usdValue = amount * priceUSD;

    if (usdValue > 0) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.attach_money,
              size: 16,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(width: 4),
            Text(
              '≈ ${currencyFormat.format(usdValue)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  String? _validateAmount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter an amount';
    }

    final amount = double.tryParse(value.trim());
    if (amount == null) {
      return 'Please enter a valid number';
    }

    if (amount <= 0) {
      return 'Amount must be greater than 0';
    }

    if (selectedCrypto != null) {
      final availableBalance = selectedCrypto!.balanceFormatted;
      if (amount > availableBalance) {
        return 'Insufficient balance';
      }

      // Check minimum transaction amounts
      final minAmounts = {
        'BTC': 0.00001,
        'ETH': 0.001,
        'MATIC': 0.01,
        'USDT': 1.0,
        'USDC': 1.0,
        'DAI': 1.0,
      };

      final minAmount = minAmounts[selectedCrypto!.symbol] ?? 0.0;
      if (amount < minAmount) {
        return 'Minimum amount is $minAmount ${selectedCrypto!.symbol}';
      }
    }

    return null;
  }
}

class _DecimalTextInputFormatter extends TextInputFormatter {
  final int decimalRange;

  _DecimalTextInputFormatter({required this.decimalRange});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String newText = newValue.text;

    // Allow empty string
    if (newText.isEmpty) {
      return newValue;
    }

    // Check for valid decimal format
    if (!RegExp(r'^\d*\.?\d*$').hasMatch(newText)) {
      return oldValue;
    }

    // Check decimal places
    if (newText.contains('.')) {
      List<String> parts = newText.split('.');
      if (parts.length > 2) {
        return oldValue;
      }
      if (parts[1].length > decimalRange) {
        return oldValue;
      }
    }

    return newValue;
  }
}
