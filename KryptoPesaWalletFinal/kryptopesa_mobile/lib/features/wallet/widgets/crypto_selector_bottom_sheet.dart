import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/wallet_models.dart';

class CryptoSelectorBottomSheet extends StatelessWidget {
  final List<CryptoBalance> balances;
  final CryptoBalance? selectedCrypto;
  final Function(CryptoBalance) onCryptoSelected;

  const CryptoSelectorBottomSheet({
    super.key,
    required this.balances,
    this.selectedCrypto,
    required this.onCryptoSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cryptoFormat = NumberFormat('#,##0.########');

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Text(
                  'Select Cryptocurrency',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Crypto list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: balances.length,
              itemBuilder: (context, index) {
                final crypto = balances[index];
                final isSelected = selectedCrypto?.symbol == crypto.symbol &&
                                 selectedCrypto?.network == crypto.network;
                
                return ListTile(
                  onTap: () {
                    onCryptoSelected(crypto);
                    Navigator.of(context).pop();
                  },
                  leading: _buildCryptoIcon(crypto.symbol),
                  title: Row(
                    children: [
                      Text(
                        crypto.symbol,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildNetworkBadge(theme, crypto.network),
                    ],
                  ),
                  subtitle: Text(
                    'Balance: ${cryptoFormat.format(crypto.balanceFormatted)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: theme.colorScheme.primary,
                        )
                      : null,
                  selected: isSelected,
                  selectedTileColor: theme.colorScheme.primary.withOpacity(0.1),
                );
              },
            ),
          ),
          
          // Bottom padding
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCryptoIcon(String symbol) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getCryptoColor(symbol).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        _getCryptoIcon(symbol),
        color: _getCryptoColor(symbol),
        size: 20,
      ),
    );
  }

  Widget _buildNetworkBadge(ThemeData theme, String network) {
    if (network == 'bitcoin') return const SizedBox.shrink();

    Color badgeColor;
    String networkName;

    switch (network) {
      case 'polygon':
        badgeColor = const Color(0xFF8247E5);
        networkName = 'Polygon';
        break;
      case 'ethereum':
        badgeColor = const Color(0xFF627EEA);
        networkName = 'Ethereum';
        break;
      default:
        badgeColor = theme.colorScheme.outline;
        networkName = network.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: badgeColor.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        networkName,
        style: theme.textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getCryptoIcon(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.polygon;
      case 'USDT':
      case 'USDC':
        return Icons.attach_money;
      case 'DAI':
        return Icons.account_balance;
      default:
        return Icons.monetization_on;
    }
  }

  Color _getCryptoColor(String symbol) {
    switch (symbol.toUpperCase()) {
      case 'BTC':
        return const Color(0xFFF7931A);
      case 'ETH':
        return const Color(0xFF627EEA);
      case 'MATIC':
        return const Color(0xFF8247E5);
      case 'USDT':
        return const Color(0xFF26A17B);
      case 'USDC':
        return const Color(0xFF2775CA);
      case 'DAI':
        return const Color(0xFFF5AC37);
      default:
        return const Color(0xFF6B7280);
    }
  }
}
