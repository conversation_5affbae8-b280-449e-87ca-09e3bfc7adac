import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../models/wallet_models.dart';

class RecentTransactionsList extends StatelessWidget {
  final List<Transaction> transactions;
  final Function(Transaction)? onTransactionTap;

  const RecentTransactionsList({
    super.key,
    required this.transactions,
    this.onTransactionTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (transactions.isEmpty) {
      return _buildEmptyState(theme);
    }

    return Column(
      children: transactions
          .map((transaction) => _TransactionListItem(
                transaction: transaction,
                onTap: () => onTransactionTap?.call(transaction),
              ))
          .toList(),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 48,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No Transactions Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your transaction history will appear here',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _TransactionListItem extends StatelessWidget {
  final Transaction transaction;
  final VoidCallback? onTap;

  const _TransactionListItem({
    required this.transaction,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cryptoFormat = NumberFormat('#,##0.########');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Transaction Icon
              _buildTransactionIcon(theme),
              const SizedBox(width: 12),
              
              // Transaction Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _getTransactionTitle(),
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildStatusBadge(theme),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getTransactionSubtitle(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      timeago.format(transaction.timestamp),
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Amount
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${transaction.isIncoming ? '+' : '-'}${cryptoFormat.format(transaction.amountFormatted)}',
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: transaction.isIncoming 
                          ? Colors.green 
                          : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    transaction.symbol,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionIcon(ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (transaction.type) {
      case TransactionType.send:
        iconData = Icons.arrow_upward;
        iconColor = theme.colorScheme.error;
        break;
      case TransactionType.receive:
        iconData = Icons.arrow_downward;
        iconColor = Colors.green;
        break;
      case TransactionType.escrowFund:
        iconData = Icons.lock;
        iconColor = Colors.orange;
        break;
      case TransactionType.escrowRelease:
        iconData = Icons.lock_open;
        iconColor = Colors.blue;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  Widget _buildStatusBadge(ThemeData theme) {
    Color badgeColor;
    String statusText;

    switch (transaction.status) {
      case TransactionStatus.pending:
        badgeColor = Colors.orange;
        statusText = 'Pending';
        break;
      case TransactionStatus.confirmed:
        badgeColor = Colors.green;
        statusText = 'Confirmed';
        break;
      case TransactionStatus.failed:
        badgeColor = Colors.red;
        statusText = 'Failed';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: badgeColor.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        statusText,
        style: theme.textTheme.labelSmall?.copyWith(
          color: badgeColor,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _getTransactionTitle() {
    switch (transaction.type) {
      case TransactionType.send:
        return 'Sent ${transaction.symbol}';
      case TransactionType.receive:
        return 'Received ${transaction.symbol}';
      case TransactionType.escrowFund:
        return 'Escrow Funded';
      case TransactionType.escrowRelease:
        return 'Escrow Released';
    }
  }

  String _getTransactionSubtitle() {
    switch (transaction.type) {
      case TransactionType.send:
        return 'To ${_formatAddress(transaction.to)}';
      case TransactionType.receive:
        return 'From ${_formatAddress(transaction.from)}';
      case TransactionType.escrowFund:
        return 'Trade escrow funding';
      case TransactionType.escrowRelease:
        return 'Trade completed';
    }
  }

  String _formatAddress(String address) {
    if (address.length <= 10) return address;
    return '${address.substring(0, 6)}...${address.substring(address.length - 4)}';
  }
}
