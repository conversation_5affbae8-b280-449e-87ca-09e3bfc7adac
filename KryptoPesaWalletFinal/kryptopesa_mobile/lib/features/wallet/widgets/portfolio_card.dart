import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/wallet_models.dart';

class PortfolioCard extends StatelessWidget {
  final PortfolioValue? portfolioValue;
  final bool isLoading;

  const PortfolioCard({
    super.key,
    this.portfolioValue,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Portfolio Value',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              if (isLoading)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.white.withOpacity(0.7),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          if (portfolioValue != null) ...[
            Text(
              currencyFormat.format(portfolioValue!.totalValue),
              style: theme.textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPortfolioBreakdown(theme),
          ] else ...[
            Text(
              currencyFormat.format(0.0),
              style: theme.textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading portfolio data...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPortfolioBreakdown(ThemeData theme) {
    if (portfolioValue == null || portfolioValue!.breakdown.isEmpty) {
      return const SizedBox.shrink();
    }

    // Show top 3 assets
    final topAssets = portfolioValue!.breakdown.take(3).toList();

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Top Assets',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            Text(
              '${portfolioValue!.breakdown.length} assets',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...topAssets.map((asset) => _buildAssetRow(theme, asset)),
      ],
    );
  }

  Widget _buildAssetRow(ThemeData theme, PortfolioBreakdown asset) {
    final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
    final percentFormat = NumberFormat.percentPattern();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              _buildCryptoIcon(asset.symbol),
              const SizedBox(width: 8),
              Text(
                asset.symbol,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                currencyFormat.format(asset.value),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                percentFormat.format(asset.percentage / 100),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCryptoIcon(String symbol) {
    IconData iconData;
    Color iconColor = Colors.white.withOpacity(0.9);

    switch (symbol.toUpperCase()) {
      case 'BTC':
        iconData = Icons.currency_bitcoin;
        break;
      case 'ETH':
        iconData = Icons.diamond;
        break;
      case 'MATIC':
        iconData = Icons.change_history; // Use triangle icon instead of polygon
        break;
      case 'USDT':
      case 'USDC':
      case 'DAI':
        iconData = Icons.attach_money;
        break;
      default:
        iconData = Icons.monetization_on;
    }

    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }
}
