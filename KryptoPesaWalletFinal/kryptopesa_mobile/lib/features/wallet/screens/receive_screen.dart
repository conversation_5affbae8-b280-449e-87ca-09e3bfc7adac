import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/success_snackbar.dart';
import '../providers/wallet_provider.dart';
import '../models/wallet_models.dart';
import '../widgets/crypto_selector_bottom_sheet.dart';

class ReceiveScreen extends ConsumerStatefulWidget {
  final String? initialSymbol;

  const ReceiveScreen({
    super.key,
    this.initialSymbol,
  });

  @override
  ConsumerState<ReceiveScreen> createState() => _ReceiveScreenState();
}

class _ReceiveScreenState extends ConsumerState<ReceiveScreen> {
  final _amountController = TextEditingController();
  final _memoController = TextEditingController();
  
  String? _selectedCrypto;
  String? _selectedAddress;
  bool _includeAmount = false;
  bool _includeMemo = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSelectedCrypto();
    });
  }

  void _initializeSelectedCrypto() {
    final walletState = ref.read(walletProvider);
    final balances = walletState.balances;

    if (balances.isNotEmpty) {
      if (widget.initialSymbol != null) {
        final symbol = widget.initialSymbol!;
        if (balances.containsKey(symbol)) {
          _selectedCrypto = symbol;
        } else {
          _selectedCrypto = balances.keys.first;
        }
      } else {
        _selectedCrypto = balances.keys.first;
      }
      _updateSelectedAddress();
      setState(() {});
    }
  }

  void _updateSelectedAddress() {
    if (_selectedCrypto == null) return;

    final walletState = ref.read(walletProvider);
    final addresses = walletState.addresses;

    if (addresses.isNotEmpty) {
      _selectedAddress = addresses[_selectedCrypto!];
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _memoController.dispose();
    super.dispose();
  }

  String _generateQRData() {
    if (_selectedAddress == null) return '';
    
    String qrData = _selectedAddress!;
    
    // For Bitcoin, use BIP21 format
    if (_getNetworkForCrypto(_selectedCrypto!) == 'bitcoin') {
      qrData = 'bitcoin:$_selectedAddress';
      
      List<String> params = [];
      if (_includeAmount && _amountController.text.isNotEmpty) {
        params.add('amount=${_amountController.text}');
      }
      if (_includeMemo && _memoController.text.isNotEmpty) {
        params.add('label=${Uri.encodeComponent(_memoController.text)}');
      }
      
      if (params.isNotEmpty) {
        qrData += '?${params.join('&')}';
      }
    }
    // For Ethereum/Polygon, use EIP-681 format
    else {
      if (_includeAmount && _amountController.text.isNotEmpty) {
        qrData = 'ethereum:$_selectedAddress@${_getChainId()}?value=${_amountController.text}';
      } else {
        qrData = 'ethereum:$_selectedAddress@${_getChainId()}';
      }
    }
    
    return qrData;
  }

  String _getChainId() {
    switch (_getNetworkForCrypto(_selectedCrypto!)) {
      case 'ethereum':
        return '1'; // Ethereum mainnet
      case 'polygon':
        return '137'; // Polygon mainnet
      default:
        return '1';
    }
  }

  void _selectCrypto() {
    final balancesMap = ref.read(walletBalancesProvider);

    // Convert map to list of crypto options
    final cryptoOptions = balancesMap.keys.toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCryptoSelector(cryptoOptions),
    );
  }

  Widget _buildCryptoSelector(List<String> cryptoOptions) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Select Cryptocurrency',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...cryptoOptions.map((crypto) => ListTile(
            leading: Icon(_getCryptoIcon(crypto)),
            title: Text(crypto),
            trailing: _selectedCrypto == crypto
                ? const Icon(Icons.check, color: Colors.green)
                : null,
            onTap: () {
              setState(() {
                _selectedCrypto = crypto;
              });
              _updateSelectedAddress();
              Navigator.pop(context);
            },
          )),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  IconData _getCryptoIcon(String crypto) {
    switch (crypto) {
      case 'BTC':
        return Icons.currency_bitcoin;
      case 'ETH':
        return Icons.diamond;
      case 'MATIC':
        return Icons.hexagon;
      default:
        return Icons.monetization_on;
    }
  }

  Future<void> _copyAddress() async {
    if (_selectedAddress != null) {
      await Clipboard.setData(ClipboardData(text: _selectedAddress!));
      if (mounted) {
        SuccessSnackbar.show(
          context,
          message: 'Address copied to clipboard',
        );
      }
    }
  }

  Future<void> _shareAddress() async {
    if (_selectedAddress != null) {
      final qrData = _generateQRData();
      await Share.share(
        qrData,
        subject: 'My $_selectedCrypto Address',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: CustomAppBar(
        title: 'Receive Crypto',
        actions: [
          IconButton(
            icon: Icon(Icons.share),
            onPressed: _shareAddress,
            tooltip: 'Share Address',
          ),
        ],
      ),
      body: _selectedCrypto == null
          ? _buildLoadingState(theme)
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Crypto Selector
                  Text(
                    'Select Cryptocurrency',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildCryptoSelectorCard(theme),
                  
                  const SizedBox(height: 32),
                  
                  // QR Code
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.shadow.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: QrImageView(
                        data: _generateQRData(),
                        version: QrVersions.auto,
                        size: 200.0,
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        errorCorrectionLevel: QrErrorCorrectLevel.M,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Address Display
                  Text(
                    'Your $_selectedCrypto Address',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildAddressCard(theme),
                  
                  const SizedBox(height: 24),
                  
                  // Optional Amount
                  _buildAmountSection(theme),
                  
                  const SizedBox(height: 24),
                  
                  // Optional Memo
                  _buildMemoSection(theme),
                  
                  const SizedBox(height: 32),
                  
                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _copyAddress,
                          icon: Icon(Icons.copy),
                          label: Text('Copy Address'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: FilledButton.icon(
                          onPressed: _shareAddress,
                          icon: Icon(Icons.share),
                          label: Text('Share'),
                          style: FilledButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading wallet...',
            style: theme.textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildCryptoSelectorCard(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: ListTile(
        onTap: _selectCrypto,
        leading: _buildCryptoIcon(_selectedCrypto!),
        title: Text(
          _selectedCrypto!,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(_getNetworkDisplayName(_getNetworkForCrypto(_selectedCrypto!))),
        trailing: const Icon(Icons.keyboard_arrow_down),
      ),
    );
  }

  Widget _buildAddressCard(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _selectedAddress ?? 'Loading...',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _copyAddress,
                  icon: Icon(Icons.copy, size: 20),
                  tooltip: 'Copy Address',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Request Specific Amount (Optional)',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Switch(
              value: _includeAmount,
              onChanged: (value) {
                setState(() {
                  _includeAmount = value;
                  if (!value) {
                    _amountController.clear();
                  }
                });
              },
            ),
          ],
        ),
        if (_includeAmount) ...[
          const SizedBox(height: 12),
          TextFormField(
            controller: _amountController,
            decoration: InputDecoration(
              hintText: 'Enter amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              suffixText: _selectedCrypto,
            ),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            onChanged: (_) => setState(() {}),
          ),
        ],
      ],
    );
  }

  Widget _buildMemoSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Add Memo (Optional)',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Switch(
              value: _includeMemo,
              onChanged: (value) {
                setState(() {
                  _includeMemo = value;
                  if (!value) {
                    _memoController.clear();
                  }
                });
              },
            ),
          ],
        ),
        if (_includeMemo) ...[
          const SizedBox(height: 12),
          TextFormField(
            controller: _memoController,
            decoration: InputDecoration(
              hintText: 'Enter memo or description',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 2,
            maxLength: 100,
            onChanged: (_) => setState(() {}),
          ),
        ],
      ],
    );
  }

  Widget _buildCryptoIcon(String symbol) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getCryptoColor(symbol).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        _getCryptoIcon(symbol),
        color: _getCryptoColor(symbol),
        size: 20,
      ),
    );
  }

  // Removed duplicate methods - they already exist above

  String _getNetworkForCrypto(String crypto) {
    switch (crypto) {
      case 'BTC':
        return 'bitcoin';
      case 'ETH':
      case 'USDT':
      case 'USDC':
        return 'ethereum';
      case 'MATIC':
        return 'polygon';
      default:
        return 'ethereum';
    }
  }

  String _getNetworkDisplayName(String network) {
    switch (network) {
      case 'bitcoin':
        return 'Bitcoin Network';
      case 'ethereum':
        return 'Ethereum Network';
      case 'polygon':
        return 'Polygon Network';
      default:
        return network.toUpperCase();
    }
  }
}
