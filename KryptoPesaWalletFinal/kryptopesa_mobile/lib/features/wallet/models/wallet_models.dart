import 'package:json_annotation/json_annotation.dart';

part 'wallet_models.g.dart';

// Wallet Model
@JsonSerializable()
class Wallet {
  final String id;
  final String userId;
  final WalletAddresses addresses;
  final List<CryptoBalance> balances;
  final List<Transaction> transactions;
  final WalletSecurity security;
  final WalletPreferences preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Wallet({
    required this.id,
    required this.userId,
    required this.addresses,
    required this.balances,
    required this.transactions,
    required this.security,
    required this.preferences,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) => _$WalletFromJson(json);
  Map<String, dynamic> toJson() => _$WalletToJson(this);

  double get totalPortfolioValue {
    return balances.fold(0.0, (sum, balance) => sum + balance.valueUSD);
  }

  CryptoBalance? getBalance(String symbol, String network) {
    return balances.where((b) => b.symbol == symbol && b.network == network).firstOrNull;
  }

  List<Transaction> getRecentTransactions({int limit = 10}) {
    final sorted = List<Transaction>.from(transactions)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sorted.take(limit).toList();
  }
}

// Wallet Addresses
@JsonSerializable()
class WalletAddresses {
  final EthereumAddress ethereum;
  final BitcoinAddress? bitcoin;

  const WalletAddresses({
    required this.ethereum,
    this.bitcoin,
  });

  factory WalletAddresses.fromJson(Map<String, dynamic> json) => _$WalletAddressesFromJson(json);
  Map<String, dynamic> toJson() => _$WalletAddressesToJson(this);
}

@JsonSerializable()
class EthereumAddress {
  final String address;
  final String publicKey;
  final String derivationPath;

  const EthereumAddress({
    required this.address,
    required this.publicKey,
    required this.derivationPath,
  });

  factory EthereumAddress.fromJson(Map<String, dynamic> json) => _$EthereumAddressFromJson(json);
  Map<String, dynamic> toJson() => _$EthereumAddressToJson(this);
}

@JsonSerializable()
class BitcoinAddress {
  final String address;
  final String publicKey;
  final String derivationPath;

  const BitcoinAddress({
    required this.address,
    required this.publicKey,
    required this.derivationPath,
  });

  factory BitcoinAddress.fromJson(Map<String, dynamic> json) => _$BitcoinAddressFromJson(json);
  Map<String, dynamic> toJson() => _$BitcoinAddressToJson(this);
}

// Crypto Balance
@JsonSerializable()
class CryptoBalance {
  final String symbol;
  final String? contractAddress;
  final String network;
  final String balance; // Raw balance as string
  final int decimals;
  final DateTime lastUpdated;
  final double? priceUSD;
  final double? change24h;

  const CryptoBalance({
    required this.symbol,
    this.contractAddress,
    required this.network,
    required this.balance,
    required this.decimals,
    required this.lastUpdated,
    this.priceUSD,
    this.change24h,
  });

  factory CryptoBalance.fromJson(Map<String, dynamic> json) => _$CryptoBalanceFromJson(json);
  Map<String, dynamic> toJson() => _$CryptoBalanceToJson(this);

  double get balanceFormatted {
    final balanceInt = BigInt.tryParse(balance) ?? BigInt.zero;
    return balanceInt.toDouble() / (BigInt.from(10).pow(decimals).toDouble());
  }

  double get valueUSD {
    return balanceFormatted * (priceUSD ?? 0.0);
  }

  bool get isPositiveChange => (change24h ?? 0.0) >= 0;

  String get displaySymbol {
    switch (symbol) {
      case 'MATIC':
        return network == 'polygon' ? 'MATIC' : symbol;
      case 'ETH':
        return network == 'ethereum' ? 'ETH' : symbol;
      default:
        return symbol;
    }
  }
}

// Transaction
@JsonSerializable()
class Transaction {
  final String hash;
  final TransactionType type;
  final String symbol;
  final String amount;
  final String from;
  final String to;
  final String network;
  final int gasUsed;
  final String gasPrice;
  final String fee;
  final TransactionStatus status;
  final int confirmations;
  final DateTime timestamp;
  final String? memo;
  final String? relatedTradeId;

  const Transaction({
    required this.hash,
    required this.type,
    required this.symbol,
    required this.amount,
    required this.from,
    required this.to,
    required this.network,
    required this.gasUsed,
    required this.gasPrice,
    required this.fee,
    required this.status,
    required this.confirmations,
    required this.timestamp,
    this.memo,
    this.relatedTradeId,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => _$TransactionFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionToJson(this);

  double get amountFormatted {
    return double.tryParse(amount) ?? 0.0;
  }

  double get feeFormatted {
    return double.tryParse(fee) ?? 0.0;
  }

  bool get isIncoming => type == TransactionType.receive;
  bool get isOutgoing => type == TransactionType.send;
  bool get isPending => status == TransactionStatus.pending;
  bool get isConfirmed => status == TransactionStatus.confirmed;
  bool get isFailed => status == TransactionStatus.failed;
}

enum TransactionType {
  @JsonValue('send')
  send,
  @JsonValue('receive')
  receive,
  @JsonValue('escrow_fund')
  escrowFund,
  @JsonValue('escrow_release')
  escrowRelease,
}

enum TransactionStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('failed')
  failed,
}

// Wallet Security
@JsonSerializable()
class WalletSecurity {
  final bool backupCompleted;
  final DateTime? backupDate;
  final DateTime lastAccessDate;

  const WalletSecurity({
    required this.backupCompleted,
    this.backupDate,
    required this.lastAccessDate,
  });

  factory WalletSecurity.fromJson(Map<String, dynamic> json) => _$WalletSecurityFromJson(json);
  Map<String, dynamic> toJson() => _$WalletSecurityToJson(this);
}

// Wallet Preferences
@JsonSerializable()
class WalletPreferences {
  final String defaultNetwork;
  final bool autoRefreshBalances;
  final bool transactionNotifications;

  const WalletPreferences({
    required this.defaultNetwork,
    required this.autoRefreshBalances,
    required this.transactionNotifications,
  });

  factory WalletPreferences.fromJson(Map<String, dynamic> json) => _$WalletPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$WalletPreferencesToJson(this);
}

// Send Transaction Request
@JsonSerializable()
class SendTransactionRequest {
  final String toAddress;
  final String amount;
  final String symbol;
  final String network;
  final String? gasPrice;
  final int? gasLimit;
  final String? memo;

  const SendTransactionRequest({
    required this.toAddress,
    required this.amount,
    required this.symbol,
    required this.network,
    this.gasPrice,
    this.gasLimit,
    this.memo,
  });

  factory SendTransactionRequest.fromJson(Map<String, dynamic> json) => _$SendTransactionRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SendTransactionRequestToJson(this);
}

// Transaction Fee Estimate
@JsonSerializable()
class TransactionFeeEstimate {
  final String network;
  final String symbol;
  final String? gasPrice;
  final int? gasLimit;
  final String estimatedFee;
  final String? estimatedFeeUSD;
  final int? estimatedMinutes;

  const TransactionFeeEstimate({
    required this.network,
    required this.symbol,
    this.gasPrice,
    this.gasLimit,
    required this.estimatedFee,
    this.estimatedFeeUSD,
    this.estimatedMinutes,
  });

  factory TransactionFeeEstimate.fromJson(Map<String, dynamic> json) => _$TransactionFeeEstimateFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionFeeEstimateToJson(this);
}

// Portfolio Value
@JsonSerializable()
class PortfolioValue {
  final double totalValue;
  final String currency;
  final List<PortfolioBreakdown> breakdown;
  final DateTime timestamp;

  const PortfolioValue({
    required this.totalValue,
    required this.currency,
    required this.breakdown,
    required this.timestamp,
  });

  factory PortfolioValue.fromJson(Map<String, dynamic> json) => _$PortfolioValueFromJson(json);
  Map<String, dynamic> toJson() => _$PortfolioValueToJson(this);
}

@JsonSerializable()
class PortfolioBreakdown {
  final String symbol;
  final double balance;
  final double price;
  final double value;
  final double percentage;

  const PortfolioBreakdown({
    required this.symbol,
    required this.balance,
    required this.price,
    required this.value,
    required this.percentage,
  });

  factory PortfolioBreakdown.fromJson(Map<String, dynamic> json) => _$PortfolioBreakdownFromJson(json);
  Map<String, dynamic> toJson() => _$PortfolioBreakdownToJson(this);
}

// Crypto Prices
@JsonSerializable()
class CryptoPrices {
  final Map<String, Map<String, dynamic>> prices;
  final DateTime timestamp;

  const CryptoPrices({
    required this.prices,
    required this.timestamp,
  });

  factory CryptoPrices.fromJson(Map<String, dynamic> json) => _$CryptoPricesFromJson(json);
  Map<String, dynamic> toJson() => _$CryptoPricesToJson(this);

  double? getPrice(String crypto, String currency) {
    final cryptoId = _getCryptoId(crypto);
    final currencyKey = currency.toLowerCase();
    return prices[cryptoId]?[currencyKey]?.toDouble();
  }

  double? getChange24h(String crypto, String currency) {
    final cryptoId = _getCryptoId(crypto);
    final changeKey = '${currency.toLowerCase()}_24h_change';
    return prices[cryptoId]?[changeKey]?.toDouble();
  }

  String _getCryptoId(String symbol) {
    const mapping = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'MATIC': 'matic-network',
      'USDT': 'tether',
      'USDC': 'usd-coin',
      'DAI': 'dai',
    };
    return mapping[symbol.toUpperCase()] ?? symbol.toLowerCase();
  }
}
