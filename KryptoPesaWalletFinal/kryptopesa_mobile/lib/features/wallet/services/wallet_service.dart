import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/api_service.dart';
import '../../../core/constants/api_constants.dart';
import '../../../core/utils/logger.dart';
import '../models/wallet_models.dart';

class WalletService {
  static const String _walletKey = 'wallet_data';
  static const String _mnemonicKey = 'wallet_mnemonic';
  static const String _pricesKey = 'crypto_prices';
  static const String _portfolioKey = 'portfolio_value';
  
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Wallet? _currentWallet;
  static CryptoPrices? _cachedPrices;
  static bool _isInitialized = false;

  // Initialize the wallet service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadWalletData();
    _isInitialized = true;
    
    AppLogger.info('WalletService initialized - Wallet: ${_currentWallet != null}');
  }

  // Create new wallet
  static Future<WalletCreationResult> createWallet() async {
    try {
      AppLogger.info('Creating new wallet...');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletCreate,
      );

      if (response.success && response.data != null) {
        final data = response.data!['data'] as Map<String, dynamic>;
        final mnemonic = data['mnemonic'] as String;
        final addresses = data['addresses'] as Map<String, dynamic>;
        
        // Store mnemonic securely (only during creation)
        await _secureStorage.write(key: _mnemonicKey, value: mnemonic);
        
        // Fetch the complete wallet data
        await refreshWallet();
        
        AppLogger.info('Wallet created successfully');
        return WalletCreationResult.success(
          mnemonic: mnemonic,
          addresses: addresses,
        );
      }
      
      return WalletCreationResult.failure('Failed to create wallet');
    } catch (e) {
      AppLogger.error('Failed to create wallet', e);
      return WalletCreationResult.failure(e.toString());
    }
  }

  // Import existing wallet
  static Future<WalletCreationResult> importWallet(String mnemonic) async {
    try {
      AppLogger.info('Importing wallet...');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletImport,
        data: {'mnemonic': mnemonic},
      );

      if (response.success && response.data != null) {
        final data = response.data!['data'] as Map<String, dynamic>;
        final addresses = data['addresses'] as Map<String, dynamic>;
        
        // Store mnemonic securely
        await _secureStorage.write(key: _mnemonicKey, value: mnemonic);
        
        // Fetch the complete wallet data
        await refreshWallet();
        
        AppLogger.info('Wallet imported successfully');
        return WalletCreationResult.success(
          mnemonic: mnemonic,
          addresses: addresses,
        );
      }
      
      return WalletCreationResult.failure('Failed to import wallet');
    } catch (e) {
      AppLogger.error('Failed to import wallet', e);
      return WalletCreationResult.failure(e.toString());
    }
  }

  // Get current wallet
  static Future<Wallet?> getWallet() async {
    if (_currentWallet != null) {
      return _currentWallet;
    }

    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiConstants.walletGet,
      );

      if (response.success && response.data != null) {
        final walletData = response.data!['data']['wallet'] as Map<String, dynamic>;
        final wallet = Wallet.fromJson(walletData);
        await _saveWalletData(wallet);
        return wallet;
      }
    } catch (e) {
      AppLogger.error('Failed to get wallet', e);
    }

    return null;
  }

  // Refresh wallet data
  static Future<bool> refreshWallet() async {
    try {
      final wallet = await getWallet();
      if (wallet != null) {
        await refreshBalances();
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.error('Failed to refresh wallet', e);
      return false;
    }
  }

  // Refresh balances
  static Future<bool> refreshBalances() async {
    try {
      AppLogger.info('Refreshing balances...');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletBalances,
      );

      if (response.success && response.data != null) {
        final balances = response.data!['data']['balances'] as List;
        
        if (_currentWallet != null) {
          final updatedBalances = balances
              .map((b) => CryptoBalance.fromJson(b as Map<String, dynamic>))
              .toList();
          
          _currentWallet = Wallet(
            id: _currentWallet!.id,
            userId: _currentWallet!.userId,
            addresses: _currentWallet!.addresses,
            balances: updatedBalances,
            transactions: _currentWallet!.transactions,
            security: _currentWallet!.security,
            preferences: _currentWallet!.preferences,
            createdAt: _currentWallet!.createdAt,
            updatedAt: DateTime.now(),
          );
          
          await _saveWalletData(_currentWallet!);
        }
        
        AppLogger.info('Balances refreshed successfully');
        return true;
      }
      
      return false;
    } catch (e) {
      AppLogger.error('Failed to refresh balances', e);
      return false;
    }
  }

  // Send transaction
  static Future<TransactionResult> sendTransaction(SendTransactionRequest request) async {
    try {
      AppLogger.info('Sending transaction: ${request.symbol} ${request.amount}');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletSend,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final data = response.data!['data'] as Map<String, dynamic>;
        final txHash = data['txHash'] as String;
        final transaction = Transaction.fromJson(data['transaction'] as Map<String, dynamic>);
        
        // Update local wallet with new transaction
        if (_currentWallet != null) {
          final updatedTransactions = [transaction, ..._currentWallet!.transactions];
          _currentWallet = Wallet(
            id: _currentWallet!.id,
            userId: _currentWallet!.userId,
            addresses: _currentWallet!.addresses,
            balances: _currentWallet!.balances,
            transactions: updatedTransactions,
            security: _currentWallet!.security,
            preferences: _currentWallet!.preferences,
            createdAt: _currentWallet!.createdAt,
            updatedAt: DateTime.now(),
          );
          
          await _saveWalletData(_currentWallet!);
        }
        
        AppLogger.info('Transaction sent successfully: $txHash');
        return TransactionResult.success(
          txHash: txHash,
          transaction: transaction,
        );
      }
      
      return TransactionResult.failure('Failed to send transaction');
    } catch (e) {
      AppLogger.error('Failed to send transaction', e);
      return TransactionResult.failure(e.toString());
    }
  }

  // Estimate transaction fee
  static Future<TransactionFeeEstimate?> estimateTransactionFee(SendTransactionRequest request) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletEstimateFee,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final estimateData = response.data!['data']['estimate'] as Map<String, dynamic>;
        return TransactionFeeEstimate.fromJson(estimateData);
      }
    } catch (e) {
      AppLogger.error('Failed to estimate transaction fee', e);
    }

    return null;
  }

  // Get transaction history
  static Future<List<Transaction>> getTransactionHistory({int limit = 50, int offset = 0}) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.walletTransactions}?limit=$limit&offset=$offset',
      );

      if (response.success && response.data != null) {
        final data = response.data!['data'] as Map<String, dynamic>;
        final transactions = data['transactions'] as List;
        
        return transactions
            .map((t) => Transaction.fromJson(t as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      AppLogger.error('Failed to get transaction history', e);
    }

    return [];
  }

  // Get crypto prices
  static Future<CryptoPrices?> getCryptoPrices({
    List<String> cryptos = const ['BTC', 'ETH', 'MATIC', 'USDT', 'USDC'],
    List<String> currencies = const ['USD'],
  }) async {
    try {
      final cryptosParam = cryptos.join(',');
      final currenciesParam = currencies.join(',');
      
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.walletPrices}?cryptos=$cryptosParam&currencies=$currenciesParam',
      );

      if (response.success && response.data != null) {
        final pricesData = response.data!['data']['prices'] as Map<String, dynamic>;
        final prices = CryptoPrices(
          prices: pricesData.cast<String, Map<String, dynamic>>(),
          timestamp: DateTime.now(),
        );
        
        _cachedPrices = prices;
        await _savePricesData(prices);
        
        return prices;
      }
    } catch (e) {
      AppLogger.error('Failed to get crypto prices', e);
    }

    return _cachedPrices;
  }

  // Get portfolio value
  static Future<PortfolioValue?> getPortfolioValue({String currency = 'USD'}) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '${ApiConstants.walletPortfolioValue}?currency=$currency',
      );

      if (response.success && response.data != null) {
        final portfolioData = response.data!['data']['portfolioValue'] as Map<String, dynamic>;
        final portfolio = PortfolioValue.fromJson(portfolioData);
        
        await _savePortfolioData(portfolio);
        return portfolio;
      }
    } catch (e) {
      AppLogger.error('Failed to get portfolio value', e);
    }

    return null;
  }

  // Verify mnemonic
  static Future<bool> verifyMnemonic(String mnemonic) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletVerifyMnemonic,
        data: {'mnemonic': mnemonic},
      );

      if (response.success && response.data != null) {
        return response.data!['data']['isValid'] as bool;
      }
    } catch (e) {
      AppLogger.error('Failed to verify mnemonic', e);
    }

    return false;
  }

  // Mark backup as completed
  static Future<bool> markBackupCompleted() async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.walletBackupComplete,
      );

      if (response.success) {
        // Update local wallet security data
        if (_currentWallet != null) {
          final updatedSecurity = WalletSecurity(
            backupCompleted: true,
            backupDate: DateTime.now(),
            lastAccessDate: _currentWallet!.security.lastAccessDate,
          );
          
          _currentWallet = Wallet(
            id: _currentWallet!.id,
            userId: _currentWallet!.userId,
            addresses: _currentWallet!.addresses,
            balances: _currentWallet!.balances,
            transactions: _currentWallet!.transactions,
            security: updatedSecurity,
            preferences: _currentWallet!.preferences,
            createdAt: _currentWallet!.createdAt,
            updatedAt: DateTime.now(),
          );
          
          await _saveWalletData(_currentWallet!);
        }
        
        return true;
      }
    } catch (e) {
      AppLogger.error('Failed to mark backup as completed', e);
    }

    return false;
  }

  // Get stored mnemonic (for backup purposes)
  static Future<String?> getStoredMnemonic() async {
    try {
      return await _secureStorage.read(key: _mnemonicKey);
    } catch (e) {
      AppLogger.error('Failed to get stored mnemonic', e);
      return null;
    }
  }

  // Clear wallet data (logout)
  static Future<void> clearWalletData() async {
    try {
      await _secureStorage.delete(key: _mnemonicKey);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_walletKey);
      await prefs.remove(_pricesKey);
      await prefs.remove(_portfolioKey);
      
      _currentWallet = null;
      _cachedPrices = null;
      
      AppLogger.info('Wallet data cleared');
    } catch (e) {
      AppLogger.error('Failed to clear wallet data', e);
    }
  }

  // Private helper methods
  static Future<void> _loadWalletData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final walletJson = prefs.getString(_walletKey);
      
      if (walletJson != null) {
        final walletData = json.decode(walletJson) as Map<String, dynamic>;
        _currentWallet = Wallet.fromJson(walletData);
      }
      
      final pricesJson = prefs.getString(_pricesKey);
      if (pricesJson != null) {
        final pricesData = json.decode(pricesJson) as Map<String, dynamic>;
        _cachedPrices = CryptoPrices.fromJson(pricesData);
      }
    } catch (e) {
      AppLogger.error('Failed to load wallet data', e);
    }
  }

  static Future<void> _saveWalletData(Wallet wallet) async {
    try {
      _currentWallet = wallet;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_walletKey, json.encode(wallet.toJson()));
    } catch (e) {
      AppLogger.error('Failed to save wallet data', e);
    }
  }

  static Future<void> _savePricesData(CryptoPrices prices) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_pricesKey, json.encode(prices.toJson()));
    } catch (e) {
      AppLogger.error('Failed to save prices data', e);
    }
  }

  static Future<void> _savePortfolioData(PortfolioValue portfolio) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_portfolioKey, json.encode(portfolio.toJson()));
    } catch (e) {
      AppLogger.error('Failed to save portfolio data', e);
    }
  }

  // Getters
  static Wallet? get currentWallet => _currentWallet;
  static CryptoPrices? get cachedPrices => _cachedPrices;
  static bool get hasWallet => _currentWallet != null;
  static bool get isBackupCompleted => _currentWallet?.security.backupCompleted ?? false;
}

// Result classes
class WalletCreationResult {
  final bool success;
  final String? mnemonic;
  final Map<String, dynamic>? addresses;
  final String? error;

  const WalletCreationResult._({
    required this.success,
    this.mnemonic,
    this.addresses,
    this.error,
  });

  factory WalletCreationResult.success({
    required String mnemonic,
    required Map<String, dynamic> addresses,
  }) {
    return WalletCreationResult._(
      success: true,
      mnemonic: mnemonic,
      addresses: addresses,
    );
  }

  factory WalletCreationResult.failure(String error) {
    return WalletCreationResult._(
      success: false,
      error: error,
    );
  }
}

class TransactionResult {
  final bool success;
  final String? txHash;
  final Transaction? transaction;
  final String? error;

  const TransactionResult._({
    required this.success,
    this.txHash,
    this.transaction,
    this.error,
  });

  factory TransactionResult.success({
    required String txHash,
    required Transaction transaction,
  }) {
    return TransactionResult._(
      success: true,
      txHash: txHash,
      transaction: transaction,
    );
  }

  factory TransactionResult.failure(String error) {
    return TransactionResult._(
      success: false,
      error: error,
    );
  }
}
