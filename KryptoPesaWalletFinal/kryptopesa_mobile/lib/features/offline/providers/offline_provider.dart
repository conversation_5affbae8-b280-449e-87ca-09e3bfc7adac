import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/connectivity_service.dart';
import '../../../core/services/offline_queue_service.dart';
import '../../../core/services/sync_service.dart';
import '../../../core/services/local_database_service.dart';
import '../../../core/utils/logger.dart';

// Offline State
class OfflineState {
  final ConnectionStatus connectionStatus;
  final SyncStatus syncStatus;
  final bool isOnline;
  final bool isSyncing;
  final int pendingOperations;
  final DateTime? lastSyncTime;
  final List<String> syncErrors;
  final Map<String, dynamic> queueStats;
  final bool autoSyncEnabled;
  final bool isInitialized;

  const OfflineState({
    required this.connectionStatus,
    this.syncStatus = SyncStatus.idle,
    this.isOnline = false,
    this.isSyncing = false,
    this.pendingOperations = 0,
    this.lastSyncTime,
    this.syncErrors = const [],
    this.queueStats = const {},
    this.autoSyncEnabled = true,
    this.isInitialized = false,
  });

  OfflineState copyWith({
    ConnectionStatus? connectionStatus,
    SyncStatus? syncStatus,
    bool? isOnline,
    bool? isSyncing,
    int? pendingOperations,
    DateTime? lastSyncTime,
    List<String>? syncErrors,
    Map<String, dynamic>? queueStats,
    bool? autoSyncEnabled,
    bool? isInitialized,
  }) {
    return OfflineState(
      connectionStatus: connectionStatus ?? this.connectionStatus,
      syncStatus: syncStatus ?? this.syncStatus,
      isOnline: isOnline ?? this.isOnline,
      isSyncing: isSyncing ?? this.isSyncing,
      pendingOperations: pendingOperations ?? this.pendingOperations,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      syncErrors: syncErrors ?? this.syncErrors,
      queueStats: queueStats ?? this.queueStats,
      autoSyncEnabled: autoSyncEnabled ?? this.autoSyncEnabled,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  bool get hasGoodConnection => 
      isOnline && connectionStatus.quality.index >= ConnectionQuality.fair.index;
  
  bool get hasPendingOperations => pendingOperations > 0;
  
  bool get hasErrors => syncErrors.isNotEmpty;
  
  String get connectionQualityText {
    switch (connectionStatus.quality) {
      case ConnectionQuality.excellent:
        return 'Excellent';
      case ConnectionQuality.good:
        return 'Good';
      case ConnectionQuality.fair:
        return 'Fair';
      case ConnectionQuality.poor:
        return 'Poor';
      case ConnectionQuality.none:
        return 'No Connection';
    }
  }

  String get connectionTypeText {
    switch (connectionStatus.type) {
      case ConnectionType.wifi:
        return 'Wi-Fi';
      case ConnectionType.mobile:
        return 'Mobile Data';
      case ConnectionType.ethernet:
        return 'Ethernet';
      case ConnectionType.bluetooth:
        return 'Bluetooth';
      case ConnectionType.vpn:
        return 'VPN';
      case ConnectionType.other:
        return 'Other';
      case ConnectionType.none:
        return 'No Connection';
    }
  }
}

// Offline Notifier
class OfflineNotifier extends StateNotifier<OfflineState> {
  OfflineNotifier() : super(OfflineState(
    connectionStatus: ConnectionStatus(
      type: ConnectionType.none,
      quality: ConnectionQuality.none,
      isConnected: false,
      lastChecked: DateTime.now(),
    ),
  )) {
    _initialize();
  }

  final ConnectivityService _connectivity = ConnectivityService.instance;
  final OfflineQueueService _queue = OfflineQueueService.instance;
  final SyncService _sync = SyncService.instance;
  final LocalDatabaseService _database = LocalDatabaseService.instance;

  StreamSubscription<ConnectionStatus>? _connectivitySubscription;
  StreamSubscription<SyncResult>? _syncSubscription;
  Timer? _statsTimer;

  /// Initialize offline provider
  Future<void> _initialize() async {
    try {
      AppLogger.info('Initializing offline provider...');

      // Initialize services
      await _connectivity.initialize();
      await _queue.initialize();
      await _sync.initialize();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.statusStream.listen(
        _onConnectivityChanged,
      );

      // Listen to sync results
      _syncSubscription = _sync.syncResultStream.listen(
        _onSyncResult,
      );

      // Start periodic stats updates
      _startStatsTimer();

      // Update initial state
      await _updateState();

      state = state.copyWith(isInitialized: true);
      
      AppLogger.info('Offline provider initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize offline provider', e);
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectionStatus status) {
    AppLogger.info('Connectivity changed: ${status.type.name} (${status.quality.name})');
    
    state = state.copyWith(
      connectionStatus: status,
      isOnline: status.isConnected,
    );

    // Clear sync errors when connection is restored
    if (status.isConnected && state.hasErrors) {
      state = state.copyWith(syncErrors: []);
    }
  }

  /// Handle sync results
  void _onSyncResult(SyncResult result) {
    AppLogger.info('Sync result: ${result.status.name}');
    
    state = state.copyWith(
      syncStatus: result.status,
      isSyncing: false,
      lastSyncTime: result.timestamp,
      syncErrors: result.errors,
    );
  }

  /// Update state with latest stats
  Future<void> _updateState() async {
    try {
      final queueStats = await _queue.getQueueStats();
      final pendingOps = queueStats['pending_operations'] as int? ?? 0;
      
      state = state.copyWith(
        connectionStatus: _connectivity.currentStatus,
        isOnline: _connectivity.isConnected,
        isSyncing: _sync.isSyncing,
        syncStatus: _sync.currentStatus,
        pendingOperations: pendingOps,
        queueStats: queueStats,
      );
    } catch (e) {
      AppLogger.error('Failed to update offline state', e);
    }
  }

  /// Start periodic stats timer
  void _startStatsTimer() {
    _statsTimer?.cancel();
    _statsTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateState();
    });
  }

  /// Force sync
  Future<void> forceSync() async {
    if (!state.isOnline) {
      throw Exception('No internet connection available');
    }

    if (state.isSyncing) {
      AppLogger.warning('Sync already in progress');
      return;
    }

    state = state.copyWith(isSyncing: true, syncErrors: []);
    
    try {
      await _sync.forceSync();
    } catch (e) {
      AppLogger.error('Force sync failed', e);
      state = state.copyWith(
        isSyncing: false,
        syncErrors: [e.toString()],
      );
    }
  }

  /// Queue operation for offline execution
  Future<String> queueOperation({
    required OperationType type,
    required String endpoint,
    required String method,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    OperationPriority priority = OperationPriority.normal,
  }) async {
    try {
      final operationId = await _queue.queueOperation(
        type: type,
        endpoint: endpoint,
        method: method,
        data: data,
        headers: headers,
        priority: priority,
      );

      // Update pending operations count
      await _updateState();

      return operationId;
    } catch (e) {
      AppLogger.error('Failed to queue operation', e);
      rethrow;
    }
  }

  /// Clear failed operations
  Future<void> clearFailedOperations() async {
    try {
      await _queue.clearFailedOperations();
      await _updateState();
    } catch (e) {
      AppLogger.error('Failed to clear failed operations', e);
    }
  }

  /// Toggle auto-sync
  void toggleAutoSync() {
    final newValue = !state.autoSyncEnabled;
    _sync.setAutoSyncEnabled(newValue);
    
    state = state.copyWith(autoSyncEnabled: newValue);
    AppLogger.info('Auto-sync ${newValue ? 'enabled' : 'disabled'}');
  }

  /// Refresh connectivity status
  Future<void> refreshConnectivity() async {
    await _connectivity.refresh();
  }

  /// Wait for connection
  Future<bool> waitForConnection({
    Duration timeout = const Duration(seconds: 30),
    ConnectionQuality minQuality = ConnectionQuality.fair,
  }) async {
    return await _connectivity.waitForConnection(
      timeout: timeout,
      minQuality: minQuality,
    );
  }

  /// Get detailed connection info
  Map<String, dynamic> getConnectionInfo() {
    return _connectivity.getConnectionInfo();
  }

  /// Get sync statistics
  Map<String, dynamic> getSyncStats() {
    return _sync.getSyncStats();
  }

  /// Cache data locally
  Future<void> cacheData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiresIn,
  }) async {
    try {
      await _database.cacheData(key, data, expiresIn: expiresIn);
    } catch (e) {
      AppLogger.error('Failed to cache data', e);
    }
  }

  /// Get cached data
  Future<Map<String, dynamic>?> getCachedData(String key) async {
    try {
      return await _database.getCachedData(key);
    } catch (e) {
      AppLogger.error('Failed to get cached data', e);
      return null;
    }
  }

  /// Clear expired cache
  Future<void> clearExpiredCache() async {
    try {
      await _database.clearExpiredCache();
    } catch (e) {
      AppLogger.error('Failed to clear expired cache', e);
    }
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    try {
      return await _database.getDatabaseStats();
    } catch (e) {
      AppLogger.error('Failed to get database stats', e);
      return {};
    }
  }

  /// Clear sync errors
  void clearSyncErrors() {
    state = state.copyWith(syncErrors: []);
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncSubscription?.cancel();
    _statsTimer?.cancel();
    
    _connectivity.dispose();
    _queue.dispose();
    _sync.dispose();
    
    super.dispose();
  }
}

// Provider
final offlineProvider = StateNotifierProvider<OfflineNotifier, OfflineState>((ref) {
  return OfflineNotifier();
});

// Additional providers for specific data
final connectionStatusProvider = Provider<ConnectionStatus>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.connectionStatus;
});

final isOnlineProvider = Provider<bool>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.isOnline;
});

final pendingOperationsProvider = Provider<int>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.pendingOperations;
});

final syncStatusProvider = Provider<SyncStatus>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.syncStatus;
});

final hasGoodConnectionProvider = Provider<bool>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.hasGoodConnection;
});

final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  final offlineState = ref.watch(offlineProvider);
  return offlineState.lastSyncTime;
});
