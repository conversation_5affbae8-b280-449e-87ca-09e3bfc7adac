import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../../core/services/connectivity_service.dart';
import '../../../core/services/sync_service.dart';
import '../providers/offline_provider.dart';

class OfflineStatusWidget extends ConsumerWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const OfflineStatusWidget({
    super.key,
    this.showDetails = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final offlineState = ref.watch(offlineProvider);

    if (!offlineState.isInitialized) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _getStatusColor(offlineState).withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: _getStatusColor(offlineState).withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status icon
            Icon(
              _getStatusIcon(offlineState),
              size: 16,
              color: _getStatusColor(offlineState),
            ),
            
            const SizedBox(width: 6),
            
            // Status text
            Text(
              _getStatusText(offlineState),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStatusColor(offlineState),
                fontWeight: FontWeight.w600,
              ),
            ),
            
            // Pending operations indicator
            if (offlineState.hasPendingOperations) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${offlineState.pendingOperations}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            
            // Sync indicator
            if (offlineState.isSyncing) ...[
              const SizedBox(width: 6),
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getStatusColor(offlineState),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(OfflineState state) {
    if (!state.isOnline) return Colors.red;
    if (state.isSyncing) return Colors.blue;
    if (state.hasErrors) return Colors.orange;
    if (state.hasPendingOperations) return Colors.amber;
    
    switch (state.connectionStatus.quality) {
      case ConnectionQuality.excellent:
      case ConnectionQuality.good:
        return Colors.green;
      case ConnectionQuality.fair:
        return Colors.orange;
      case ConnectionQuality.poor:
        return Colors.red;
      case ConnectionQuality.none:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(OfflineState state) {
    if (!state.isOnline) return Icons.cloud_off;
    if (state.isSyncing) return Icons.sync;
    if (state.hasErrors) return Icons.warning;
    if (state.hasPendingOperations) return Icons.schedule;
    
    switch (state.connectionStatus.type) {
      case ConnectionType.wifi:
        return Icons.wifi;
      case ConnectionType.mobile:
        return Icons.signal_cellular_4_bar;
      case ConnectionType.ethernet:
        return Icons.lan;
      case ConnectionType.bluetooth:
        return Icons.bluetooth;
      case ConnectionType.vpn:
        return Icons.vpn_lock;
      case ConnectionType.other:
        return Icons.device_hub;
      case ConnectionType.none:
        return Icons.cloud_off;
    }
  }

  String _getStatusText(OfflineState state) {
    if (!state.isOnline) return 'Offline';
    if (state.isSyncing) return 'Syncing';
    if (state.hasErrors) return 'Sync Error';
    if (state.hasPendingOperations) return 'Pending';
    
    return state.connectionQualityText;
  }
}

class OfflineStatusBanner extends ConsumerWidget {
  const OfflineStatusBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final offlineState = ref.watch(offlineProvider);

    // Only show banner when offline or has issues
    if (offlineState.isOnline && !offlineState.hasErrors && !offlineState.hasPendingOperations) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getBannerColor(offlineState),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            _getBannerIcon(offlineState),
            color: Colors.white,
            size: 20,
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getBannerTitle(offlineState),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                Text(
                  _getBannerMessage(offlineState),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          
          // Action button
          if (_shouldShowAction(offlineState))
            TextButton(
              onPressed: () => _handleAction(context, ref, offlineState),
              child: Text(
                _getActionText(offlineState),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getBannerColor(OfflineState state) {
    if (!state.isOnline) return Colors.red;
    if (state.hasErrors) return Colors.orange;
    if (state.hasPendingOperations) return Colors.blue;
    return Colors.grey;
  }

  IconData _getBannerIcon(OfflineState state) {
    if (!state.isOnline) return Icons.cloud_off;
    if (state.hasErrors) return Icons.warning;
    if (state.hasPendingOperations) return Icons.schedule;
    return Icons.info;
  }

  String _getBannerTitle(OfflineState state) {
    if (!state.isOnline) return 'You\'re offline';
    if (state.hasErrors) return 'Sync issues';
    if (state.hasPendingOperations) return 'Pending operations';
    return 'Status';
  }

  String _getBannerMessage(OfflineState state) {
    if (!state.isOnline) {
      return 'Some features may be limited. Changes will sync when connection is restored.';
    }
    
    if (state.hasErrors) {
      return 'Some data couldn\'t sync. ${state.syncErrors.length} error(s) occurred.';
    }
    
    if (state.hasPendingOperations) {
      return '${state.pendingOperations} operation(s) waiting to sync.';
    }
    
    return 'Everything is up to date.';
  }

  bool _shouldShowAction(OfflineState state) {
    return state.isOnline && (state.hasErrors || state.hasPendingOperations);
  }

  String _getActionText(OfflineState state) {
    if (state.hasErrors) return 'Retry';
    if (state.hasPendingOperations) return 'Sync Now';
    return 'OK';
  }

  void _handleAction(BuildContext context, WidgetRef ref, OfflineState state) {
    final offlineNotifier = ref.read(offlineProvider.notifier);
    
    if (state.hasErrors) {
      offlineNotifier.clearSyncErrors();
      offlineNotifier.forceSync();
    } else if (state.hasPendingOperations) {
      offlineNotifier.forceSync();
    }
  }
}

class DetailedOfflineStatus extends ConsumerWidget {
  const DetailedOfflineStatus({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final offlineState = ref.watch(offlineProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.network_check,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Connection Status',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Connection info
            _buildInfoRow(
              context,
              'Connection',
              '${offlineState.connectionTypeText} (${offlineState.connectionQualityText})',
              offlineState.isOnline ? Icons.check_circle : Icons.error,
              offlineState.isOnline ? Colors.green : Colors.red,
            ),
            
            const SizedBox(height: 8),
            
            // Sync status
            _buildInfoRow(
              context,
              'Sync Status',
              offlineState.syncStatus.name.toUpperCase(),
              offlineState.isSyncing ? Icons.sync : Icons.check,
              offlineState.isSyncing ? Colors.blue : Colors.green,
            ),
            
            const SizedBox(height: 8),
            
            // Pending operations
            _buildInfoRow(
              context,
              'Pending Operations',
              '${offlineState.pendingOperations}',
              Icons.schedule,
              offlineState.hasPendingOperations ? Colors.orange : Colors.green,
            ),
            
            const SizedBox(height: 8),
            
            // Last sync
            if (offlineState.lastSyncTime != null)
              _buildInfoRow(
                context,
                'Last Sync',
                timeago.format(offlineState.lastSyncTime!),
                Icons.access_time,
                Colors.grey,
              ),
            
            // Errors
            if (offlineState.hasErrors) ...[
              const SizedBox(height: 16),
              Text(
                'Sync Errors',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...offlineState.syncErrors.map((error) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 16,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          error,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            // Actions
            const SizedBox(height: 16),
            Row(
              children: [
                if (offlineState.isOnline)
                  Expanded(
                    child: FilledButton.icon(
                      onPressed: offlineState.isSyncing 
                          ? null 
                          : () => ref.read(offlineProvider.notifier).forceSync(),
                      icon: Icon(Icons.sync),
                      label: Text('Force Sync'),
                    ),
                  ),
                
                if (offlineState.isOnline && offlineState.hasErrors) ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => ref.read(offlineProvider.notifier).clearSyncErrors(),
                      icon: Icon(Icons.clear),
                      label: Text('Clear Errors'),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
