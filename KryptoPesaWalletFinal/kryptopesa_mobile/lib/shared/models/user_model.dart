import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

/// User Model
/// Represents a user in the KryptoPesa system
@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profilePicture;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isKycVerified;
  final UserStatus status;
  final UserRole role;
  final ReputationModel reputation;
  final SecuritySettingsModel securitySettings;
  final NotificationSettingsModel notificationSettings;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final String? referralCode;
  final String? referredBy;
  final Map<String, dynamic>? metadata;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profilePicture,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isKycVerified,
    required this.status,
    required this.role,
    required this.reputation,
    required this.securitySettings,
    required this.notificationSettings,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.referralCode,
    this.referredBy,
    this.metadata,
  });

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get display name
  String get displayName => fullName.trim().isEmpty ? email : fullName;

  /// Check if user is verified
  bool get isVerified => isEmailVerified && isKycVerified;

  /// Check if user can trade
  bool get canTrade => isVerified && status == UserStatus.active;

  /// Check if user is admin
  bool get isAdmin => role == UserRole.admin || role == UserRole.superAdmin;

  /// Check if user is moderator
  bool get isModerator => role == UserRole.moderator || isAdmin;

  /// Factory constructor from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// Copy with method
  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profilePicture,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isKycVerified,
    UserStatus? status,
    UserRole? role,
    ReputationModel? reputation,
    SecuritySettingsModel? securitySettings,
    NotificationSettingsModel? notificationSettings,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    String? referralCode,
    String? referredBy,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profilePicture: profilePicture ?? this.profilePicture,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isKycVerified: isKycVerified ?? this.isKycVerified,
      status: status ?? this.status,
      role: role ?? this.role,
      reputation: reputation ?? this.reputation,
      securitySettings: securitySettings ?? this.securitySettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      referralCode: referralCode ?? this.referralCode,
      referredBy: referredBy ?? this.referredBy,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// User Status Enum
enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('banned')
  banned,
  @JsonValue('pending')
  pending,
}

/// User Role Enum
enum UserRole {
  @JsonValue('user')
  user,
  @JsonValue('moderator')
  moderator,
  @JsonValue('admin')
  admin,
  @JsonValue('super_admin')
  superAdmin,
}

/// Reputation Model
@JsonSerializable()
class ReputationModel {
  final double score;
  final int totalTrades;
  final int successfulTrades;
  final int disputedTrades;
  final int positiveReviews;
  final int negativeReviews;
  final double averageRating;
  final List<String> badges;

  const ReputationModel({
    required this.score,
    required this.totalTrades,
    required this.successfulTrades,
    required this.disputedTrades,
    required this.positiveReviews,
    required this.negativeReviews,
    required this.averageRating,
    required this.badges,
  });

  /// Get success rate
  double get successRate {
    if (totalTrades == 0) return 0.0;
    return (successfulTrades / totalTrades) * 100;
  }

  /// Get dispute rate
  double get disputeRate {
    if (totalTrades == 0) return 0.0;
    return (disputedTrades / totalTrades) * 100;
  }

  /// Check if user is trusted
  bool get isTrusted => score >= 80.0 && successRate >= 95.0;

  factory ReputationModel.fromJson(Map<String, dynamic> json) => _$ReputationModelFromJson(json);
  Map<String, dynamic> toJson() => _$ReputationModelToJson(this);
}

/// Security Settings Model
@JsonSerializable()
class SecuritySettingsModel {
  final bool twoFactorEnabled;
  final bool biometricEnabled;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool loginAlerts;
  final bool tradeAlerts;
  final int sessionTimeout;
  final List<String> trustedDevices;

  const SecuritySettingsModel({
    required this.twoFactorEnabled,
    required this.biometricEnabled,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.loginAlerts,
    required this.tradeAlerts,
    required this.sessionTimeout,
    required this.trustedDevices,
  });

  factory SecuritySettingsModel.fromJson(Map<String, dynamic> json) => _$SecuritySettingsModelFromJson(json);
  Map<String, dynamic> toJson() => _$SecuritySettingsModelToJson(this);
}

/// Notification Settings Model
@JsonSerializable()
class NotificationSettingsModel {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool tradeUpdates;
  final bool chatMessages;
  final bool priceAlerts;
  final bool securityAlerts;
  final bool marketingEmails;
  final Map<String, bool> customSettings;

  const NotificationSettingsModel({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.tradeUpdates,
    required this.chatMessages,
    required this.priceAlerts,
    required this.securityAlerts,
    required this.marketingEmails,
    required this.customSettings,
  });

  factory NotificationSettingsModel.fromJson(Map<String, dynamic> json) => _$NotificationSettingsModelFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationSettingsModelToJson(this);
}
