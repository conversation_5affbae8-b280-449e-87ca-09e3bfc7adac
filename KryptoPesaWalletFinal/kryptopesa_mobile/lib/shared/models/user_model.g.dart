// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      profilePicture: json['profilePicture'] as String?,
      isEmailVerified: json['isEmailVerified'] as bool,
      isPhoneVerified: json['isPhoneVerified'] as bool,
      isKycVerified: json['isKycVerified'] as bool,
      status: $enumDecode(_$UserStatusEnumMap, json['status']),
      role: $enumDecode(_$UserRoleEnumMap, json['role']),
      reputation:
          ReputationModel.fromJson(json['reputation'] as Map<String, dynamic>),
      securitySettings: SecuritySettingsModel.fromJson(
          json['securitySettings'] as Map<String, dynamic>),
      notificationSettings: NotificationSettingsModel.fromJson(
          json['notificationSettings'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      referralCode: json['referralCode'] as String?,
      referredBy: json['referredBy'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'profilePicture': instance.profilePicture,
      'isEmailVerified': instance.isEmailVerified,
      'isPhoneVerified': instance.isPhoneVerified,
      'isKycVerified': instance.isKycVerified,
      'status': _$UserStatusEnumMap[instance.status]!,
      'role': _$UserRoleEnumMap[instance.role]!,
      'reputation': instance.reputation,
      'securitySettings': instance.securitySettings,
      'notificationSettings': instance.notificationSettings,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'referralCode': instance.referralCode,
      'referredBy': instance.referredBy,
      'metadata': instance.metadata,
    };

const _$UserStatusEnumMap = {
  UserStatus.active: 'active',
  UserStatus.inactive: 'inactive',
  UserStatus.suspended: 'suspended',
  UserStatus.banned: 'banned',
  UserStatus.pending: 'pending',
};

const _$UserRoleEnumMap = {
  UserRole.user: 'user',
  UserRole.moderator: 'moderator',
  UserRole.admin: 'admin',
  UserRole.superAdmin: 'super_admin',
};

ReputationModel _$ReputationModelFromJson(Map<String, dynamic> json) =>
    ReputationModel(
      score: (json['score'] as num).toDouble(),
      totalTrades: (json['totalTrades'] as num).toInt(),
      successfulTrades: (json['successfulTrades'] as num).toInt(),
      disputedTrades: (json['disputedTrades'] as num).toInt(),
      positiveReviews: (json['positiveReviews'] as num).toInt(),
      negativeReviews: (json['negativeReviews'] as num).toInt(),
      averageRating: (json['averageRating'] as num).toDouble(),
      badges:
          (json['badges'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ReputationModelToJson(ReputationModel instance) =>
    <String, dynamic>{
      'score': instance.score,
      'totalTrades': instance.totalTrades,
      'successfulTrades': instance.successfulTrades,
      'disputedTrades': instance.disputedTrades,
      'positiveReviews': instance.positiveReviews,
      'negativeReviews': instance.negativeReviews,
      'averageRating': instance.averageRating,
      'badges': instance.badges,
    };

SecuritySettingsModel _$SecuritySettingsModelFromJson(
        Map<String, dynamic> json) =>
    SecuritySettingsModel(
      twoFactorEnabled: json['twoFactorEnabled'] as bool,
      biometricEnabled: json['biometricEnabled'] as bool,
      emailNotifications: json['emailNotifications'] as bool,
      smsNotifications: json['smsNotifications'] as bool,
      loginAlerts: json['loginAlerts'] as bool,
      tradeAlerts: json['tradeAlerts'] as bool,
      sessionTimeout: (json['sessionTimeout'] as num).toInt(),
      trustedDevices: (json['trustedDevices'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$SecuritySettingsModelToJson(
        SecuritySettingsModel instance) =>
    <String, dynamic>{
      'twoFactorEnabled': instance.twoFactorEnabled,
      'biometricEnabled': instance.biometricEnabled,
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'loginAlerts': instance.loginAlerts,
      'tradeAlerts': instance.tradeAlerts,
      'sessionTimeout': instance.sessionTimeout,
      'trustedDevices': instance.trustedDevices,
    };

NotificationSettingsModel _$NotificationSettingsModelFromJson(
        Map<String, dynamic> json) =>
    NotificationSettingsModel(
      pushNotifications: json['pushNotifications'] as bool,
      emailNotifications: json['emailNotifications'] as bool,
      smsNotifications: json['smsNotifications'] as bool,
      tradeUpdates: json['tradeUpdates'] as bool,
      chatMessages: json['chatMessages'] as bool,
      priceAlerts: json['priceAlerts'] as bool,
      securityAlerts: json['securityAlerts'] as bool,
      marketingEmails: json['marketingEmails'] as bool,
      customSettings: Map<String, bool>.from(json['customSettings'] as Map),
    );

Map<String, dynamic> _$NotificationSettingsModelToJson(
        NotificationSettingsModel instance) =>
    <String, dynamic>{
      'pushNotifications': instance.pushNotifications,
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'tradeUpdates': instance.tradeUpdates,
      'chatMessages': instance.chatMessages,
      'priceAlerts': instance.priceAlerts,
      'securityAlerts': instance.securityAlerts,
      'marketingEmails': instance.marketingEmails,
      'customSettings': instance.customSettings,
    };
