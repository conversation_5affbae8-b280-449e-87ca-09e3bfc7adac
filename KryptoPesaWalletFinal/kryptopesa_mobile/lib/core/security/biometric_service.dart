import 'package:local_auth/local_auth.dart';
import 'package:flutter/services.dart';

import '../storage/secure_storage.dart';
import '../utils/logger.dart';

/// Biometric Authentication Service
/// Handles fingerprint, face recognition, and other biometric authentication
class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();
  static bool _isInitialized = false;
  static bool _isAvailable = false;
  static List<BiometricType> _availableBiometrics = [];
  
  /// Initialize biometric service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing biometric service...');
      
      // Check if biometric authentication is available
      _isAvailable = await _localAuth.canCheckBiometrics;
      
      if (_isAvailable) {
        // Get available biometric types
        _availableBiometrics = await _localAuth.getAvailableBiometrics();
        AppLogger.biometric('Available biometrics', true);
        AppLogger.info('Available biometrics: $_availableBiometrics');
      } else {
        AppLogger.biometric('Biometrics not available', false);
      }
      
      _isInitialized = true;
      AppLogger.info('Biometric service initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize biometric service', error, stackTrace);
      _isAvailable = false;
      _isInitialized = true; // Still mark as initialized to prevent retries
    }
  }
  
  /// Check if biometric authentication is available
  static bool get isAvailable => _isAvailable;
  
  /// Get available biometric types
  static List<BiometricType> get availableBiometrics => _availableBiometrics;
  
  /// Check if fingerprint is available
  static bool get isFingerprintAvailable =>
      _availableBiometrics.contains(BiometricType.fingerprint);
  
  /// Check if face recognition is available
  static bool get isFaceAvailable =>
      _availableBiometrics.contains(BiometricType.face);
  
  /// Check if iris recognition is available
  static bool get isIrisAvailable =>
      _availableBiometrics.contains(BiometricType.iris);
  
  /// Check if any strong biometric is available
  static bool get isStrongBiometricAvailable =>
      _availableBiometrics.contains(BiometricType.strong);
  
  /// Check if weak biometric is available
  static bool get isWeakBiometricAvailable =>
      _availableBiometrics.contains(BiometricType.weak);
  
  /// Authenticate with biometrics
  static Future<bool> authenticate({
    String localizedReason = 'Please authenticate to access KryptoPesa',
    bool useErrorDialogs = true,
    bool stickyAuth = false,
    bool sensitiveTransaction = false,
    bool biometricOnly = false,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (!_isAvailable) {
      AppLogger.biometric('Authentication failed - not available', false);
      return false;
    }
    
    try {
      final AuthenticationOptions options = AuthenticationOptions(
        useErrorDialogs: useErrorDialogs,
        stickyAuth: stickyAuth,
        sensitiveTransaction: sensitiveTransaction,
        biometricOnly: biometricOnly,
      );
      
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: options,
      );
      
      AppLogger.biometric('Authentication attempt', didAuthenticate);
      
      if (didAuthenticate) {
        // Store successful authentication timestamp
        await SecureStorage.write(
          'last_biometric_auth',
          DateTime.now().toIso8601String(),
        );
      }
      
      return didAuthenticate;
      
    } on PlatformException catch (e) {
      AppLogger.error('Biometric authentication error', e);
      
      switch (e.code) {
        case 'NotAvailable':
          AppLogger.biometric('Biometrics not available', false);
          break;
        case 'NotEnrolled':
          AppLogger.biometric('No biometrics enrolled', false);
          break;
        case 'LockedOut':
          AppLogger.biometric('Biometrics locked out', false);
          break;
        case 'PermanentlyLockedOut':
          AppLogger.biometric('Biometrics permanently locked out', false);
          break;
        default:
          AppLogger.biometric('Unknown biometric error: ${e.code}', false);
      }
      
      return false;
    } catch (error, stackTrace) {
      AppLogger.error('Unexpected biometric error', error, stackTrace);
      return false;
    }
  }
  
  /// Authenticate for wallet access
  static Future<bool> authenticateForWallet() async {
    return await authenticate(
      localizedReason: 'Authenticate to access your crypto wallet',
      sensitiveTransaction: true,
      biometricOnly: true,
    );
  }
  
  /// Authenticate for trading
  static Future<bool> authenticateForTrading() async {
    return await authenticate(
      localizedReason: 'Authenticate to confirm this trade',
      sensitiveTransaction: true,
      stickyAuth: true,
    );
  }
  
  /// Authenticate for sending crypto
  static Future<bool> authenticateForSending({
    required String amount,
    required String currency,
    required String recipient,
  }) async {
    return await authenticate(
      localizedReason: 'Authenticate to send $amount $currency to $recipient',
      sensitiveTransaction: true,
      stickyAuth: true,
      biometricOnly: true,
    );
  }
  
  /// Authenticate for app access
  static Future<bool> authenticateForAppAccess() async {
    return await authenticate(
      localizedReason: 'Authenticate to access KryptoPesa',
      useErrorDialogs: true,
      stickyAuth: false,
    );
  }
  
  /// Authenticate for settings change
  static Future<bool> authenticateForSettings() async {
    return await authenticate(
      localizedReason: 'Authenticate to change security settings',
      sensitiveTransaction: true,
    );
  }
  
  /// Check if biometric authentication is enabled
  static Future<bool> isBiometricEnabled() async {
    final biometricKey = await SecureStorage.getBiometricKey();
    return biometricKey != null;
  }
  
  /// Enable biometric authentication
  static Future<bool> enableBiometric() async {
    if (!_isAvailable) return false;
    
    // First authenticate to confirm user identity
    final authenticated = await authenticate(
      localizedReason: 'Authenticate to enable biometric login',
      sensitiveTransaction: true,
    );
    
    if (!authenticated) return false;
    
    // Generate and store biometric key
    final biometricKey = _generateBiometricKey();
    await SecureStorage.storeBiometricKey(biometricKey);
    
    AppLogger.biometric('Biometric authentication enabled', true);
    return true;
  }
  
  /// Disable biometric authentication
  static Future<bool> disableBiometric() async {
    // Authenticate before disabling
    final authenticated = await authenticate(
      localizedReason: 'Authenticate to disable biometric login',
      sensitiveTransaction: true,
    );
    
    if (!authenticated) return false;
    
    // Remove biometric key
    await SecureStorage.delete('biometric_key');
    
    AppLogger.biometric('Biometric authentication disabled', true);
    return true;
  }
  
  /// Get last authentication time
  static Future<DateTime?> getLastAuthenticationTime() async {
    final timestamp = await SecureStorage.read('last_biometric_auth');
    if (timestamp == null) return null;
    
    try {
      return DateTime.parse(timestamp);
    } catch (e) {
      return null;
    }
  }
  
  /// Check if recent authentication is valid
  static Future<bool> isRecentAuthenticationValid({
    Duration validDuration = const Duration(minutes: 5),
  }) async {
    final lastAuth = await getLastAuthenticationTime();
    if (lastAuth == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(lastAuth);
    
    return difference <= validDuration;
  }
  
  /// Get biometric status message
  static Future<String> getBiometricStatusMessage() async {
    if (!_isInitialized) {
      return 'Biometric service not initialized';
    }
    
    if (!_isAvailable) {
      return 'Biometric authentication is not available on this device';
    }
    
    if (_availableBiometrics.isEmpty) {
      return 'No biometric methods are enrolled on this device';
    }
    
    final enabled = await isBiometricEnabled();
    if (!enabled) {
      return 'Biometric authentication is available but not enabled';
    }
    
    final types = <String>[];
    if (isFingerprintAvailable) types.add('Fingerprint');
    if (isFaceAvailable) types.add('Face ID');
    if (isIrisAvailable) types.add('Iris');
    
    return 'Biometric authentication is enabled (${types.join(', ')})';
  }
  
  /// Generate biometric key
  static String _generateBiometricKey() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp.toString().split('').reversed.join();
    return 'bio_key_$random';
  }
  
  /// Stop biometric authentication (cancel ongoing)
  static Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
      AppLogger.biometric('Authentication stopped', true);
    } catch (error) {
      AppLogger.error('Failed to stop biometric authentication', error);
    }
  }
  
  /// Check if biometric service is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Get supported biometric types as strings
  static List<String> getSupportedBiometricTypes() {
    return _availableBiometrics.map((type) {
      switch (type) {
        case BiometricType.face:
          return 'Face ID';
        case BiometricType.fingerprint:
          return 'Fingerprint';
        case BiometricType.iris:
          return 'Iris';
        case BiometricType.strong:
          return 'Strong Biometric';
        case BiometricType.weak:
          return 'Weak Biometric';
        default:
          return 'Unknown';
      }
    }).toList();
  }
}
