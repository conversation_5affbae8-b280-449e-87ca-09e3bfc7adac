import 'package:flutter/material.dart';
import 'enhanced_loading_states.dart';

/// Skeleton for Trading Offer Card
class TradingOfferSkeleton extends StatelessWidget {
  const TradingOfferSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SkeletonLoader(
                  width: 40,
                  height: 40,
                  borderRadius: BorderRadius.circular(20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonLoader(width: 120, height: 16),
                      const Si<PERSON><PERSON><PERSON>(height: 4),
                      Skeleton<PERSON>oa<PERSON>(width: 80, height: 14),
                    ],
                  ),
                ),
                SkeletonLoader(width: 60, height: 24, borderRadius: BorderRadius.circular(12)),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonLoader(width: 60, height: 12),
                    const SizedBox(height: 4),
                    SkeletonLoader(width: 80, height: 16),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    SkeletonLoader(width: 60, height: 12),
                    const SizedBox(height: 4),
                    SkeletonLoader(width: 100, height: 16),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Skeleton for Wallet Balance Card
class WalletBalanceSkeleton extends StatelessWidget {
  const WalletBalanceSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SkeletonLoader(width: 100, height: 16),
                SkeletonLoader(width: 60, height: 24, borderRadius: BorderRadius.circular(12)),
              ],
            ),
            const SizedBox(height: 16),
            SkeletonLoader(width: 150, height: 32),
            const SizedBox(height: 8),
            SkeletonLoader(width: 100, height: 14),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: SkeletonLoader(
                    height: 40,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SkeletonLoader(
                    height: 40,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Skeleton for Transaction Item
class TransactionSkeleton extends StatelessWidget {
  const TransactionSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: SkeletonLoader(
        width: 40,
        height: 40,
        borderRadius: BorderRadius.circular(20),
      ),
      title: SkeletonLoader(width: 120, height: 16),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          SkeletonLoader(width: 80, height: 14),
          const SizedBox(height: 2),
          SkeletonLoader(width: 100, height: 12),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SkeletonLoader(width: 60, height: 16),
          const SizedBox(height: 4),
          SkeletonLoader(width: 40, height: 12),
        ],
      ),
    );
  }
}

/// Skeleton for Chat Message
class ChatMessageSkeleton extends StatelessWidget {
  final bool isMe;
  
  const ChatMessageSkeleton({super.key, this.isMe = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) ...[
            SkeletonLoader(
              width: 32,
              height: 32,
              borderRadius: BorderRadius.circular(16),
            ),
            const SizedBox(width: 8),
          ],
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.7,
            ),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonLoader(width: 100, height: 14),
                const SizedBox(height: 4),
                SkeletonLoader(width: 150, height: 14),
                const SizedBox(height: 8),
                SkeletonLoader(width: 60, height: 12),
              ],
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            SkeletonLoader(
              width: 32,
              height: 32,
              borderRadius: BorderRadius.circular(16),
            ),
          ],
        ],
      ),
    );
  }
}

/// Skeleton for Profile Stats
class ProfileStatsSkeleton extends StatelessWidget {
  const ProfileStatsSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            SkeletonLoader(
              width: 80,
              height: 80,
              borderRadius: BorderRadius.circular(40),
            ),
            const SizedBox(height: 16),
            SkeletonLoader(width: 120, height: 20),
            const SizedBox(height: 8),
            SkeletonLoader(width: 80, height: 14),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    SkeletonLoader(width: 40, height: 24),
                    const SizedBox(height: 4),
                    SkeletonLoader(width: 60, height: 12),
                  ],
                ),
                Column(
                  children: [
                    SkeletonLoader(width: 40, height: 24),
                    const SizedBox(height: 4),
                    SkeletonLoader(width: 60, height: 12),
                  ],
                ),
                Column(
                  children: [
                    SkeletonLoader(width: 40, height: 24),
                    const SizedBox(height: 4),
                    SkeletonLoader(width: 60, height: 12),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Generic List Skeleton
class ListSkeleton extends StatelessWidget {
  final int itemCount;
  final Widget Function(int index) itemBuilder;

  const ListSkeleton({
    super.key,
    this.itemCount = 5,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) => itemBuilder(index),
    );
  }
}

/// Loading State Wrapper
class LoadingStateWrapper extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final Widget loadingWidget;
  final String? errorMessage;
  final VoidCallback? onRetry;

  const LoadingStateWrapper({
    super.key,
    required this.isLoading,
    required this.child,
    required this.loadingWidget,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null) {
      return _buildErrorState(context);
    }
    
    if (isLoading) {
      return loadingWidget;
    }
    
    return child;
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E3A8A),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
