import 'package:json_annotation/json_annotation.dart';
import 'user_model.dart';

part 'auth_models.g.dart';

// Login Request
@JsonSerializable()
class LoginRequest {
  final String identifier; // email, username, or phone
  final String password;

  const LoginRequest({
    required this.identifier,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

// Register Request
@JsonSerializable()
class RegisterRequest {
  final String username;
  final String email;
  final String phone;
  final String password;
  final String firstName;
  final String lastName;
  final String country;
  final String city;

  const RegisterRequest({
    required this.username,
    required this.email,
    required this.phone,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.country,
    required this.city,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

// Reset Password Request
@JsonSerializable()
class ResetPasswordRequest {
  final String token;
  final String newPassword;

  const ResetPasswordRequest({
    required this.token,
    required this.newPassword,
  });

  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) => _$ResetPasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ResetPasswordRequestToJson(this);
}

// Change Password Request
@JsonSerializable()
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) => _$ChangePasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);
}

// Auth Response
@JsonSerializable()
class AuthResponse {
  final User? user;
  final String? token;
  final String? refreshToken;
  final DateTime? expiresAt;

  const AuthResponse({
    this.user,
    this.token,
    this.refreshToken,
    this.expiresAt,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) => _$AuthResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

// Auth Result
class AuthResult {
  final bool success;
  final String message;
  final User? user;
  final String? token;
  final Map<String, dynamic>? errors;

  const AuthResult({
    required this.success,
    required this.message,
    this.user,
    this.token,
    this.errors,
  });

  factory AuthResult.success({
    User? user,
    String? token,
    String? message,
  }) {
    return AuthResult(
      success: true,
      message: message ?? 'Operation successful',
      user: user,
      token: token,
    );
  }

  factory AuthResult.failure({
    required String message,
    Map<String, dynamic>? errors,
  }) {
    return AuthResult(
      success: false,
      message: message,
      errors: errors,
    );
  }

  bool get hasErrors => errors != null && errors!.isNotEmpty;
  
  List<String> get errorMessages {
    if (!hasErrors) return [];
    
    final messages = <String>[];
    errors!.forEach((key, value) {
      if (value is String) {
        messages.add(value);
      } else if (value is List) {
        messages.addAll(value.cast<String>());
      }
    });
    return messages;
  }
}

// Biometric Login Request
@JsonSerializable()
class BiometricLoginRequest {
  final String userId;
  final String biometricData;
  final String deviceId;

  const BiometricLoginRequest({
    required this.userId,
    required this.biometricData,
    required this.deviceId,
  });

  factory BiometricLoginRequest.fromJson(Map<String, dynamic> json) => _$BiometricLoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BiometricLoginRequestToJson(this);
}

// Email Verification Request
@JsonSerializable()
class EmailVerificationRequest {
  final String token;

  const EmailVerificationRequest({
    required this.token,
  });

  factory EmailVerificationRequest.fromJson(Map<String, dynamic> json) => _$EmailVerificationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$EmailVerificationRequestToJson(this);
}

// Phone Verification Request
@JsonSerializable()
class PhoneVerificationRequest {
  final String code;

  const PhoneVerificationRequest({
    required this.code,
  });

  factory PhoneVerificationRequest.fromJson(Map<String, dynamic> json) => _$PhoneVerificationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PhoneVerificationRequestToJson(this);
}

// Update Profile Request
@JsonSerializable()
class UpdateProfileRequest {
  final String? firstName;
  final String? lastName;
  final String? bio;
  final String? city;
  final String? preferredLanguage;

  const UpdateProfileRequest({
    this.firstName,
    this.lastName,
    this.bio,
    this.city,
    this.preferredLanguage,
  });

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) => _$UpdateProfileRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateProfileRequestToJson(this);
}

// Update Preferences Request
@JsonSerializable()
class UpdatePreferencesRequest {
  final NotificationPreferences? notifications;
  final TradingPreferences? trading;

  const UpdatePreferencesRequest({
    this.notifications,
    this.trading,
  });

  factory UpdatePreferencesRequest.fromJson(Map<String, dynamic> json) => _$UpdatePreferencesRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePreferencesRequestToJson(this);
}

// Identity Verification Request
@JsonSerializable()
class IdentityVerificationRequest {
  final String documentType;
  final String documentNumber;
  final List<String> documentImages;

  const IdentityVerificationRequest({
    required this.documentType,
    required this.documentNumber,
    required this.documentImages,
  });

  factory IdentityVerificationRequest.fromJson(Map<String, dynamic> json) => _$IdentityVerificationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$IdentityVerificationRequestToJson(this);
}

// Two Factor Auth Setup Request
@JsonSerializable()
class TwoFactorSetupRequest {
  final String secret;
  final String code;

  const TwoFactorSetupRequest({
    required this.secret,
    required this.code,
  });

  factory TwoFactorSetupRequest.fromJson(Map<String, dynamic> json) => _$TwoFactorSetupRequestFromJson(json);
  Map<String, dynamic> toJson() => _$TwoFactorSetupRequestToJson(this);
}

// Two Factor Auth Verify Request
@JsonSerializable()
class TwoFactorVerifyRequest {
  final String code;

  const TwoFactorVerifyRequest({
    required this.code,
  });

  factory TwoFactorVerifyRequest.fromJson(Map<String, dynamic> json) => _$TwoFactorVerifyRequestFromJson(json);
  Map<String, dynamic> toJson() => _$TwoFactorVerifyRequestToJson(this);
}

// Session Info
@JsonSerializable()
class SessionInfo {
  final String deviceId;
  final String deviceName;
  final String ipAddress;
  final String userAgent;
  final DateTime lastActive;
  final bool isCurrent;

  const SessionInfo({
    required this.deviceId,
    required this.deviceName,
    required this.ipAddress,
    required this.userAgent,
    required this.lastActive,
    required this.isCurrent,
  });

  factory SessionInfo.fromJson(Map<String, dynamic> json) => _$SessionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$SessionInfoToJson(this);
}

// Login History
@JsonSerializable()
class LoginHistory {
  final String id;
  final String deviceId;
  final String deviceName;
  final String ipAddress;
  final String userAgent;
  final String location;
  final DateTime timestamp;
  final bool successful;
  final String? failureReason;

  const LoginHistory({
    required this.id,
    required this.deviceId,
    required this.deviceName,
    required this.ipAddress,
    required this.userAgent,
    required this.location,
    required this.timestamp,
    required this.successful,
    this.failureReason,
  });

  factory LoginHistory.fromJson(Map<String, dynamic> json) => _$LoginHistoryFromJson(json);
  Map<String, dynamic> toJson() => _$LoginHistoryToJson(this);
}
