import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String username;
  final String email;
  final String phone;
  final UserProfile profile;
  final UserVerification verification;
  final UserReputation reputation;
  final UserPreferences preferences;
  final UserSecurity security;
  final String status;
  final String role;
  final DateTime? lastActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.phone,
    required this.profile,
    required this.verification,
    required this.reputation,
    required this.preferences,
    required this.security,
    required this.status,
    required this.role,
    this.lastActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? username,
    String? email,
    String? phone,
    UserProfile? profile,
    UserVerification? verification,
    UserReputation? reputation,
    UserPreferences? preferences,
    UserSecurity? security,
    String? status,
    String? role,
    DateTime? lastActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      profile: profile ?? this.profile,
      verification: verification ?? this.verification,
      reputation: reputation ?? this.reputation,
      preferences: preferences ?? this.preferences,
      security: security ?? this.security,
      status: status ?? this.status,
      role: role ?? this.role,
      lastActive: lastActive ?? this.lastActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isActive => status == 'active';
  bool get isVerified => verification.email.verified && verification.phone.verified;
  bool get isIdentityVerified => verification.identity.verified;
  bool get isAdmin => role == 'admin';
  bool get isModerator => role == 'moderator';
}

@JsonSerializable()
class UserProfile {
  final String firstName;
  final String lastName;
  final String? avatar;
  final String bio;
  final UserLocation location;
  final String preferredLanguage;

  const UserProfile({
    required this.firstName,
    required this.lastName,
    this.avatar,
    required this.bio,
    required this.location,
    required this.preferredLanguage,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  String get fullName => '$firstName $lastName';
  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';
}

@JsonSerializable()
class UserLocation {
  final String country;
  final String city;

  const UserLocation({
    required this.country,
    required this.city,
  });

  factory UserLocation.fromJson(Map<String, dynamic> json) => _$UserLocationFromJson(json);
  Map<String, dynamic> toJson() => _$UserLocationToJson(this);
}

@JsonSerializable()
class UserVerification {
  final EmailVerification email;
  final PhoneVerification phone;
  final IdentityVerification identity;

  const UserVerification({
    required this.email,
    required this.phone,
    required this.identity,
  });

  factory UserVerification.fromJson(Map<String, dynamic> json) => _$UserVerificationFromJson(json);
  Map<String, dynamic> toJson() => _$UserVerificationToJson(this);
}

@JsonSerializable()
class EmailVerification {
  final bool verified;
  final String? token;
  final DateTime? expiresAt;

  const EmailVerification({
    required this.verified,
    this.token,
    this.expiresAt,
  });

  factory EmailVerification.fromJson(Map<String, dynamic> json) => _$EmailVerificationFromJson(json);
  Map<String, dynamic> toJson() => _$EmailVerificationToJson(this);
}

@JsonSerializable()
class PhoneVerification {
  final bool verified;
  final String? code;
  final DateTime? expiresAt;

  const PhoneVerification({
    required this.verified,
    this.code,
    this.expiresAt,
  });

  factory PhoneVerification.fromJson(Map<String, dynamic> json) => _$PhoneVerificationFromJson(json);
  Map<String, dynamic> toJson() => _$PhoneVerificationToJson(this);
}

@JsonSerializable()
class IdentityVerification {
  final bool verified;
  final String? documentType;
  final String? documentNumber;
  final List<String> documentImages;
  final DateTime? verifiedAt;
  final String? verifiedBy;

  const IdentityVerification({
    required this.verified,
    this.documentType,
    this.documentNumber,
    required this.documentImages,
    this.verifiedAt,
    this.verifiedBy,
  });

  factory IdentityVerification.fromJson(Map<String, dynamic> json) => _$IdentityVerificationFromJson(json);
  Map<String, dynamic> toJson() => _$IdentityVerificationToJson(this);
}

@JsonSerializable()
class UserReputation {
  final double score;
  final int totalTrades;
  final int completedTrades;
  final int cancelledTrades;
  final int disputedTrades;
  final double averageRating;
  final int totalRatings;

  const UserReputation({
    required this.score,
    required this.totalTrades,
    required this.completedTrades,
    required this.cancelledTrades,
    required this.disputedTrades,
    required this.averageRating,
    required this.totalRatings,
  });

  factory UserReputation.fromJson(Map<String, dynamic> json) => _$UserReputationFromJson(json);
  Map<String, dynamic> toJson() => _$UserReputationToJson(this);

  double get completionRate => totalTrades > 0 ? (completedTrades / totalTrades) * 100 : 0;
  double get cancellationRate => totalTrades > 0 ? (cancelledTrades / totalTrades) * 100 : 0;
  double get disputeRate => totalTrades > 0 ? (disputedTrades / totalTrades) * 100 : 0;
}

@JsonSerializable()
class UserPreferences {
  final NotificationPreferences notifications;
  final TradingPreferences trading;

  const UserPreferences({
    required this.notifications,
    required this.trading,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$UserPreferencesToJson(this);
}

@JsonSerializable()
class NotificationPreferences {
  final bool email;
  final bool push;
  final bool sms;

  const NotificationPreferences({
    required this.email,
    required this.push,
    required this.sms,
  });

  factory NotificationPreferences.fromJson(Map<String, dynamic> json) => _$NotificationPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$NotificationPreferencesToJson(this);
}

@JsonSerializable()
class TradingPreferences {
  final bool autoAcceptOffers;
  final double maxTradeAmount;
  final List<String> preferredPaymentMethods;

  const TradingPreferences({
    required this.autoAcceptOffers,
    required this.maxTradeAmount,
    required this.preferredPaymentMethods,
  });

  factory TradingPreferences.fromJson(Map<String, dynamic> json) => _$TradingPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$TradingPreferencesToJson(this);
}

@JsonSerializable()
class UserSecurity {
  final bool twoFactorEnabled;
  final DateTime? lastLogin;
  final int loginAttempts;
  final DateTime? lockUntil;

  const UserSecurity({
    required this.twoFactorEnabled,
    this.lastLogin,
    required this.loginAttempts,
    this.lockUntil,
  });

  factory UserSecurity.fromJson(Map<String, dynamic> json) => _$UserSecurityFromJson(json);
  Map<String, dynamic> toJson() => _$UserSecurityToJson(this);

  bool get isLocked => lockUntil != null && lockUntil!.isAfter(DateTime.now());
}
