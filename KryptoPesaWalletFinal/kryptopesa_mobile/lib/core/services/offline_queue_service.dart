import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';
import '../utils/logger.dart';
import 'local_database_service.dart';
import 'connectivity_service.dart';

enum OperationType {
  userUpdate,
  walletSync,
  transaction,
  tradeAction,
  messageSync,
  fileUpload,
  other,
}

enum OperationPriority {
  low(0),
  normal(1),
  high(2),
  critical(3);

  const OperationPriority(this.value);
  final int value;
}

class QueuedOperation {
  final String id;
  final OperationType type;
  final String endpoint;
  final String method;
  final Map<String, dynamic>? data;
  final Map<String, String>? headers;
  final OperationPriority priority;
  final int maxRetries;
  final DateTime createdAt;
  final DateTime? scheduledAt;
  int retryCount;
  String? errorMessage;
  String status;

  QueuedOperation({
    required this.id,
    required this.type,
    required this.endpoint,
    required this.method,
    this.data,
    this.headers,
    this.priority = OperationPriority.normal,
    this.maxRetries = 3,
    required this.createdAt,
    this.scheduledAt,
    this.retryCount = 0,
    this.errorMessage,
    this.status = 'pending',
  });

  factory QueuedOperation.fromMap(Map<String, dynamic> map) {
    return QueuedOperation(
      id: map['operation_id'],
      type: OperationType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => OperationType.other,
      ),
      endpoint: map['endpoint'],
      method: map['method'],
      data: map['data'] != null ? json.decode(map['data']) : null,
      headers: map['headers'] != null ? 
          Map<String, String>.from(json.decode(map['headers'])) : null,
      priority: OperationPriority.values.firstWhere(
        (e) => e.value == map['priority'],
        orElse: () => OperationPriority.normal,
      ),
      maxRetries: map['max_retries'] ?? 3,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      scheduledAt: map['scheduled_at'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['scheduled_at'])
          : null,
      retryCount: map['retry_count'] ?? 0,
      errorMessage: map['error_message'],
      status: map['status'] ?? 'pending',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'operation_id': id,
      'type': type.name,
      'endpoint': endpoint,
      'method': method,
      'data': data != null ? json.encode(data) : null,
      'headers': headers != null ? json.encode(headers) : null,
      'priority': priority.value,
      'max_retries': maxRetries,
      'created_at': createdAt.millisecondsSinceEpoch,
      'scheduled_at': scheduledAt?.millisecondsSinceEpoch,
      'retry_count': retryCount,
      'error_message': errorMessage,
      'status': status,
    };
  }

  bool get canRetry => retryCount < maxRetries && status != 'completed';
  bool get isExpired => DateTime.now().difference(createdAt).inDays > 7;
  
  Duration get nextRetryDelay {
    // Exponential backoff: 2^retryCount seconds (max 5 minutes)
    final delaySeconds = min(pow(2, retryCount).toInt(), 300);
    return Duration(seconds: delaySeconds);
  }
}

class OfflineQueueService {
  static OfflineQueueService? _instance;
  static OfflineQueueService get instance => _instance ??= OfflineQueueService._();
  
  OfflineQueueService._();

  final LocalDatabaseService _database = LocalDatabaseService.instance;
  final ConnectivityService _connectivity = ConnectivityService.instance;
  final Dio _dio = Dio();
  final Uuid _uuid = const Uuid();

  Timer? _processingTimer;
  bool _isProcessing = false;
  StreamSubscription<ConnectionStatus>? _connectivitySubscription;

  static const Duration _processingInterval = Duration(seconds: 10);
  static const int _maxConcurrentOperations = 3;

  /// Initialize the offline queue service
  Future<void> initialize() async {
    try {
      AppLogger.info('Initializing offline queue service...');

      // Configure Dio
      _dio.options.connectTimeout = const Duration(seconds: 30);
      _dio.options.receiveTimeout = const Duration(seconds: 30);
      _dio.options.sendTimeout = const Duration(seconds: 30);

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.statusStream.listen(
        _onConnectivityChanged,
      );

      // Start processing timer
      _startProcessingTimer();

      // Process any existing operations
      if (_connectivity.isConnected) {
        _processQueue();
      }

      AppLogger.info('Offline queue service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize offline queue service', e);
    }
  }

  /// Add operation to queue
  Future<String> queueOperation({
    required OperationType type,
    required String endpoint,
    required String method,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    OperationPriority priority = OperationPriority.normal,
    int maxRetries = 3,
    DateTime? scheduledAt,
  }) async {
    try {
      final operationId = _uuid.v4();
      
      await _database.addPendingOperation(
        operationId: operationId,
        type: type.name,
        endpoint: endpoint,
        method: method,
        data: data,
        headers: headers,
        priority: priority.value,
        maxRetries: maxRetries,
        scheduledAt: scheduledAt,
      );

      AppLogger.info('Queued operation: $type -> $endpoint');

      // Try to process immediately if connected
      if (_connectivity.isConnected && !_isProcessing) {
        _processQueue();
      }

      return operationId;
    } catch (e) {
      AppLogger.error('Failed to queue operation', e);
      rethrow;
    }
  }

  /// Process the operation queue
  Future<void> _processQueue() async {
    if (_isProcessing || !_connectivity.isConnected) return;

    _isProcessing = true;
    
    try {
      AppLogger.debug('Processing operation queue...');

      final operations = await _getPendingOperations();
      if (operations.isEmpty) {
        AppLogger.debug('No pending operations to process');
        return;
      }

      AppLogger.info('Processing ${operations.length} pending operations');

      // Process operations concurrently with limit
      final futures = <Future>[];
      for (int i = 0; i < min(operations.length, _maxConcurrentOperations); i++) {
        futures.add(_processOperation(operations[i]));
      }

      await Future.wait(futures);

      // If there are more operations, schedule next processing
      if (operations.length > _maxConcurrentOperations) {
        Timer(const Duration(seconds: 2), () => _processQueue());
      }

    } catch (e) {
      AppLogger.error('Error processing queue', e);
    } finally {
      _isProcessing = false;
    }
  }

  /// Get pending operations from database
  Future<List<QueuedOperation>> _getPendingOperations() async {
    try {
      final rawOperations = await _database.getPendingOperations(
        limit: _maxConcurrentOperations * 2,
      );

      return rawOperations
          .map((op) => QueuedOperation.fromMap(op))
          .where((op) => !op.isExpired)
          .toList();
    } catch (e) {
      AppLogger.error('Failed to get pending operations', e);
      return [];
    }
  }

  /// Process a single operation
  Future<void> _processOperation(QueuedOperation operation) async {
    try {
      AppLogger.debug('Processing operation: ${operation.id}');

      // Check if operation is scheduled for future
      if (operation.scheduledAt != null && 
          DateTime.now().isBefore(operation.scheduledAt!)) {
        AppLogger.debug('Operation ${operation.id} is scheduled for future');
        return;
      }

      // Update status to processing
      await _database.updateOperationStatus(operation.id, 'processing');

      // Execute the operation
      final response = await _executeOperation(operation);

      if (response.statusCode != null && 
          response.statusCode! >= 200 && 
          response.statusCode! < 300) {
        // Success
        await _database.updateOperationStatus(operation.id, 'completed');
        await _database.removeOperation(operation.id);
        
        AppLogger.info('Operation completed successfully: ${operation.id}');
      } else {
        // HTTP error
        await _handleOperationFailure(
          operation, 
          'HTTP ${response.statusCode}: ${response.statusMessage}',
        );
      }

    } catch (e) {
      await _handleOperationFailure(operation, e.toString());
    }
  }

  /// Execute the actual HTTP operation
  Future<Response> _executeOperation(QueuedOperation operation) async {
    final options = Options(
      method: operation.method,
      headers: operation.headers,
    );

    switch (operation.method.toUpperCase()) {
      case 'GET':
        return await _dio.get(
          operation.endpoint,
          queryParameters: operation.data,
          options: options,
        );
      case 'POST':
        return await _dio.post(
          operation.endpoint,
          data: operation.data,
          options: options,
        );
      case 'PUT':
        return await _dio.put(
          operation.endpoint,
          data: operation.data,
          options: options,
        );
      case 'PATCH':
        return await _dio.patch(
          operation.endpoint,
          data: operation.data,
          options: options,
        );
      case 'DELETE':
        return await _dio.delete(
          operation.endpoint,
          data: operation.data,
          options: options,
        );
      default:
        throw UnsupportedError('HTTP method ${operation.method} not supported');
    }
  }

  /// Handle operation failure
  Future<void> _handleOperationFailure(
    QueuedOperation operation, 
    String errorMessage,
  ) async {
    operation.retryCount++;
    operation.errorMessage = errorMessage;

    if (operation.canRetry) {
      // Schedule retry with exponential backoff
      final retryAt = DateTime.now().add(operation.nextRetryDelay);
      
      await _database.updateOperationStatus(
        operation.id,
        'pending',
        errorMessage: errorMessage,
        retryCount: operation.retryCount,
      );

      AppLogger.warning(
        'Operation ${operation.id} failed, retry ${operation.retryCount}/${operation.maxRetries} '
        'scheduled for ${retryAt.toIso8601String()}: $errorMessage'
      );
    } else {
      // Max retries reached, mark as failed
      await _database.updateOperationStatus(
        operation.id,
        'failed',
        errorMessage: errorMessage,
        retryCount: operation.retryCount,
      );

      AppLogger.error(
        'Operation ${operation.id} failed permanently after ${operation.retryCount} retries: $errorMessage'
      );
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectionStatus status) {
    if (status.isConnected && status.quality.index >= ConnectionQuality.fair.index) {
      AppLogger.info('Connection restored, processing queue...');
      _processQueue();
    }
  }

  /// Start processing timer
  void _startProcessingTimer() {
    _processingTimer?.cancel();
    _processingTimer = Timer.periodic(_processingInterval, (timer) {
      if (_connectivity.isConnected && !_isProcessing) {
        _processQueue();
      }
    });
  }

  /// Get queue statistics
  Future<Map<String, dynamic>> getQueueStats() async {
    try {
      final stats = await _database.getDatabaseStats();
      return {
        'pending_operations': stats['pending_operations'] ?? 0,
        'is_processing': _isProcessing,
        'is_connected': _connectivity.isConnected,
        'connection_quality': _connectivity.currentStatus.quality.name,
      };
    } catch (e) {
      AppLogger.error('Failed to get queue stats', e);
      return {};
    }
  }

  /// Clear failed operations
  Future<void> clearFailedOperations() async {
    try {
      // This would require a database method to delete failed operations
      AppLogger.info('Clearing failed operations...');
      // TODO: Implement in database service
    } catch (e) {
      AppLogger.error('Failed to clear failed operations', e);
    }
  }

  /// Force process queue (for manual sync)
  Future<void> forceSync() async {
    AppLogger.info('Force sync requested...');
    
    if (!_connectivity.isConnected) {
      throw Exception('No internet connection available');
    }

    await _processQueue();
  }

  /// Dispose resources
  void dispose() {
    _processingTimer?.cancel();
    _connectivitySubscription?.cancel();
    _dio.close();
    
    AppLogger.info('Offline queue service disposed');
  }
}
