import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../utils/logger.dart';

/// Navigation Service
/// Provides centralized navigation management
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static GoRouter? _router;
  
  /// Set the router instance
  static void setRouter(GoRouter router) {
    _router = router;
  }
  
  /// Get current context
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// Navigate to a named route
  static Future<T?> navigateTo<T extends Object?>(String routeName, {Object? extra}) async {
    if (_router == null) {
      AppLogger.error('Router not initialized');
      return null;
    }
    
    AppLogger.navigation('Current', routeName);
    _router!.go(routeName, extra: extra);
    return null;
  }
  
  /// Push a named route
  static Future<T?> pushTo<T extends Object?>(String routeName, {Object? extra}) async {
    if (_router == null) {
      AppLogger.error('Router not initialized');
      return null;
    }
    
    AppLogger.navigation('Current', routeName);
    return _router!.push<T>(routeName, extra: extra);
  }
  
  /// Replace current route
  static void replaceTo(String routeName, {Object? extra}) {
    if (_router == null) {
      AppLogger.error('Router not initialized');
      return;
    }
    
    AppLogger.navigation('Current', routeName);
    _router!.pushReplacement(routeName, extra: extra);
  }
  
  /// Go back
  static void goBack<T extends Object?>([T? result]) {
    if (_router == null) {
      AppLogger.error('Router not initialized');
      return;
    }
    
    if (_router!.canPop()) {
      AppLogger.navigation('Back', 'Previous');
      _router!.pop(result);
    }
  }
  
  /// Pop until a specific route
  static void popUntil(String routeName) {
    if (currentContext == null) return;
    
    Navigator.of(currentContext!).popUntil((route) {
      return route.settings.name == routeName;
    });
    
    AppLogger.navigation('PopUntil', routeName);
  }
  
  /// Clear stack and navigate to route
  static void clearAndNavigateTo(String routeName, {Object? extra}) {
    if (_router == null) {
      AppLogger.error('Router not initialized');
      return;
    }
    
    AppLogger.navigation('ClearAndNavigate', routeName);
    _router!.go(routeName, extra: extra);
  }
  
  /// Show modal bottom sheet
  static Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) async {
    if (currentContext == null) return null;
    
    return showModalBottomSheet<T>(
      context: currentContext!,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      builder: (context) => child,
    );
  }
  
  /// Show dialog
  static Future<T?> showAppDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) async {
    if (currentContext == null) return null;

    return showDialog<T>(
      context: currentContext!,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      builder: (context) => child,
    );
  }
  
  /// Show snack bar
  static void showSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    Color? backgroundColor,
    Color? textColor,
  }) {
    if (currentContext == null) return;
    
    final snackBar = SnackBar(
      content: Text(
        message,
        style: TextStyle(color: textColor),
      ),
      duration: duration,
      action: action,
      backgroundColor: backgroundColor,
    );
    
    ScaffoldMessenger.of(currentContext!).showSnackBar(snackBar);
  }
  
  /// Show success snack bar
  static void showSuccessSnackBar(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }
  
  /// Show error snack bar
  static void showErrorSnackBar(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      duration: const Duration(seconds: 5),
    );
  }
  
  /// Show warning snack bar
  static void showWarningSnackBar(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }
  
  /// Show info snack bar
  static void showInfoSnackBar(String message) {
    showSnackBar(
      message: message,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
  
  /// Navigate to onboarding
  static void navigateToOnboarding() {
    clearAndNavigateTo('/onboarding');
  }
  
  /// Navigate to login
  static void navigateToLogin() {
    navigateTo('/auth/login');
  }
  
  /// Navigate to register
  static void navigateToRegister() {
    navigateTo('/auth/register');
  }
  
  /// Navigate to home
  static void navigateToHome() {
    clearAndNavigateTo('/home');
  }
  
  /// Navigate to wallet
  static void navigateToWallet() {
    navigateTo('/wallet');
  }
  
  /// Navigate to trading
  static void navigateToTrading() {
    navigateTo('/trading');
  }
  
  /// Navigate to chat
  static void navigateToChat([String? chatId]) {
    if (chatId != null) {
      navigateTo('/chat/$chatId');
    } else {
      navigateTo('/chat');
    }
  }
  
  /// Navigate to profile
  static void navigateToProfile() {
    navigateTo('/profile');
  }
  
  /// Navigate to settings
  static void navigateToSettings() {
    navigateTo('/settings');
  }
  
  /// Navigate to QR scanner
  static Future<String?> navigateToQRScanner() async {
    return await pushTo<String>('/qr-scanner');
  }
  
  /// Navigate to trade detail
  static void navigateToTradeDetail(String tradeId) {
    navigateTo('/trading/trade/$tradeId');
  }
  
  /// Navigate to offer detail
  static void navigateToOfferDetail(String offerId) {
    navigateTo('/trading/offer/$offerId');
  }
  
  /// Navigate to transaction detail
  static void navigateToTransactionDetail(String transactionId) {
    navigateTo('/wallet/transaction/$transactionId');
  }
  
  /// Navigate to send crypto
  static void navigateToSendCrypto() {
    navigateTo('/wallet/send');
  }
  
  /// Navigate to receive crypto
  static void navigateToReceiveCrypto() {
    navigateTo('/wallet/receive');
  }
  
  /// Navigate to create offer
  static void navigateToCreateOffer() {
    navigateTo('/trading/create-offer');
  }
  
  /// Navigate to security settings
  static void navigateToSecuritySettings() {
    navigateTo('/settings/security');
  }
  
  /// Navigate to biometric setup
  static void navigateToBiometricSetup() {
    navigateTo('/biometric-setup');
  }
  
  /// Navigate to backup wallet
  static void navigateToBackupWallet() {
    navigateTo('/backup-wallet');
  }
  
  /// Navigate to restore wallet
  static void navigateToRestoreWallet() {
    navigateTo('/restore-wallet');
  }
  
  /// Check if can go back
  static bool canGoBack() {
    return _router?.canPop() ?? false;
  }
  
  /// Get current route name
  static String? getCurrentRouteName() {
    return _router?.routerDelegate.currentConfiguration.last.matchedLocation;
  }
}
