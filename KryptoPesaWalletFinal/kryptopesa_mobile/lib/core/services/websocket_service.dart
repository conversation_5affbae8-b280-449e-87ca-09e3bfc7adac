import 'dart:async';
import 'dart:convert';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../storage/local_storage.dart';
import '../utils/logger.dart';

/// WebSocket Service
/// Handles real-time communication with the backend
class WebSocketService {
  static IO.Socket? _socket;
  static bool _isInitialized = false;
  static bool _isConnected = false;
  static Timer? _reconnectTimer;
  static int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  
  // Event controllers
  static final StreamController<Map<String, dynamic>> _tradeUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _chatMessageController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _walletUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _systemUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _typingController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<Map<String, dynamic>> _userStatusController =
      StreamController<Map<String, dynamic>>.broadcast();
  static final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();
  
  /// Initialize WebSocket service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing WebSocket service...');
      
      await _connect();
      
      // Listen for connectivity changes
      Connectivity().onConnectivityChanged.listen(_onConnectivityChanged);
      
      _isInitialized = true;
      AppLogger.info('WebSocket service initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize WebSocket service', error, stackTrace);
      rethrow;
    }
  }
  
  /// Connect to WebSocket server
  static Future<void> _connect() async {
    if (_socket != null && _socket!.connected) return;
    
    final wsUrl = dotenv.env['WS_BASE_URL'] ?? 'ws://localhost:3000';
    final token = LocalStorage.getAuthToken();
    
    _socket = IO.io(wsUrl, IO.OptionBuilder()
        .setTransports(['websocket'])
        .enableAutoConnect()
        .setAuth({
          'token': token,
        })
        .build());
    
    _setupEventListeners();
    
    AppLogger.websocket('Connecting to WebSocket', {'url': wsUrl});
  }
  
  /// Setup event listeners
  static void _setupEventListeners() {
    if (_socket == null) return;
    
    // Connection events
    _socket!.onConnect((_) {
      _isConnected = true;
      _reconnectAttempts = 0;
      _reconnectTimer?.cancel();
      _connectionController.add(true);
      AppLogger.websocket('Connected', null);

      // Join user room
      final user = LocalStorage.getUser();
      if (user != null) {
        _socket!.emit('join_user_room', {'userId': user['id']});
      }
    });
    
    _socket!.onDisconnect((_) {
      _isConnected = false;
      _connectionController.add(false);
      AppLogger.websocket('Disconnected', null);
      _scheduleReconnect();
    });
    
    _socket!.onConnectError((error) {
      AppLogger.websocket('Connection error', {'error': error.toString()});
      _scheduleReconnect();
    });
    
    // Business logic events
    _socket!.on('trade_update', (data) {
      AppLogger.websocket('Trade update received', data);
      _tradeUpdateController.add(Map<String, dynamic>.from(data));
    });
    
    _socket!.on('chat_message', (data) {
      AppLogger.websocket('Chat message received', data);
      _chatMessageController.add(Map<String, dynamic>.from(data));
    });
    
    _socket!.on('wallet_update', (data) {
      AppLogger.websocket('Wallet update received', data);
      _walletUpdateController.add(Map<String, dynamic>.from(data));
    });
    
    _socket!.on('system_update', (data) {
      AppLogger.websocket('System update received', data);
      _systemUpdateController.add(Map<String, dynamic>.from(data));
    });
    
    _socket!.on('notification', (data) {
      AppLogger.websocket('Notification received', data);
      _handleNotification(Map<String, dynamic>.from(data));
    });

    // Chat-specific events
    _socket!.on('new_message', (data) {
      AppLogger.websocket('New message received', data);
      _chatMessageController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('user_typing', (data) {
      AppLogger.websocket('User typing', data);
      _typingController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('user_joined', (data) {
      AppLogger.websocket('User joined', data);
      _userStatusController.add({
        'type': 'joined',
        ...Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('user_left', (data) {
      AppLogger.websocket('User left', data);
      _userStatusController.add({
        'type': 'left',
        ...Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('user_offline', (data) {
      AppLogger.websocket('User offline', data);
      _userStatusController.add({
        'type': 'offline',
        ...Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('messages_read', (data) {
      AppLogger.websocket('Messages read', data);
      _chatMessageController.add({
        'type': 'messages_read',
        ...Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('joined_trade', (data) {
      AppLogger.websocket('Joined trade room', data);
      _tradeUpdateController.add({
        'type': 'joined_trade',
        ...Map<String, dynamic>.from(data)
      });
    });

    _socket!.on('error', (error) {
      AppLogger.websocket('Socket error', {'error': error.toString()});
    });
  }
  
  /// Handle connectivity changes
  static void _onConnectivityChanged(List<ConnectivityResult> results) {
    if (!results.contains(ConnectivityResult.none) && !_isConnected) {
      AppLogger.websocket('Connectivity restored, reconnecting', null);
      _connect();
    }
  }
  
  /// Schedule reconnection
  static void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      AppLogger.websocket('Max reconnect attempts reached', null);
      return;
    }
    
    _reconnectTimer?.cancel();
    
    final delay = Duration(seconds: 2 << _reconnectAttempts); // Exponential backoff
    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      AppLogger.websocket('Reconnecting', {'attempt': _reconnectAttempts});
      _connect();
    });
  }
  
  /// Handle incoming notifications
  static void _handleNotification(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final title = data['title'] as String?;
    final body = data['body'] as String?;
    
    if (title != null && body != null) {
      // Show local notification
      // NotificationService.showNotification(title: title, body: body);
    }
  }
  
  /// Emit event to server
  static void emit(String event, Map<String, dynamic> data) {
    if (_socket == null || !_isConnected) {
      AppLogger.websocket('Cannot emit, not connected', {'event': event});
      return;
    }
    
    AppLogger.websocket('Emitting event', {'event': event, 'data': data});
    _socket!.emit(event, data);
  }
  
  /// Join a room
  static void joinRoom(String roomId) {
    emit('join_room', {'roomId': roomId});
  }
  
  /// Leave a room
  static void leaveRoom(String roomId) {
    emit('leave_room', {'roomId': roomId});
  }
  
  /// Send chat message
  static void sendChatMessage({
    required String chatId,
    required String message,
    String? messageType,
    Map<String, dynamic>? metadata,
  }) {
    emit('chat_message', {
      'chatId': chatId,
      'message': message,
      'messageType': messageType ?? 'text',
      'metadata': metadata,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Update trade status
  static void updateTradeStatus({
    required String tradeId,
    required String status,
    Map<String, dynamic>? data,
  }) {
    emit('trade_status_update', {
      'tradeId': tradeId,
      'status': status,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  /// Send typing indicator
  static void sendTypingIndicator({
    required String chatId,
    required bool isTyping,
  }) {
    emit('typing', {
      'chatId': chatId,
      'isTyping': isTyping,
    });
  }
  
  /// Request trade updates
  static void requestTradeUpdates(String tradeId) {
    emit('subscribe_trade', {'tradeId': tradeId});
  }
  
  /// Stop trade updates
  static void stopTradeUpdates(String tradeId) {
    emit('unsubscribe_trade', {'tradeId': tradeId});
  }
  
  /// Request wallet updates
  static void requestWalletUpdates() {
    emit('subscribe_wallet', {});
  }
  
  /// Stop wallet updates
  static void stopWalletUpdates() {
    emit('unsubscribe_wallet', {});
  }
  
  /// Stream getters
  static Stream<Map<String, dynamic>> get tradeUpdates => _tradeUpdateController.stream;
  static Stream<Map<String, dynamic>> get chatMessages => _chatMessageController.stream;
  static Stream<Map<String, dynamic>> get walletUpdates => _walletUpdateController.stream;
  static Stream<Map<String, dynamic>> get systemUpdates => _systemUpdateController.stream;
  static Stream<Map<String, dynamic>> get typingIndicators => _typingController.stream;
  static Stream<Map<String, dynamic>> get userStatusUpdates => _userStatusController.stream;
  static Stream<bool> get connectionStatus => _connectionController.stream;
  
  /// Connection status
  static bool get isConnected => _isConnected;
  static bool get isInitialized => _isInitialized;
  
  /// Join trade room for chat
  static Future<bool> joinTradeRoom(String tradeId) async {
    if (!_isConnected || _socket == null) {
      AppLogger.error('Cannot join trade room: not connected');
      return false;
    }

    try {
      emit('join_trade', {'tradeId': tradeId});
      AppLogger.info('Joined trade room: $tradeId');
      return true;
    } catch (e) {
      AppLogger.error('Error joining trade room', e);
      return false;
    }
  }

  /// Leave trade room
  static void leaveTradeRoom(String tradeId) {
    if (!_isConnected || _socket == null) return;
    emit('leave_trade', {'tradeId': tradeId});
    AppLogger.info('Left trade room: $tradeId');
  }

  /// Send trade chat message
  static Future<bool> sendTradeChatMessage({
    required String tradeId,
    required String message,
    String type = 'text',
    String? attachmentUrl,
  }) async {
    if (!_isConnected || _socket == null) {
      AppLogger.error('Cannot send message: not connected');
      return false;
    }

    try {
      emit('send_message', {
        'tradeId': tradeId,
        'message': message,
        'type': type,
        'attachmentUrl': attachmentUrl,
      });
      AppLogger.info('Message sent to trade: $tradeId');
      return true;
    } catch (e) {
      AppLogger.error('Error sending message', e);
      return false;
    }
  }

  /// Start typing indicator
  static void startTyping(String tradeId) {
    if (!_isConnected || _socket == null) return;
    emit('typing_start', {'tradeId': tradeId});
  }

  /// Stop typing indicator
  static void stopTyping(String tradeId) {
    if (!_isConnected || _socket == null) return;
    emit('typing_stop', {'tradeId': tradeId});
  }

  /// Mark messages as read
  static void markMessagesAsRead(String tradeId, List<String> messageIds) {
    if (!_isConnected || _socket == null) return;
    emit('mark_read', {
      'tradeId': tradeId,
      'messageIds': messageIds,
    });
  }

  /// Disconnect
  static void disconnect() {
    _reconnectTimer?.cancel();
    _socket?.disconnect();
    _socket?.dispose();
    _socket = null;
    _isConnected = false;
    _connectionController.add(false);
    AppLogger.websocket('Disconnected manually', null);
  }
  
  /// Dispose
  static void dispose() {
    disconnect();
    _tradeUpdateController.close();
    _chatMessageController.close();
    _walletUpdateController.close();
    _systemUpdateController.close();
    _typingController.close();
    _userStatusController.close();
    _connectionController.close();
    _isInitialized = false;
    AppLogger.websocket('WebSocket service disposed', null);
  }
}
