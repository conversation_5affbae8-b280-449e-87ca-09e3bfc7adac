import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/export.dart';
import '../utils/logger.dart';

class SecureStorageService {
  static SecureStorageService? _instance;
  static SecureStorageService get instance => _instance ??= SecureStorageService._();
  
  SecureStorageService._();

  // Flutter Secure Storage with hardware-backed encryption
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'kryptopesa_secure_prefs',
      preferencesKeyPrefix: 'kp_',
      resetOnError: true,
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.kryptopesa.wallet',
      accountName: 'KryptoPesa',
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );

  // Storage keys
  static const String _masterKeyKey = 'master_encryption_key';
  static const String _walletSeedKey = 'wallet_seed_encrypted';
  static const String _privateKeysKey = 'private_keys_encrypted';
  static const String _pinHashKey = 'pin_hash';
  static const String _biometricKeyKey = 'biometric_key';
  static const String _deviceIdKey = 'device_id';
  static const String _encryptionSaltKey = 'encryption_salt';

  Encrypter? _encrypter;
  IV? _iv;

  /// Initialize secure storage
  Future<bool> initialize() async {
    try {
      // Generate or retrieve master encryption key
      await _initializeMasterKey();
      
      // Generate device ID if not exists
      await _ensureDeviceId();
      
      AppLogger.info('Secure storage initialized successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to initialize secure storage', e);
      return false;
    }
  }

  /// Store wallet seed securely
  Future<bool> storeWalletSeed(String seed) async {
    try {
      final encryptedSeed = await _encryptData(seed);
      await _secureStorage.write(key: _walletSeedKey, value: encryptedSeed);
      
      AppLogger.info('Wallet seed stored securely');
      return true;
    } catch (e) {
      AppLogger.error('Failed to store wallet seed', e);
      return false;
    }
  }

  /// Retrieve wallet seed
  Future<String?> getWalletSeed() async {
    try {
      final encryptedSeed = await _secureStorage.read(key: _walletSeedKey);
      if (encryptedSeed == null) return null;
      
      return await _decryptData(encryptedSeed);
    } catch (e) {
      AppLogger.error('Failed to retrieve wallet seed', e);
      return null;
    }
  }

  /// Store private keys securely
  Future<bool> storePrivateKeys(Map<String, String> privateKeys) async {
    try {
      final keysJson = json.encode(privateKeys);
      final encryptedKeys = await _encryptData(keysJson);
      await _secureStorage.write(key: _privateKeysKey, value: encryptedKeys);
      
      AppLogger.info('Private keys stored securely');
      return true;
    } catch (e) {
      AppLogger.error('Failed to store private keys', e);
      return false;
    }
  }

  /// Retrieve private keys
  Future<Map<String, String>?> getPrivateKeys() async {
    try {
      final encryptedKeys = await _secureStorage.read(key: _privateKeysKey);
      if (encryptedKeys == null) return null;
      
      final keysJson = await _decryptData(encryptedKeys);
      return Map<String, String>.from(json.decode(keysJson));
    } catch (e) {
      AppLogger.error('Failed to retrieve private keys', e);
      return null;
    }
  }

  /// Store PIN hash
  Future<bool> storePinHash(String pin) async {
    try {
      final salt = await _getOrCreateSalt();
      final pinHash = _hashPin(pin, salt);
      await _secureStorage.write(key: _pinHashKey, value: pinHash);
      
      AppLogger.info('PIN hash stored securely');
      return true;
    } catch (e) {
      AppLogger.error('Failed to store PIN hash', e);
      return false;
    }
  }

  /// Verify PIN
  Future<bool> verifyPin(String pin) async {
    try {
      final storedHash = await _secureStorage.read(key: _pinHashKey);
      if (storedHash == null) return false;
      
      final salt = await _getOrCreateSalt();
      final pinHash = _hashPin(pin, salt);
      
      return storedHash == pinHash;
    } catch (e) {
      AppLogger.error('Failed to verify PIN', e);
      return false;
    }
  }

  /// Check if PIN is set
  Future<bool> isPinSet() async {
    try {
      final pinHash = await _secureStorage.read(key: _pinHashKey);
      return pinHash != null;
    } catch (e) {
      AppLogger.error('Failed to check PIN status', e);
      return false;
    }
  }

  /// Store biometric key
  Future<bool> storeBiometricKey(String key) async {
    try {
      await _secureStorage.write(key: _biometricKeyKey, value: key);
      AppLogger.info('Biometric key stored securely');
      return true;
    } catch (e) {
      AppLogger.error('Failed to store biometric key', e);
      return false;
    }
  }

  /// Get biometric key
  Future<String?> getBiometricKey() async {
    try {
      return await _secureStorage.read(key: _biometricKeyKey);
    } catch (e) {
      AppLogger.error('Failed to retrieve biometric key', e);
      return null;
    }
  }

  /// Store encrypted data with custom key
  Future<bool> storeEncryptedData(String key, String data) async {
    try {
      final encryptedData = await _encryptData(data);
      await _secureStorage.write(key: key, value: encryptedData);
      return true;
    } catch (e) {
      AppLogger.error('Failed to store encrypted data', e);
      return false;
    }
  }

  /// Retrieve and decrypt data with custom key
  Future<String?> getDecryptedData(String key) async {
    try {
      final encryptedData = await _secureStorage.read(key: key);
      if (encryptedData == null) return null;
      
      return await _decryptData(encryptedData);
    } catch (e) {
      AppLogger.error('Failed to retrieve decrypted data', e);
      return null;
    }
  }

  /// Get device ID
  Future<String> getDeviceId() async {
    try {
      String? deviceId = await _secureStorage.read(key: _deviceIdKey);
      if (deviceId == null) {
        deviceId = await _generateDeviceId();
        await _secureStorage.write(key: _deviceIdKey, value: deviceId);
      }
      return deviceId;
    } catch (e) {
      AppLogger.error('Failed to get device ID', e);
      return 'unknown_device';
    }
  }

  /// Clear all secure data
  Future<bool> clearAllData() async {
    try {
      await _secureStorage.deleteAll();
      _encrypter = null;
      _iv = null;
      
      AppLogger.info('All secure data cleared');
      return true;
    } catch (e) {
      AppLogger.error('Failed to clear secure data', e);
      return false;
    }
  }

  /// Clear specific data
  Future<bool> clearData(String key) async {
    try {
      await _secureStorage.delete(key: key);
      return true;
    } catch (e) {
      AppLogger.error('Failed to clear data for key: $key', e);
      return false;
    }
  }

  /// Check if data exists
  Future<bool> hasData(String key) async {
    try {
      final data = await _secureStorage.read(key: key);
      return data != null;
    } catch (e) {
      AppLogger.error('Failed to check data existence', e);
      return false;
    }
  }

  /// Get all stored keys (for debugging/migration)
  Future<Map<String, String>> getAllData() async {
    try {
      return await _secureStorage.readAll();
    } catch (e) {
      AppLogger.error('Failed to get all data', e);
      return {};
    }
  }

  /// Initialize master encryption key
  Future<void> _initializeMasterKey() async {
    String? masterKey = await _secureStorage.read(key: _masterKeyKey);
    
    if (masterKey == null) {
      // Generate new master key
      masterKey = _generateSecureKey();
      await _secureStorage.write(key: _masterKeyKey, value: masterKey);
      AppLogger.info('New master encryption key generated');
    }

    // Initialize encrypter
    final key = Key.fromBase64(masterKey);
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }

  /// Encrypt data
  Future<String> _encryptData(String data) async {
    if (_encrypter == null) {
      throw Exception('Encrypter not initialized');
    }
    
    final encrypted = _encrypter!.encrypt(data, iv: _iv);
    return encrypted.base64;
  }

  /// Decrypt data
  Future<String> _decryptData(String encryptedData) async {
    if (_encrypter == null) {
      throw Exception('Encrypter not initialized');
    }
    
    final encrypted = Encrypted.fromBase64(encryptedData);
    return _encrypter!.decrypt(encrypted, iv: _iv);
  }

  /// Generate secure random key
  String _generateSecureKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// Generate device ID
  Future<String> _generateDeviceId() async {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final deviceId = '${base64.encode(bytes)}_$timestamp';
    return deviceId;
  }

  /// Ensure device ID exists
  Future<void> _ensureDeviceId() async {
    await getDeviceId(); // This will create one if it doesn't exist
  }

  /// Get or create salt for PIN hashing
  Future<String> _getOrCreateSalt() async {
    String? salt = await _secureStorage.read(key: _encryptionSaltKey);
    if (salt == null) {
      salt = _generateSecureKey();
      await _secureStorage.write(key: _encryptionSaltKey, value: salt);
    }
    return salt;
  }

  /// Hash PIN with salt
  String _hashPin(String pin, String salt) {
    final bytes = utf8.encode(pin + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Backup secure data (encrypted)
  Future<String?> createSecureBackup() async {
    try {
      final allData = await getAllData();
      final backupData = {
        'timestamp': DateTime.now().toIso8601String(),
        'deviceId': await getDeviceId(),
        'data': allData,
      };
      
      final backupJson = json.encode(backupData);
      return await _encryptData(backupJson);
    } catch (e) {
      AppLogger.error('Failed to create secure backup', e);
      return null;
    }
  }

  /// Restore from secure backup
  Future<bool> restoreFromSecureBackup(String encryptedBackup) async {
    try {
      final backupJson = await _decryptData(encryptedBackup);
      final backupData = json.decode(backupJson) as Map<String, dynamic>;
      
      final data = backupData['data'] as Map<String, dynamic>;
      
      // Clear existing data
      await clearAllData();
      
      // Restore data
      for (final entry in data.entries) {
        await _secureStorage.write(key: entry.key, value: entry.value);
      }
      
      // Reinitialize
      await initialize();
      
      AppLogger.info('Secure backup restored successfully');
      return true;
    } catch (e) {
      AppLogger.error('Failed to restore secure backup', e);
      return false;
    }
  }
}
