// Removed unused imports
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:hive_flutter/hive_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../storage/secure_storage.dart';
import '../storage/local_storage.dart';
import '../network/api_client.dart';
import '../security/biometric_service.dart';
import '../utils/logger.dart';
import 'notification_service.dart';
import 'websocket_service.dart';
import 'background_sync_service.dart';

/// App Initializer Service
/// Handles initialization of all core services and dependencies
class AppInitializer {
  static bool _isInitialized = false;
  
  /// Initialize all app services
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Starting app initialization...');
      
      // Initialize storage services
      await _initializeStorage();
      
      // Initialize security services
      await _initializeSecurity();
      
      // Initialize network services
      await _initializeNetwork();
      
      // Initialize notification services
      await _initializeNotifications();
      
      // Initialize background services
      await _initializeBackgroundServices();
      
      // Request permissions
      await _requestPermissions();
      
      _isInitialized = true;
      AppLogger.info('App initialization completed successfully');
      
    } catch (error, stackTrace) {
      AppLogger.error('App initialization failed', error, stackTrace);
      rethrow;
    }
  }
  
  /// Initialize storage services
  static Future<void> _initializeStorage() async {
    AppLogger.info('Initializing storage services...');
    
    // Initialize SharedPreferences
    await SharedPreferences.getInstance();
    
    // Initialize Hive boxes
    await LocalStorage.initialize();
    
    // Initialize secure storage
    await SecureStorage.initialize();
    
    AppLogger.info('Storage services initialized');
  }
  
  /// Initialize security services
  static Future<void> _initializeSecurity() async {
    AppLogger.info('Initializing security services...');
    
    // Initialize biometric authentication
    await BiometricService.initialize();
    
    AppLogger.info('Security services initialized');
  }
  
  /// Initialize network services
  static Future<void> _initializeNetwork() async {
    AppLogger.info('Initializing network services...');
    
    // Initialize API client
    await ApiClient.initialize();
    
    // Initialize WebSocket service
    await WebSocketService.initialize();
    
    // Check connectivity
    final connectivity = await Connectivity().checkConnectivity();
    AppLogger.info('Network connectivity: $connectivity');
    
    AppLogger.info('Network services initialized');
  }
  
  /// Initialize notification services
  static Future<void> _initializeNotifications() async {
    AppLogger.info('Initializing notification services...');
    
    // Initialize local notifications
    await NotificationService.initialize();
    
    AppLogger.info('Notification services initialized');
  }
  
  /// Initialize background services
  static Future<void> _initializeBackgroundServices() async {
    AppLogger.info('Initializing background services...');
    
    // Initialize background sync service
    await BackgroundSyncService.initialize();
    
    AppLogger.info('Background services initialized');
  }
  
  /// Request necessary permissions
  static Future<void> _requestPermissions() async {
    AppLogger.info('Requesting permissions...');
    
    // Request notification permission
    await Permission.notification.request();
    
    // Request camera permission (for QR scanning)
    await Permission.camera.request();
    
    // Request storage permission (for file operations)
    await Permission.storage.request();
    
    // Request biometric permission
    await Permission.systemAlertWindow.request();
    
    AppLogger.info('Permissions requested');
  }
  
  /// Check if app is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
