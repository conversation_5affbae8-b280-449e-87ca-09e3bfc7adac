import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import '../models/user_model.dart';
import '../models/auth_models.dart';
import '../utils/logger.dart';
import '../constants/api_constants.dart';
import 'api_service.dart';

class AuthService {
  static const String _userKey = 'user_data';
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _biometricEnabledKey = 'biometric_enabled';
  
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static User? _currentUser;
  static String? _authToken;
  static bool _isInitialized = false;

  // Initialize the auth service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadUserData();
    await _loadAuthToken();
    _isInitialized = true;
    
    AppLogger.info('AuthService initialized - User: ${_currentUser != null}, Token: ${_authToken != null}');
  }

  // Load user data from storage
  static Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userKey);
      
      if (userData != null) {
        final userJson = jsonDecode(userData);
        _currentUser = User.fromJson(userJson);
        AppLogger.debug('User data loaded: ${_currentUser?.id}');
      }
    } catch (e) {
      AppLogger.error('Failed to load user data', e);
    }
  }

  // Save user data to storage
  static Future<void> _saveUserData(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = jsonEncode(user.toJson());
      await prefs.setString(_userKey, userData);
      _currentUser = user;
      AppLogger.debug('User data saved: ${user.id}');
    } catch (e) {
      AppLogger.error('Failed to save user data', e);
    }
  }

  // Load auth token from secure storage
  static Future<void> _loadAuthToken() async {
    try {
      _authToken = await _secureStorage.read(key: _tokenKey);
      if (_authToken != null) {
        await ApiService.setAuthToken(_authToken!);
      }
    } catch (e) {
      AppLogger.error('Failed to load auth token', e);
    }
  }

  // Save auth token to secure storage
  static Future<void> _saveAuthToken(String token) async {
    try {
      await _secureStorage.write(key: _tokenKey, value: token);
      _authToken = token;
      await ApiService.setAuthToken(token);
      AppLogger.debug('Auth token saved');
    } catch (e) {
      AppLogger.error('Failed to save auth token', e);
    }
  }

  // Clear all auth data
  static Future<void> _clearAuthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await _secureStorage.delete(key: _tokenKey);
      await _secureStorage.delete(key: _refreshTokenKey);
      
      _currentUser = null;
      _authToken = null;
      await ApiService.clearAuthToken();
      
      AppLogger.debug('Auth data cleared');
    } catch (e) {
      AppLogger.error('Failed to clear auth data', e);
    }
  }

  // Register new user
  static Future<AuthResult> register(RegisterRequest request) async {
    try {
      AppLogger.info('Registration attempt: ${request.email}');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.authRegister,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        if (authResponse.token != null) {
          await _saveAuthToken(authResponse.token!);
        }
        
        if (authResponse.user != null) {
          await _saveUserData(authResponse.user!);
        }

        AppLogger.info('Registration successful: ${authResponse.user?.id}');
        return AuthResult.success(
          user: authResponse.user,
          token: authResponse.token,
          message: response.message ?? 'Registration successful',
        );
      } else {
        AppLogger.warning('Registration failed: ${response.message}');
        return AuthResult.failure(
          message: response.message ?? 'Registration failed',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Registration error', e);
      return AuthResult.failure(
        message: 'Registration failed. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Login user
  static Future<AuthResult> login(LoginRequest request) async {
    try {
      AppLogger.info('Login attempt: ${request.identifier}');
      
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.authLogin,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        if (authResponse.token != null) {
          await _saveAuthToken(authResponse.token!);
        }
        
        if (authResponse.user != null) {
          await _saveUserData(authResponse.user!);
        }

        AppLogger.info('Login successful: ${authResponse.user?.id}');
        return AuthResult.success(
          user: authResponse.user,
          token: authResponse.token,
          message: response.message ?? 'Login successful',
        );
      } else {
        AppLogger.warning('Login failed: ${response.message}');
        return AuthResult.failure(
          message: response.message ?? 'Login failed',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Login error', e);
      return AuthResult.failure(
        message: 'Login failed. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Logout user
  static Future<void> logout() async {
    try {
      AppLogger.info('Logout attempt: ${_currentUser?.id}');
      
      // Call logout endpoint
      await ApiService.post(ApiConstants.authLogout);
      
      // Clear local data
      await _clearAuthData();
      
      AppLogger.info('Logout successful');
    } catch (e) {
      AppLogger.error('Logout error', e);
      // Clear local data even if API call fails
      await _clearAuthData();
    }
  }

  // Get current user
  static Future<User?> getCurrentUser() async {
    if (_currentUser != null) {
      return _currentUser;
    }

    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiConstants.authMe,
      );

      if (response.success && response.data != null) {
        final user = User.fromJson(response.data!);
        await _saveUserData(user);
        return user;
      }
    } catch (e) {
      AppLogger.error('Failed to get current user', e);
    }

    return null;
  }

  // Refresh auth token
  static Future<bool> refreshToken() async {
    try {
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      if (refreshToken == null) return false;

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiConstants.authRefresh,
        data: {'refreshToken': refreshToken},
      );

      if (response.success && response.data != null) {
        final authResponse = AuthResponse.fromJson(response.data!);
        
        if (authResponse.token != null) {
          await _saveAuthToken(authResponse.token!);
          return true;
        }
      }
    } catch (e) {
      AppLogger.error('Token refresh error', e);
    }

    return false;
  }

  // Forgot password
  static Future<AuthResult> forgotPassword(String email) async {
    try {
      final response = await ApiService.post(
        ApiConstants.authForgotPassword,
        data: {'email': email},
      );

      if (response.success) {
        return AuthResult.success(
          message: response.message ?? 'Password reset email sent',
        );
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Failed to send reset email',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Forgot password error', e);
      return AuthResult.failure(
        message: 'Failed to send reset email. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Reset password
  static Future<AuthResult> resetPassword(ResetPasswordRequest request) async {
    try {
      final response = await ApiService.post(
        ApiConstants.authResetPassword,
        data: request.toJson(),
      );

      if (response.success) {
        return AuthResult.success(
          message: response.message ?? 'Password reset successful',
        );
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Password reset failed',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Reset password error', e);
      return AuthResult.failure(
        message: 'Password reset failed. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Verify email
  static Future<AuthResult> verifyEmail(String token) async {
    try {
      final response = await ApiService.post(
        ApiConstants.authVerifyEmail,
        data: {'token': token},
      );

      if (response.success) {
        // Refresh user data
        await getCurrentUser();
        
        return AuthResult.success(
          message: response.message ?? 'Email verified successfully',
        );
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Email verification failed',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Email verification error', e);
      return AuthResult.failure(
        message: 'Email verification failed. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Resend verification email
  static Future<AuthResult> resendVerificationEmail() async {
    try {
      final response = await ApiService.post(ApiConstants.authResendVerification);

      if (response.success) {
        return AuthResult.success(
          message: response.message ?? 'Verification email sent',
        );
      } else {
        return AuthResult.failure(
          message: response.message ?? 'Failed to send verification email',
          errors: response.error,
        );
      }
    } catch (e) {
      AppLogger.error('Resend verification error', e);
      return AuthResult.failure(
        message: 'Failed to send verification email. Please try again.',
        errors: {'general': e.toString()},
      );
    }
  }

  // Check if user is authenticated
  static bool get isAuthenticated => _authToken != null && _currentUser != null;

  // Get current user (cached)
  static User? get currentUser => _currentUser;

  // Get auth token
  static String? get authToken => _authToken;

  // Check if biometric is enabled
  static Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      AppLogger.error('Failed to check biometric status', e);
      return false;
    }
  }

  // Enable/disable biometric authentication
  static Future<void> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enabled);
      AppLogger.info('Biometric setting updated: $enabled');
    } catch (e) {
      AppLogger.error('Failed to update biometric setting', e);
    }
  }
}
