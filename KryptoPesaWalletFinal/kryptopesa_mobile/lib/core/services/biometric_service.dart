import 'dart:io';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

class BiometricService {
  static BiometricService? _instance;
  static BiometricService get instance => _instance ??= BiometricService._();
  
  BiometricService._();

  final LocalAuthentication _localAuth = LocalAuthentication();
  
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _biometricTypeKey = 'biometric_type';
  static const String _lastBiometricCheckKey = 'last_biometric_check';

  /// Check if biometric authentication is available on device
  Future<bool> isAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      
      AppLogger.info('Biometric availability: device=$isAvailable, canCheck=$canCheckBiometrics');
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      AppLogger.error('Error checking biometric availability', e);
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final List<BiometricType> availableBiometrics = 
          await _localAuth.getAvailableBiometrics();
      
      AppLogger.info('Available biometrics: $availableBiometrics');
      return availableBiometrics;
    } catch (e) {
      AppLogger.error('Error getting available biometrics', e);
      return [];
    }
  }

  /// Check if biometric authentication is enabled in app settings
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      AppLogger.error('Error checking biometric enabled status', e);
      return false;
    }
  }

  /// Enable or disable biometric authentication
  Future<bool> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enabled);
      
      if (enabled) {
        // Store the current biometric types
        final biometrics = await getAvailableBiometrics();
        await prefs.setString(_biometricTypeKey, biometrics.toString());
      } else {
        await prefs.remove(_biometricTypeKey);
      }
      
      AppLogger.info('Biometric authentication ${enabled ? 'enabled' : 'disabled'}');
      return true;
    } catch (e) {
      AppLogger.error('Error setting biometric enabled status', e);
      return false;
    }
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticate({
    required String reason,
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      // Check if biometric is available
      if (!await isAvailable()) {
        return BiometricAuthResult.unavailable();
      }

      // Check if biometric is enabled in app
      if (!await isBiometricEnabled()) {
        return BiometricAuthResult.disabled();
      }

      AppLogger.info('Starting biometric authentication');

      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        authMessages: [
          AndroidAuthMessages(
            signInTitle: 'Biometric Authentication',
            biometricHint: 'Touch sensor',
            biometricNotRecognized: 'Biometric not recognized, try again',
            biometricRequiredTitle: 'Biometric Required',
            biometricSuccess: 'Biometric authentication successful',
            cancelButton: 'Cancel',
            deviceCredentialsRequiredTitle: 'Device Credentials Required',
            deviceCredentialsSetupDescription: 'Please set up device credentials',
            goToSettingsButton: 'Go to Settings',
            goToSettingsDescription: 'Please set up biometric authentication in settings',
          ),
          const IOSAuthMessages(
            lockOut: 'Biometric authentication is locked out',
            goToSettingsButton: 'Go to Settings',
            goToSettingsDescription: 'Please set up biometric authentication in settings',
            cancelButton: 'Cancel',
          ),
        ],
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: false,
        ),
      );

      // Update last check timestamp
      await _updateLastBiometricCheck();

      if (didAuthenticate) {
        AppLogger.info('Biometric authentication successful');
        return BiometricAuthResult.success();
      } else {
        AppLogger.info('Biometric authentication failed or cancelled');
        return BiometricAuthResult.failed();
      }

    } catch (e) {
      AppLogger.error('Biometric authentication error', e);
      return BiometricAuthResult.error(e.toString());
    }
  }

  /// Quick biometric check for app unlock
  Future<bool> quickAuthenticate() async {
    final result = await authenticate(
      reason: 'Unlock KryptoPesa',
      useErrorDialogs: false,
      stickyAuth: false,
    );
    return result.isSuccess;
  }

  /// Authenticate for sensitive operations
  Future<bool> authenticateForSensitiveOperation(String operation) async {
    final result = await authenticate(
      reason: 'Authenticate to $operation',
      useErrorDialogs: true,
      stickyAuth: true,
    );
    return result.isSuccess;
  }

  /// Check if biometric setup has changed (security measure)
  Future<bool> hasBiometricSetupChanged() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedBiometrics = prefs.getString(_biometricTypeKey);

      if (storedBiometrics == null) return false;

      final currentBiometrics = await getAvailableBiometrics();
      final currentBiometricsString = currentBiometrics.toString();

      return storedBiometrics != currentBiometricsString;
    } catch (e) {
      AppLogger.error('Error checking biometric setup changes', e);
      return true; // Assume changed for security
    }
  }

  /// Get biometric capability info
  Future<BiometricCapability> getBiometricCapability() async {
    try {
      final isAvailable = await this.isAvailable();
      final biometrics = await getAvailableBiometrics();
      final isEnabled = await isBiometricEnabled();

      return BiometricCapability(
        isAvailable: isAvailable,
        availableTypes: biometrics,
        isEnabled: isEnabled,
        hasFingerprint: biometrics.contains(BiometricType.fingerprint),
        hasFaceId: biometrics.contains(BiometricType.face),
        hasIris: biometrics.contains(BiometricType.iris),
        platformSupport: _getPlatformSupport(),
      );
    } catch (e) {
      AppLogger.error('Error getting biometric capability', e);
      return BiometricCapability.unavailable();
    }
  }

  /// Update last biometric check timestamp
  Future<void> _updateLastBiometricCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastBiometricCheckKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      AppLogger.error('Error updating last biometric check', e);
    }
  }

  /// Get platform-specific support info
  String _getPlatformSupport() {
    if (Platform.isAndroid) {
      return 'Android Biometric API';
    } else if (Platform.isIOS) {
      return 'iOS Touch ID / Face ID';
    } else {
      return 'Platform not supported';
    }
  }

  /// Reset biometric settings (for security purposes)
  Future<void> resetBiometricSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_biometricEnabledKey);
      await prefs.remove(_biometricTypeKey);
      await prefs.remove(_lastBiometricCheckKey);
      
      AppLogger.info('Biometric settings reset');
    } catch (e) {
      AppLogger.error('Error resetting biometric settings', e);
    }
  }
}

// Result classes
class BiometricAuthResult {
  final bool isSuccess;
  final bool isAvailable;
  final bool isEnabled;
  final String? error;

  const BiometricAuthResult._({
    required this.isSuccess,
    required this.isAvailable,
    required this.isEnabled,
    this.error,
  });

  factory BiometricAuthResult.success() {
    return const BiometricAuthResult._(
      isSuccess: true,
      isAvailable: true,
      isEnabled: true,
    );
  }

  factory BiometricAuthResult.failed() {
    return const BiometricAuthResult._(
      isSuccess: false,
      isAvailable: true,
      isEnabled: true,
    );
  }

  factory BiometricAuthResult.unavailable() {
    return const BiometricAuthResult._(
      isSuccess: false,
      isAvailable: false,
      isEnabled: false,
    );
  }

  factory BiometricAuthResult.disabled() {
    return const BiometricAuthResult._(
      isSuccess: false,
      isAvailable: true,
      isEnabled: false,
    );
  }

  factory BiometricAuthResult.error(String error) {
    return BiometricAuthResult._(
      isSuccess: false,
      isAvailable: true,
      isEnabled: true,
      error: error,
    );
  }

  bool get hasError => error != null;
}

class BiometricCapability {
  final bool isAvailable;
  final List<BiometricType> availableTypes;
  final bool isEnabled;
  final bool hasFingerprint;
  final bool hasFaceId;
  final bool hasIris;
  final String platformSupport;

  const BiometricCapability({
    required this.isAvailable,
    required this.availableTypes,
    required this.isEnabled,
    required this.hasFingerprint,
    required this.hasFaceId,
    required this.hasIris,
    required this.platformSupport,
  });

  factory BiometricCapability.unavailable() {
    return const BiometricCapability(
      isAvailable: false,
      availableTypes: [],
      isEnabled: false,
      hasFingerprint: false,
      hasFaceId: false,
      hasIris: false,
      platformSupport: 'Not available',
    );
  }

  String get primaryBiometricType {
    if (hasFaceId) return 'Face ID';
    if (hasFingerprint) return 'Fingerprint';
    if (hasIris) return 'Iris';
    return 'Biometric';
  }

  String get description {
    final types = <String>[];
    if (hasFingerprint) types.add('Fingerprint');
    if (hasFaceId) types.add('Face ID');
    if (hasIris) types.add('Iris');
    
    if (types.isEmpty) return 'No biometric authentication available';
    if (types.length == 1) return types.first;
    if (types.length == 2) return '${types[0]} and ${types[1]}';
    return '${types.sublist(0, types.length - 1).join(', ')}, and ${types.last}';
  }
}
