import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';

enum ConnectionType {
  none,
  wifi,
  mobile,
  ethernet,
  bluetooth,
  vpn,
  other,
}

enum ConnectionQuality {
  excellent,
  good,
  fair,
  poor,
  none,
}

class ConnectionStatus {
  final ConnectionType type;
  final ConnectionQuality quality;
  final bool isConnected;
  final bool isMetered;
  final int? signalStrength;
  final DateTime lastChecked;

  const ConnectionStatus({
    required this.type,
    required this.quality,
    required this.isConnected,
    this.isMetered = false,
    this.signalStrength,
    required this.lastChecked,
  });

  ConnectionStatus copyWith({
    ConnectionType? type,
    ConnectionQuality? quality,
    bool? isConnected,
    bool? isMetered,
    int? signalStrength,
    DateTime? lastChecked,
  }) {
    return ConnectionStatus(
      type: type ?? this.type,
      quality: quality ?? this.quality,
      isConnected: isConnected ?? this.isConnected,
      isMetered: isMetered ?? this.isMetered,
      signalStrength: signalStrength ?? this.signalStrength,
      lastChecked: lastChecked ?? this.lastChecked,
    );
  }

  @override
  String toString() {
    return 'ConnectionStatus(type: $type, quality: $quality, connected: $isConnected)';
  }
}

class ConnectivityService {
  static ConnectivityService? _instance;
  static ConnectivityService get instance => _instance ??= ConnectivityService._();
  
  ConnectivityService._();

  final Connectivity _connectivity = Connectivity();
  final StreamController<ConnectionStatus> _statusController = 
      StreamController<ConnectionStatus>.broadcast();

  ConnectionStatus _currentStatus = ConnectionStatus(
    type: ConnectionType.none,
    quality: ConnectionQuality.none,
    isConnected: false,
    lastChecked: DateTime.now(),
  );

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _qualityCheckTimer;
  Timer? _periodicCheckTimer;

  // Configuration
  static const Duration _qualityCheckInterval = Duration(seconds: 30);
  static const Duration _periodicCheckInterval = Duration(minutes: 1);
  static const Duration _connectionTimeout = Duration(seconds: 10);
  static const List<String> _testHosts = [
    'google.com',
    'cloudflare.com',
    '*******',
  ];

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    try {
      AppLogger.info('Initializing connectivity service...');

      // Get initial connectivity status
      await _updateConnectionStatus();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        _onConnectivityChanged,
        onError: (error) {
          AppLogger.error('Connectivity stream error', error);
        },
      );

      // Start periodic quality checks
      _startQualityChecks();

      // Start periodic connectivity checks
      _startPeriodicChecks();

      AppLogger.info('Connectivity service initialized');
    } catch (e) {
      AppLogger.error('Failed to initialize connectivity service', e);
    }
  }

  /// Get current connection status
  ConnectionStatus get currentStatus => _currentStatus;

  /// Stream of connection status changes
  Stream<ConnectionStatus> get statusStream => _statusController.stream;

  /// Check if currently connected
  bool get isConnected => _currentStatus.isConnected;

  /// Check if connection is metered (mobile data)
  bool get isMetered => _currentStatus.isMetered;

  /// Check if connection quality is good enough for operations
  bool get isGoodConnection => _currentStatus.quality.index >= ConnectionQuality.fair.index;

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) async {
    AppLogger.info('Connectivity changed: $results');
    await _updateConnectionStatus();
  }

  /// Update connection status
  Future<void> _updateConnectionStatus() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final connectionType = _mapConnectivityResult(connectivityResults);
      
      bool isConnected = false;
      ConnectionQuality quality = ConnectionQuality.none;

      if (connectionType != ConnectionType.none) {
        // Test actual internet connectivity
        final connectivityTest = await _testInternetConnectivity();
        isConnected = connectivityTest.isConnected;
        quality = connectivityTest.quality;
      }

      final newStatus = ConnectionStatus(
        type: connectionType,
        quality: quality,
        isConnected: isConnected,
        isMetered: _isMeteredConnection(connectionType),
        lastChecked: DateTime.now(),
      );

      if (_hasStatusChanged(newStatus)) {
        _currentStatus = newStatus;
        _statusController.add(_currentStatus);
        
        AppLogger.info('Connection status updated: $_currentStatus');
      }
    } catch (e) {
      AppLogger.error('Failed to update connection status', e);
    }
  }

  /// Test actual internet connectivity and quality
  Future<({bool isConnected, ConnectionQuality quality})> _testInternetConnectivity() async {
    try {
      final stopwatch = Stopwatch()..start();
      int successfulTests = 0;
      int totalTests = _testHosts.length;
      List<int> responseTimes = [];

      for (final host in _testHosts) {
        try {
          final testStopwatch = Stopwatch()..start();
          
          final result = await InternetAddress.lookup(host)
              .timeout(_connectionTimeout);
          
          testStopwatch.stop();
          
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            successfulTests++;
            responseTimes.add(testStopwatch.elapsedMilliseconds);
          }
        } catch (e) {
          // Test failed for this host
          AppLogger.debug('Connectivity test failed for $host: $e');
        }
      }

      stopwatch.stop();

      final isConnected = successfulTests > 0;
      final quality = _calculateConnectionQuality(
        successfulTests,
        totalTests,
        responseTimes,
      );

      AppLogger.debug(
        'Connectivity test: $successfulTests/$totalTests successful, '
        'quality: $quality, time: ${stopwatch.elapsedMilliseconds}ms'
      );

      return (isConnected: isConnected, quality: quality);
    } catch (e) {
      AppLogger.error('Internet connectivity test failed', e);
      return (isConnected: false, quality: ConnectionQuality.none);
    }
  }

  /// Calculate connection quality based on test results
  ConnectionQuality _calculateConnectionQuality(
    int successfulTests,
    int totalTests,
    List<int> responseTimes,
  ) {
    if (successfulTests == 0) return ConnectionQuality.none;

    final successRate = successfulTests / totalTests;
    final avgResponseTime = responseTimes.isNotEmpty 
        ? responseTimes.reduce((a, b) => a + b) / responseTimes.length 
        : 0;

    if (successRate >= 0.8 && avgResponseTime < 500) {
      return ConnectionQuality.excellent;
    } else if (successRate >= 0.6 && avgResponseTime < 1000) {
      return ConnectionQuality.good;
    } else if (successRate >= 0.4 && avgResponseTime < 2000) {
      return ConnectionQuality.fair;
    } else if (successRate > 0) {
      return ConnectionQuality.poor;
    } else {
      return ConnectionQuality.none;
    }
  }

  /// Map connectivity result to connection type
  ConnectionType _mapConnectivityResult(List<ConnectivityResult> results) {
    if (results.isEmpty) return ConnectionType.none;

    // Priority order: wifi > ethernet > mobile > other
    if (results.contains(ConnectivityResult.wifi)) {
      return ConnectionType.wifi;
    } else if (results.contains(ConnectivityResult.ethernet)) {
      return ConnectionType.ethernet;
    } else if (results.contains(ConnectivityResult.mobile)) {
      return ConnectionType.mobile;
    } else if (results.contains(ConnectivityResult.bluetooth)) {
      return ConnectionType.bluetooth;
    } else if (results.contains(ConnectivityResult.vpn)) {
      return ConnectionType.vpn;
    } else if (results.contains(ConnectivityResult.other)) {
      return ConnectionType.other;
    } else {
      return ConnectionType.none;
    }
  }

  /// Check if connection is metered
  bool _isMeteredConnection(ConnectionType type) {
    return type == ConnectionType.mobile || type == ConnectionType.bluetooth;
  }

  /// Check if status has changed significantly
  bool _hasStatusChanged(ConnectionStatus newStatus) {
    return _currentStatus.type != newStatus.type ||
           _currentStatus.isConnected != newStatus.isConnected ||
           _currentStatus.quality != newStatus.quality;
  }

  /// Start periodic quality checks
  void _startQualityChecks() {
    _qualityCheckTimer?.cancel();
    _qualityCheckTimer = Timer.periodic(_qualityCheckInterval, (timer) {
      if (_currentStatus.isConnected) {
        _updateConnectionStatus();
      }
    });
  }

  /// Start periodic connectivity checks
  void _startPeriodicChecks() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = Timer.periodic(_periodicCheckInterval, (timer) {
      _updateConnectionStatus();
    });
  }

  /// Force refresh connection status
  Future<void> refresh() async {
    AppLogger.info('Forcing connectivity refresh...');
    await _updateConnectionStatus();
  }

  /// Wait for connection to be available
  Future<bool> waitForConnection({
    Duration timeout = const Duration(seconds: 30),
    ConnectionQuality minQuality = ConnectionQuality.fair,
  }) async {
    if (_currentStatus.isConnected && 
        _currentStatus.quality.index >= minQuality.index) {
      return true;
    }

    final completer = Completer<bool>();
    late StreamSubscription<ConnectionStatus> subscription;

    subscription = statusStream.listen((status) {
      if (status.isConnected && status.quality.index >= minQuality.index) {
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });

    // Set timeout
    Timer(timeout, () {
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future;
  }

  /// Get connection info for debugging
  Map<String, dynamic> getConnectionInfo() {
    return {
      'type': _currentStatus.type.name,
      'quality': _currentStatus.quality.name,
      'isConnected': _currentStatus.isConnected,
      'isMetered': _currentStatus.isMetered,
      'signalStrength': _currentStatus.signalStrength,
      'lastChecked': _currentStatus.lastChecked.toIso8601String(),
    };
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _qualityCheckTimer?.cancel();
    _periodicCheckTimer?.cancel();
    _statusController.close();
    
    AppLogger.info('Connectivity service disposed');
  }
}
