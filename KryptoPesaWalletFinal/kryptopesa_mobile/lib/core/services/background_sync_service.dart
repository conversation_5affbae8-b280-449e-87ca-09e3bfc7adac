import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../storage/local_storage.dart';
import '../network/api_client.dart';
import '../utils/logger.dart';

/// Background Sync Service
/// Handles synchronization of offline operations when connectivity is restored
class BackgroundSyncService {
  static Timer? _syncTimer;
  static bool _isInitialized = false;
  static bool _isSyncing = false;
  
  /// Initialize background sync service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing background sync service...');
      
      // Listen for connectivity changes
      Connectivity().onConnectivityChanged.listen(_onConnectivityChanged);
      
      // Start periodic sync
      _startPeriodicSync();
      
      _isInitialized = true;
      AppLogger.info('Background sync service initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize background sync service', error, stackTrace);
      rethrow;
    }
  }
  
  /// Handle connectivity changes
  static void _onConnectivityChanged(List<ConnectivityResult> results) {
    if (!results.contains(ConnectivityResult.none)) {
      AppLogger.info('Connectivity restored, starting sync...');
      syncOfflineOperations();
    }
  }
  
  /// Start periodic sync
  static void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      syncOfflineOperations();
    });
  }
  
  /// Sync offline operations
  static Future<void> syncOfflineOperations() async {
    if (_isSyncing) return;
    
    _isSyncing = true;
    
    try {
      final operations = LocalStorage.getOfflineOperations();
      if (operations.isEmpty) {
        _isSyncing = false;
        return;
      }
      
      AppLogger.info('Syncing ${operations.length} offline operations...');
      
      for (final operation in operations) {
        try {
          await _processOperation(operation);
          await LocalStorage.removeOfflineOperation(operation['id']);
          AppLogger.offline('Operation synced successfully', operation);
        } catch (error) {
          AppLogger.error('Failed to sync operation: ${operation['id']}', error);
          // Keep operation in queue for retry
        }
      }
      
      AppLogger.info('Offline sync completed');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to sync offline operations', error, stackTrace);
    } finally {
      _isSyncing = false;
    }
  }
  
  /// Process individual operation
  static Future<void> _processOperation(Map<String, dynamic> operation) async {
    final method = operation['method'] as String;
    final path = operation['path'] as String;
    final data = operation['data'];
    final queryParameters = operation['queryParameters'] as Map<String, dynamic>?;
    
    switch (method.toUpperCase()) {
      case 'POST':
        await ApiClient.post(path, data: data, queryParameters: queryParameters);
        break;
      case 'PUT':
        await ApiClient.put(path, data: data, queryParameters: queryParameters);
        break;
      case 'PATCH':
        await ApiClient.patch(path, data: data, queryParameters: queryParameters);
        break;
      case 'DELETE':
        await ApiClient.delete(path, data: data, queryParameters: queryParameters);
        break;
      default:
        throw Exception('Unsupported method: $method');
    }
  }
  
  /// Queue operation for offline sync
  static Future<void> queueOperation({
    required String method,
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    String? operationType,
  }) async {
    final operation = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'method': method,
      'path': path,
      'data': data,
      'queryParameters': queryParameters,
      'operationType': operationType,
      'timestamp': DateTime.now().toIso8601String(),
      'retryCount': 0,
    };
    
    await LocalStorage.saveOfflineOperation(operation);
    AppLogger.offline('Operation queued', operation);
  }
  
  /// Queue trade operation
  static Future<void> queueTradeOperation({
    required String tradeId,
    required String action,
    Map<String, dynamic>? data,
  }) async {
    await queueOperation(
      method: 'POST',
      path: '/api/trades/$tradeId/actions',
      data: {
        'action': action,
        ...?data,
      },
      operationType: 'trade_action',
    );
  }
  
  /// Queue chat message
  static Future<void> queueChatMessage({
    required String chatId,
    required String message,
    String messageType = 'text',
    Map<String, dynamic>? metadata,
  }) async {
    await queueOperation(
      method: 'POST',
      path: '/api/chat/$chatId/messages',
      data: {
        'message': message,
        'messageType': messageType,
        'metadata': metadata,
        'timestamp': DateTime.now().toIso8601String(),
      },
      operationType: 'chat_message',
    );
  }
  
  /// Queue wallet transaction
  static Future<void> queueWalletTransaction({
    required String walletId,
    required String type,
    required Map<String, dynamic> transactionData,
  }) async {
    await queueOperation(
      method: 'POST',
      path: '/api/wallet/$walletId/transactions',
      data: {
        'type': type,
        ...transactionData,
      },
      operationType: 'wallet_transaction',
    );
  }
  
  /// Queue profile update
  static Future<void> queueProfileUpdate(Map<String, dynamic> profileData) async {
    await queueOperation(
      method: 'PUT',
      path: '/api/users/profile',
      data: profileData,
      operationType: 'profile_update',
    );
  }
  
  /// Queue settings update
  static Future<void> queueSettingsUpdate(Map<String, dynamic> settings) async {
    await queueOperation(
      method: 'PUT',
      path: '/api/users/settings',
      data: settings,
      operationType: 'settings_update',
    );
  }
  
  /// Queue offer creation
  static Future<void> queueOfferCreation(Map<String, dynamic> offerData) async {
    await queueOperation(
      method: 'POST',
      path: '/api/offers',
      data: offerData,
      operationType: 'offer_creation',
    );
  }
  
  /// Queue offer update
  static Future<void> queueOfferUpdate({
    required String offerId,
    required Map<String, dynamic> updateData,
  }) async {
    await queueOperation(
      method: 'PUT',
      path: '/api/offers/$offerId',
      data: updateData,
      operationType: 'offer_update',
    );
  }
  
  /// Queue dispute creation
  static Future<void> queueDisputeCreation({
    required String tradeId,
    required String reason,
    String? description,
    List<String>? evidence,
  }) async {
    await queueOperation(
      method: 'POST',
      path: '/api/trades/$tradeId/dispute',
      data: {
        'reason': reason,
        'description': description,
        'evidence': evidence,
      },
      operationType: 'dispute_creation',
    );
  }
  
  /// Get pending operations count
  static int getPendingOperationsCount() {
    return LocalStorage.getOfflineOperations().length;
  }
  
  /// Get pending operations by type
  static List<Map<String, dynamic>> getPendingOperationsByType(String operationType) {
    return LocalStorage.getOfflineOperations()
        .where((op) => op['operationType'] == operationType)
        .toList();
  }
  
  /// Clear all pending operations
  static Future<void> clearAllPendingOperations() async {
    await LocalStorage.clearOfflineOperations();
    AppLogger.offline('All pending operations cleared', null);
  }
  
  /// Force sync now
  static Future<void> forceSyncNow() async {
    AppLogger.info('Force syncing offline operations...');
    await syncOfflineOperations();
  }
  
  /// Check if syncing
  static bool get isSyncing => _isSyncing;
  
  /// Check if initialized
  static bool get isInitialized => _isInitialized;
  
  /// Stop background sync
  static void stop() {
    _syncTimer?.cancel();
    _syncTimer = null;
    AppLogger.info('Background sync service stopped');
  }
  
  /// Dispose
  static void dispose() {
    stop();
    _isInitialized = false;
    AppLogger.info('Background sync service disposed');
  }
}
