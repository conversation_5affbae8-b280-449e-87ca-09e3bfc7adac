import 'dart:convert';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../utils/logger.dart';

class LocalDatabaseService {
  static LocalDatabaseService? _instance;
  static LocalDatabaseService get instance => _instance ??= LocalDatabaseService._();
  
  LocalDatabaseService._();

  Database? _database;
  static const String _databaseName = 'kryptopesa_local.db';
  static const int _databaseVersion = 1;

  /// Initialize the local database
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize database with tables
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      AppLogger.info('Initializing local database at: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _createTables,
        onUpgrade: _upgradeDatabase,
        onOpen: (db) {
          AppLogger.info('Local database opened successfully');
        },
      );
    } catch (e) {
      AppLogger.error('Failed to initialize local database', e);
      rethrow;
    }
  }

  /// Create database tables
  Future<void> _createTables(Database db, int version) async {
    try {
      AppLogger.info('Creating database tables...');

      // Pending operations table
      await db.execute('''
        CREATE TABLE pending_operations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          operation_id TEXT UNIQUE NOT NULL,
          type TEXT NOT NULL,
          endpoint TEXT NOT NULL,
          method TEXT NOT NULL,
          data TEXT,
          headers TEXT,
          priority INTEGER DEFAULT 0,
          retry_count INTEGER DEFAULT 0,
          max_retries INTEGER DEFAULT 3,
          created_at INTEGER NOT NULL,
          scheduled_at INTEGER,
          last_attempt_at INTEGER,
          error_message TEXT,
          status TEXT DEFAULT 'pending'
        )
      ''');

      // Cached data table
      await db.execute('''
        CREATE TABLE cached_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          cache_key TEXT UNIQUE NOT NULL,
          data TEXT NOT NULL,
          expires_at INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          access_count INTEGER DEFAULT 0,
          last_accessed_at INTEGER
        )
      ''');

      // User data table
      await db.execute('''
        CREATE TABLE user_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id TEXT UNIQUE NOT NULL,
          data TEXT NOT NULL,
          sync_status TEXT DEFAULT 'synced',
          last_synced_at INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // Wallet data table
      await db.execute('''
        CREATE TABLE wallet_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          wallet_id TEXT UNIQUE NOT NULL,
          address TEXT NOT NULL,
          currency TEXT NOT NULL,
          balance TEXT DEFAULT '0',
          transactions TEXT,
          last_synced_at INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // Trading data table
      await db.execute('''
        CREATE TABLE trading_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          trade_id TEXT UNIQUE NOT NULL,
          offer_id TEXT,
          data TEXT NOT NULL,
          messages TEXT,
          sync_status TEXT DEFAULT 'synced',
          last_synced_at INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // Sync metadata table
      await db.execute('''
        CREATE TABLE sync_metadata (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          entity_type TEXT NOT NULL,
          entity_id TEXT NOT NULL,
          last_sync_timestamp INTEGER,
          sync_hash TEXT,
          conflict_resolution TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          UNIQUE(entity_type, entity_id)
        )
      ''');

      // Create indexes for performance
      await db.execute('CREATE INDEX idx_pending_operations_status ON pending_operations(status)');
      await db.execute('CREATE INDEX idx_pending_operations_priority ON pending_operations(priority DESC)');
      await db.execute('CREATE INDEX idx_pending_operations_scheduled ON pending_operations(scheduled_at)');
      await db.execute('CREATE INDEX idx_cached_data_expires ON cached_data(expires_at)');
      await db.execute('CREATE INDEX idx_cached_data_key ON cached_data(cache_key)');
      await db.execute('CREATE INDEX idx_user_data_sync ON user_data(sync_status)');
      await db.execute('CREATE INDEX idx_wallet_data_currency ON wallet_data(currency)');
      await db.execute('CREATE INDEX idx_trading_data_sync ON trading_data(sync_status)');
      await db.execute('CREATE INDEX idx_sync_metadata_type ON sync_metadata(entity_type)');

      AppLogger.info('Database tables created successfully');
    } catch (e) {
      AppLogger.error('Failed to create database tables', e);
      rethrow;
    }
  }

  /// Upgrade database schema
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    AppLogger.info('Upgrading database from version $oldVersion to $newVersion');
    
    // Handle database migrations here
    // For now, we'll just recreate tables
    if (oldVersion < newVersion) {
      // Drop existing tables and recreate
      await db.execute('DROP TABLE IF EXISTS pending_operations');
      await db.execute('DROP TABLE IF EXISTS cached_data');
      await db.execute('DROP TABLE IF EXISTS user_data');
      await db.execute('DROP TABLE IF EXISTS wallet_data');
      await db.execute('DROP TABLE IF EXISTS trading_data');
      await db.execute('DROP TABLE IF EXISTS sync_metadata');
      
      await _createTables(db, newVersion);
    }
  }

  /// Add pending operation
  Future<int> addPendingOperation({
    required String operationId,
    required String type,
    required String endpoint,
    required String method,
    Map<String, dynamic>? data,
    Map<String, String>? headers,
    int priority = 0,
    int maxRetries = 3,
    DateTime? scheduledAt,
  }) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      final result = await db.insert('pending_operations', {
        'operation_id': operationId,
        'type': type,
        'endpoint': endpoint,
        'method': method,
        'data': data != null ? json.encode(data) : null,
        'headers': headers != null ? json.encode(headers) : null,
        'priority': priority,
        'max_retries': maxRetries,
        'created_at': now,
        'scheduled_at': scheduledAt?.millisecondsSinceEpoch,
        'status': 'pending',
      });

      AppLogger.info('Added pending operation: $operationId');
      return result;
    } catch (e) {
      AppLogger.error('Failed to add pending operation', e);
      rethrow;
    }
  }

  /// Get pending operations
  Future<List<Map<String, dynamic>>> getPendingOperations({
    int limit = 50,
    String? type,
  }) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      String whereClause = 'status = ? AND (scheduled_at IS NULL OR scheduled_at <= ?)';
      List<dynamic> whereArgs = ['pending', now];

      if (type != null) {
        whereClause += ' AND type = ?';
        whereArgs.add(type);
      }

      final result = await db.query(
        'pending_operations',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'priority DESC, created_at ASC',
        limit: limit,
      );

      return result;
    } catch (e) {
      AppLogger.error('Failed to get pending operations', e);
      return [];
    }
  }

  /// Update operation status
  Future<void> updateOperationStatus(
    String operationId,
    String status, {
    String? errorMessage,
    int? retryCount,
  }) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      final updateData = {
        'status': status,
        'last_attempt_at': now,
      };

      if (errorMessage != null) {
        updateData['error_message'] = errorMessage;
      }

      if (retryCount != null) {
        updateData['retry_count'] = retryCount;
      }

      await db.update(
        'pending_operations',
        updateData,
        where: 'operation_id = ?',
        whereArgs: [operationId],
      );

      AppLogger.info('Updated operation status: $operationId -> $status');
    } catch (e) {
      AppLogger.error('Failed to update operation status', e);
    }
  }

  /// Remove completed operation
  Future<void> removeOperation(String operationId) async {
    try {
      final db = await database;
      await db.delete(
        'pending_operations',
        where: 'operation_id = ?',
        whereArgs: [operationId],
      );

      AppLogger.info('Removed operation: $operationId');
    } catch (e) {
      AppLogger.error('Failed to remove operation', e);
    }
  }

  /// Cache data
  Future<void> cacheData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiresIn,
  }) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;
      final expiresAt = expiresIn != null 
          ? now + expiresIn.inMilliseconds 
          : null;

      await db.insert(
        'cached_data',
        {
          'cache_key': key,
          'data': json.encode(data),
          'expires_at': expiresAt,
          'created_at': now,
          'updated_at': now,
          'last_accessed_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      AppLogger.info('Cached data: $key');
    } catch (e) {
      AppLogger.error('Failed to cache data', e);
    }
  }

  /// Get cached data
  Future<Map<String, dynamic>?> getCachedData(String key) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      final result = await db.query(
        'cached_data',
        where: 'cache_key = ? AND (expires_at IS NULL OR expires_at > ?)',
        whereArgs: [key, now],
      );

      if (result.isNotEmpty) {
        // Update access count and timestamp
        await db.update(
          'cached_data',
          {
            'access_count': (result.first['access_count'] as int) + 1,
            'last_accessed_at': now,
          },
          where: 'cache_key = ?',
          whereArgs: [key],
        );

        final dataString = result.first['data'] as String;
        return json.decode(dataString) as Map<String, dynamic>;
      }

      return null;
    } catch (e) {
      AppLogger.error('Failed to get cached data', e);
      return null;
    }
  }

  /// Clear expired cache
  Future<void> clearExpiredCache() async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      final deletedCount = await db.delete(
        'cached_data',
        where: 'expires_at IS NOT NULL AND expires_at <= ?',
        whereArgs: [now],
      );

      AppLogger.info('Cleared $deletedCount expired cache entries');
    } catch (e) {
      AppLogger.error('Failed to clear expired cache', e);
    }
  }

  /// Store user data
  Future<void> storeUserData(String userId, Map<String, dynamic> data) async {
    try {
      final db = await database;
      final now = DateTime.now().millisecondsSinceEpoch;

      await db.insert(
        'user_data',
        {
          'user_id': userId,
          'data': json.encode(data),
          'sync_status': 'pending',
          'created_at': now,
          'updated_at': now,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      AppLogger.info('Stored user data: $userId');
    } catch (e) {
      AppLogger.error('Failed to store user data', e);
    }
  }

  /// Get user data
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final db = await database;
      final result = await db.query(
        'user_data',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      if (result.isNotEmpty) {
        final dataString = result.first['data'] as String;
        return json.decode(dataString) as Map<String, dynamic>;
      }

      return null;
    } catch (e) {
      AppLogger.error('Failed to get user data', e);
      return null;
    }
  }

  /// Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    try {
      final db = await database;
      
      final pendingOps = await db.rawQuery('SELECT COUNT(*) as count FROM pending_operations');
      final cachedData = await db.rawQuery('SELECT COUNT(*) as count FROM cached_data');
      final userData = await db.rawQuery('SELECT COUNT(*) as count FROM user_data');
      final walletData = await db.rawQuery('SELECT COUNT(*) as count FROM wallet_data');
      final tradingData = await db.rawQuery('SELECT COUNT(*) as count FROM trading_data');

      return {
        'pending_operations': pendingOps.first['count'] as int,
        'cached_data': cachedData.first['count'] as int,
        'user_data': userData.first['count'] as int,
        'wallet_data': walletData.first['count'] as int,
        'trading_data': tradingData.first['count'] as int,
      };
    } catch (e) {
      AppLogger.error('Failed to get database stats', e);
      return {};
    }
  }

  /// Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      AppLogger.info('Local database closed');
    }
  }

  /// Delete database
  Future<void> deleteDatabase() async {
    try {
      await close();
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);
      
      if (await File(path).exists()) {
        await File(path).delete();
        AppLogger.info('Local database deleted');
      }
    } catch (e) {
      AppLogger.error('Failed to delete database', e);
    }
  }
}
