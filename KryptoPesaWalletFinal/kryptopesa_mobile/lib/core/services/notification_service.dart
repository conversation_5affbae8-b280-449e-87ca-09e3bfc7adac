import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils/logger.dart';

/// Notification Service
/// Handles local and push notifications
class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  static bool _isInitialized = false;
  
  /// Initialize notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing notification service...');
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      // Request permissions
      await _requestPermissions();
      
      _isInitialized = true;
      AppLogger.info('Notification service initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize notification service', error, stackTrace);
      rethrow;
    }
  }
  
  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    
    // Create notification channels for Android
    if (defaultTargetPlatform == TargetPlatform.android) {
      await _createNotificationChannels();
    }
  }
  
  /// Create notification channels for Android
  static Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'trade_updates',
        'Trade Updates',
        description: 'Notifications about trade status changes',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('notification_sound'),
      ),
      AndroidNotificationChannel(
        'chat_messages',
        'Chat Messages',
        description: 'New chat messages from trading partners',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('message_sound'),
      ),
      AndroidNotificationChannel(
        'security_alerts',
        'Security Alerts',
        description: 'Important security notifications',
        importance: Importance.max,
        sound: RawResourceAndroidNotificationSound('alert_sound'),
      ),
      AndroidNotificationChannel(
        'general',
        'General',
        description: 'General app notifications',
        importance: Importance.defaultImportance,
      ),
    ];
    
    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  }
  
  /// Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
    
    // Get initial message if app was opened from notification
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
    
    // Get FCM token
    final token = await _firebaseMessaging.getToken();
    AppLogger.info('FCM Token: $token');
    
    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen((token) {
      AppLogger.info('FCM Token refreshed: $token');
      // TODO: Send token to backend
    });
  }
  
  /// Request notification permissions
  static Future<void> _requestPermissions() async {
    // Request local notification permission
    await Permission.notification.request();
    
    // Request Firebase messaging permission
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );
    
    AppLogger.info('Notification permission status: ${settings.authorizationStatus}');
  }
  
  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    AppLogger.info('Notification tapped: ${response.payload}');
    
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _navigateToScreen(data);
    }
  }
  
  /// Handle foreground message
  static void _handleForegroundMessage(RemoteMessage message) {
    AppLogger.info('Foreground message received: ${message.messageId}');
    
    // Show local notification for foreground messages
    showNotification(
      title: message.notification?.title ?? 'KryptoPesa',
      body: message.notification?.body ?? 'New notification',
      payload: jsonEncode(message.data),
      channelId: _getChannelId(message.data['type']),
    );
  }
  
  /// Handle notification tap from Firebase
  static void _handleNotificationTap(RemoteMessage message) {
    AppLogger.info('Notification tapped from Firebase: ${message.messageId}');
    _navigateToScreen(message.data);
  }
  
  /// Navigate to appropriate screen based on notification data
  static void _navigateToScreen(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final id = data['id'] as String?;
    
    switch (type) {
      case 'trade_update':
        if (id != null) {
          // TODO: Navigate to trade detail screen
          AppLogger.info('Navigate to trade: $id');
        }
        break;
      case 'chat_message':
        if (id != null) {
          // TODO: Navigate to chat screen
          AppLogger.info('Navigate to chat: $id');
        }
        break;
      case 'security_alert':
        // TODO: Navigate to security settings
        AppLogger.info('Navigate to security settings');
        break;
      default:
        // TODO: Navigate to home screen
        AppLogger.info('Navigate to home');
    }
  }
  
  /// Get notification channel ID based on type
  static String _getChannelId(String? type) {
    switch (type) {
      case 'trade_update':
        return 'trade_updates';
      case 'chat_message':
        return 'chat_messages';
      case 'security_alert':
        return 'security_alerts';
      default:
        return 'general';
    }
  }
  
  /// Show local notification
  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = 'general',
    int id = 0,
  }) async {
    if (!_isInitialized) return;
    
    const androidDetails = AndroidNotificationDetails(
      'general',
      'General',
      channelDescription: 'General app notifications',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(
      id,
      title,
      body,
      details,
      payload: payload,
    );
    
    AppLogger.info('Local notification shown: $title');
  }
  
  /// Show trade update notification
  static Future<void> showTradeUpdateNotification({
    required String tradeId,
    required String title,
    required String body,
  }) async {
    await showNotification(
      title: title,
      body: body,
      payload: jsonEncode({
        'type': 'trade_update',
        'id': tradeId,
      }),
      channelId: 'trade_updates',
      id: tradeId.hashCode,
    );
  }
  
  /// Show chat message notification
  static Future<void> showChatMessageNotification({
    required String chatId,
    required String senderName,
    required String message,
  }) async {
    await showNotification(
      title: senderName,
      body: message,
      payload: jsonEncode({
        'type': 'chat_message',
        'id': chatId,
      }),
      channelId: 'chat_messages',
      id: chatId.hashCode,
    );
  }
  
  /// Show security alert notification
  static Future<void> showSecurityAlertNotification({
    required String title,
    required String body,
  }) async {
    await showNotification(
      title: title,
      body: body,
      payload: jsonEncode({
        'type': 'security_alert',
      }),
      channelId: 'security_alerts',
      id: DateTime.now().millisecondsSinceEpoch,
    );
  }
  
  /// Cancel notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }
  
  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }
  
  /// Get FCM token
  static Future<String?> getFCMToken() async {
    return await _firebaseMessaging.getToken();
  }
  
  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    AppLogger.info('Subscribed to topic: $topic');
  }
  
  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    AppLogger.info('Unsubscribed from topic: $topic');
  }
  
  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }
}

/// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  AppLogger.info('Background message received: ${message.messageId}');
  
  // Handle background message
  // Note: You can't update UI from background handler
}
