import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../storage/local_storage.dart';
import '../utils/logger.dart';

/// API Client Service
/// Handles HTTP requests to the backend API
class ApiClient {
  static late Dio _dio;
  static bool _isInitialized = false;
  
  /// Initialize API client
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing API client...');
      
      final baseUrl = dotenv.env['API_BASE_URL'] ?? 'http://192.168.1.6:8000';
      AppLogger.info('ApiClient initializing with baseUrl: $baseUrl');

      _dio = Dio(BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 15), // Reduced for better UX
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ));
      
      // Add interceptors
      _dio.interceptors.add(_AuthInterceptor());
      _dio.interceptors.add(_LoggingInterceptor());
      _dio.interceptors.add(_ErrorInterceptor());
      _dio.interceptors.add(_OfflineInterceptor());
      
      _isInitialized = true;
      AppLogger.info('API client initialized with base URL: $baseUrl');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize API client', error, stackTrace);
      rethrow;
    }
  }
  
  /// GET request
  static Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
  
  /// POST request
  static Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
  
  /// PUT request
  static Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
  
  /// DELETE request
  static Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
  
  /// PATCH request
  static Future<Response<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.patch<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
  
  /// Upload file
  static Future<Response<T>> upload<T>(
    String path,
    String filePath, {
    Map<String, dynamic>? data,
    ProgressCallback? onSendProgress,
    CancelToken? cancelToken,
  }) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath),
      ...?data,
    });
    
    return await _dio.post<T>(
      path,
      data: formData,
      onSendProgress: onSendProgress,
      cancelToken: cancelToken,
    );
  }
  
  /// Download file
  static Future<Response> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    CancelToken? cancelToken,
  }) async {
    return await _dio.download(
      urlPath,
      savePath,
      onReceiveProgress: onReceiveProgress,
      cancelToken: cancelToken,
    );
  }
  
  /// Check if API client is initialized
  static bool get isInitialized => _isInitialized;
  
  /// Get Dio instance
  static Dio get dio => _dio;
}

/// Authentication Interceptor
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Add auth token to requests
    final token = LocalStorage.getAuthToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
      print('🔑 Auth token added to request: ${token.substring(0, 20)}...');
    } else {
      print('⚠️ No auth token found for request: ${options.path}');
    }

    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle token refresh on 401
    if (err.response?.statusCode == 401) {
      final refreshToken = LocalStorage.getRefreshToken();
      if (refreshToken != null) {
        try {
          // Attempt to refresh token
          final response = await ApiClient._dio.post('/auth/refresh', data: {
            'refreshToken': refreshToken,
          });
          
          final newToken = response.data['token'];
          await LocalStorage.saveAuthToken(newToken);
          
          // Retry original request
          final options = err.requestOptions;
          options.headers['Authorization'] = 'Bearer $newToken';
          
          final retryResponse = await ApiClient._dio.fetch(options);
          handler.resolve(retryResponse);
          return;
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          await LocalStorage.clearAuthTokens();
          AppLogger.warning('Token refresh failed, redirecting to login');
        }
      }
    }
    
    handler.next(err);
  }
}

/// Logging Interceptor
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.apiRequest(
      options.method,
      '${options.baseUrl}${options.path}',
      options.data,
    );
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger.apiResponse(
      response.requestOptions.method,
      '${response.requestOptions.baseUrl}${response.requestOptions.path}',
      response.statusCode ?? 0,
      response.data,
    );
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger.apiResponse(
      err.requestOptions.method,
      '${err.requestOptions.baseUrl}${err.requestOptions.path}',
      err.response?.statusCode ?? 0,
      err.response?.data ?? err.message,
    );
    handler.next(err);
  }
}

/// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    String errorMessage;
    
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = 'Send timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = 'Receive timeout. Please try again.';
        break;
      case DioExceptionType.badResponse:
        errorMessage = _handleHttpError(err.response?.statusCode ?? 0);
        break;
      case DioExceptionType.cancel:
        errorMessage = 'Request was cancelled.';
        break;
      case DioExceptionType.connectionError:
        errorMessage = 'No internet connection. Please check your network.';
        break;
      default:
        errorMessage = 'An unexpected error occurred. Please try again.';
    }
    
    // Create a new DioException with user-friendly message
    final newError = DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: errorMessage,
    );
    
    handler.next(newError);
  }
  
  String _handleHttpError(int statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Unauthorized. Please log in again.';
      case 403:
        return 'Access forbidden. You don\'t have permission.';
      case 404:
        return 'Resource not found.';
      case 409:
        return 'Conflict. The resource already exists.';
      case 422:
        return 'Validation error. Please check your input.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

/// Offline Interceptor
class _OfflineInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Check connectivity
    final connectivityResults = await Connectivity().checkConnectivity();

    if (connectivityResults.contains(ConnectivityResult.none)) {
      // No internet connection
      if (_isWriteOperation(options.method)) {
        // Queue write operations for later
        await _queueOfflineOperation(options);
        
        // Return cached response if available
        final cachedResponse = await _getCachedResponse(options);
        if (cachedResponse != null) {
          handler.resolve(cachedResponse);
          return;
        }
      } else {
        // Try to return cached response for read operations
        final cachedResponse = await _getCachedResponse(options);
        if (cachedResponse != null) {
          handler.resolve(cachedResponse);
          return;
        }
      }
      
      // No cache available, return error
      handler.reject(DioException(
        requestOptions: options,
        type: DioExceptionType.connectionError,
        error: 'No internet connection and no cached data available.',
      ));
      return;
    }
    
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Cache successful GET responses
    if (response.requestOptions.method == 'GET' && response.statusCode == 200) {
      await _cacheResponse(response);
    }
    
    handler.next(response);
  }
  
  bool _isWriteOperation(String method) {
    return ['POST', 'PUT', 'PATCH', 'DELETE'].contains(method.toUpperCase());
  }
  
  Future<void> _queueOfflineOperation(RequestOptions options) async {
    final operation = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'method': options.method,
      'path': options.path,
      'data': options.data,
      'queryParameters': options.queryParameters,
      'headers': options.headers,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    await LocalStorage.saveOfflineOperation(operation);
  }
  
  Future<Response?> _getCachedResponse(RequestOptions options) async {
    if (options.method != 'GET') return null;
    
    final cacheKey = '${options.method}_${options.path}_${options.queryParameters}';
    final cachedData = LocalStorage.getFromCache(cacheKey);
    
    if (cachedData != null) {
      return Response(
        requestOptions: options,
        data: cachedData,
        statusCode: 200,
        statusMessage: 'OK (Cached)',
      );
    }
    
    return null;
  }
  
  Future<void> _cacheResponse(Response response) async {
    final cacheKey = '${response.requestOptions.method}_${response.requestOptions.path}_${response.requestOptions.queryParameters}';
    
    await LocalStorage.saveToCache(
      cacheKey,
      response.data,
      expiry: const Duration(minutes: 30), // Cache for 30 minutes
    );
  }
}
