import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// App Logger Utility
/// Provides centralized logging with different levels and outputs
class AppLogger {
  static late Logger _logger;
  static bool _isInitialized = false;
  
  /// Initialize the logger
  static void initialize() {
    if (_isInitialized) return;
    
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
      output: _getLogOutput(),
      level: _getLogLevel(),
    );
    
    _isInitialized = true;
    info('Logger initialized');
  }
  
  /// Get log output based on environment
  static LogOutput _getLogOutput() {
    if (kDebugMode) {
      return MultiOutput([
        ConsoleOutput(),
        if (dotenv.env['ENABLE_FILE_LOGGING'] == 'true') FileOutput(),
      ]);
    } else {
      return ConsoleOutput();
    }
  }
  
  /// Get log level based on environment
  static Level _getLogLevel() {
    final logLevel = dotenv.env['LOG_LEVEL']?.toLowerCase() ?? 'info';
    
    switch (logLevel) {
      case 'verbose':
        return Level.trace;
      case 'debug':
        return Level.debug;
      case 'info':
        return Level.info;
      case 'warning':
        return Level.warning;
      case 'error':
        return Level.error;
      case 'wtf':
        return Level.fatal;
      default:
        return Level.info;
    }
  }
  
  /// Log verbose message
  static void verbose(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.t(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('VERBOSE', message, error, stackTrace);
  }
  
  /// Log debug message
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.d(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('DEBUG', message, error, stackTrace);
  }
  
  /// Log info message
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.i(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('INFO', message, error, stackTrace);
  }
  
  /// Log warning message
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.w(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('WARNING', message, error, stackTrace);
    
    // Send warning to Sentry in production
    if (!kDebugMode) {
      Sentry.captureMessage(
        message,
        level: SentryLevel.warning,
      );
    }
  }
  
  /// Log error message
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.e(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('ERROR', message, error, stackTrace);
    
    // Send error to Sentry
    Sentry.captureException(
      error ?? Exception(message),
      stackTrace: stackTrace,
    );
  }
  
  /// Log fatal error message
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_isInitialized) return;
    
    _logger.f(message, error: error, stackTrace: stackTrace);
    _logToDeveloper('FATAL', message, error, stackTrace);
    
    // Send fatal error to Sentry
    Sentry.captureException(
      error ?? Exception(message),
      stackTrace: stackTrace,
    );
  }
  
  /// Log to Flutter's developer console
  static void _logToDeveloper(
    String level,
    String message,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    developer.log(
      message,
      name: 'KryptoPesa',
      level: _getLevelValue(level),
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Get numeric level value for developer log
  static int _getLevelValue(String level) {
    switch (level) {
      case 'VERBOSE':
        return 500;
      case 'DEBUG':
        return 700;
      case 'INFO':
        return 800;
      case 'WARNING':
        return 900;
      case 'ERROR':
        return 1000;
      case 'FATAL':
        return 1200;
      default:
        return 800;
    }
  }
  
  /// Log API request
  static void apiRequest(String method, String url, Map<String, dynamic>? data) {
    debug('API Request: $method $url', data);
  }
  
  /// Log API response
  static void apiResponse(String method, String url, int statusCode, dynamic data) {
    if (statusCode >= 200 && statusCode < 300) {
      debug('API Response: $method $url [$statusCode]', data);
    } else {
      warning('API Response: $method $url [$statusCode]', data);
    }
  }
  
  /// Log user action
  static void userAction(String action, Map<String, dynamic>? context) {
    info('User Action: $action', context);
  }
  
  /// Log navigation event
  static void navigation(String from, String to) {
    debug('Navigation: $from -> $to');
  }
  
  /// Log WebSocket event
  static void websocket(String event, dynamic data) {
    debug('WebSocket: $event', data);
  }
  
  /// Log biometric authentication
  static void biometric(String action, bool success) {
    info('Biometric: $action - ${success ? 'Success' : 'Failed'}');
  }
  
  /// Log crypto operation
  static void crypto(String operation, String currency, dynamic amount) {
    info('Crypto: $operation $amount $currency');
  }
  
  /// Log trade event
  static void trade(String event, String tradeId, Map<String, dynamic>? data) {
    info('Trade: $event [$tradeId]', data);
  }
  
  /// Log security event
  static void security(String event, Map<String, dynamic>? context) {
    warning('Security: $event', context);
  }
  
  /// Log performance metric
  static void performance(String metric, Duration duration) {
    debug('Performance: $metric took ${duration.inMilliseconds}ms');
  }
  
  /// Log offline operation
  static void offline(String operation, Map<String, dynamic>? data) {
    info('Offline: $operation', data);
  }
  
  /// Check if logger is initialized
  static bool get isInitialized => _isInitialized;
}

/// Custom file output for logger
class FileOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // TODO: Implement file logging if needed
    // This would write logs to a file for debugging purposes
  }
}
