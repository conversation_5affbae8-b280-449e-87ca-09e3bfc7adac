import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/logger.dart';

/// Local Storage Service
/// Handles local data persistence using Hive and SharedPreferences
class LocalStorage {
  static late Box _userBox;
  static late Box _walletBox;
  static late Box _tradeBox;
  static late Box _chatBox;
  static late Box _settingsBox;
  static late Box _cacheBox;
  static late SharedPreferences _prefs;
  
  static bool _isInitialized = false;
  
  /// Initialize local storage
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing local storage...');
      
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      
      // Initialize Hive boxes
      _userBox = await Hive.openBox('user');
      _walletBox = await Hive.openBox('wallet');
      _tradeBox = await Hive.openBox('trade');
      _chatBox = await Hive.openBox('chat');
      _settingsBox = await Hive.openBox('settings');
      _cacheBox = await Hive.openBox('cache');
      
      _isInitialized = true;
      AppLogger.info('Local storage initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize local storage', error, stackTrace);
      rethrow;
    }
  }
  
  /// User Storage Methods
  static Future<void> saveUser(Map<String, dynamic> userData) async {
    await _userBox.put('current_user', userData);
    AppLogger.info('User data saved');
  }
  
  static Map<String, dynamic>? getUser() {
    final userData = _userBox.get('current_user');
    return userData != null ? Map<String, dynamic>.from(userData) : null;
  }
  
  static Future<void> clearUser() async {
    await _userBox.delete('current_user');
    AppLogger.info('User data cleared');
  }
  
  /// Authentication Storage Methods
  static Future<void> saveAuthToken(String token) async {
    await _prefs.setString('auth_token', token);
    AppLogger.info('Auth token saved');
  }
  
  static String? getAuthToken() {
    return _prefs.getString('auth_token');
  }
  
  static Future<void> saveRefreshToken(String token) async {
    await _prefs.setString('refresh_token', token);
    AppLogger.info('Refresh token saved');
  }
  
  static String? getRefreshToken() {
    return _prefs.getString('refresh_token');
  }
  
  static Future<void> clearAuthTokens() async {
    await _prefs.remove('auth_token');
    await _prefs.remove('refresh_token');
    AppLogger.info('Auth tokens cleared');
  }
  
  /// Wallet Storage Methods
  static Future<void> saveWalletData(String walletId, Map<String, dynamic> walletData) async {
    await _walletBox.put(walletId, walletData);
    AppLogger.info('Wallet data saved: $walletId');
  }
  
  static Map<String, dynamic>? getWalletData(String walletId) {
    final walletData = _walletBox.get(walletId);
    return walletData != null ? Map<String, dynamic>.from(walletData) : null;
  }
  
  static List<Map<String, dynamic>> getAllWallets() {
    return _walletBox.values
        .map((wallet) => Map<String, dynamic>.from(wallet))
        .toList();
  }
  
  static Future<void> deleteWallet(String walletId) async {
    await _walletBox.delete(walletId);
    AppLogger.info('Wallet deleted: $walletId');
  }
  
  /// Transaction Storage Methods
  static Future<void> saveTransaction(String txId, Map<String, dynamic> txData) async {
    await _walletBox.put('tx_$txId', txData);
    AppLogger.info('Transaction saved: $txId');
  }
  
  static Map<String, dynamic>? getTransaction(String txId) {
    final txData = _walletBox.get('tx_$txId');
    return txData != null ? Map<String, dynamic>.from(txData) : null;
  }
  
  static List<Map<String, dynamic>> getAllTransactions() {
    return _walletBox.keys
        .where((key) => key.toString().startsWith('tx_'))
        .map((key) => Map<String, dynamic>.from(_walletBox.get(key)))
        .toList();
  }
  
  /// Trade Storage Methods
  static Future<void> saveTrade(String tradeId, Map<String, dynamic> tradeData) async {
    await _tradeBox.put(tradeId, tradeData);
    AppLogger.info('Trade saved: $tradeId');
  }
  
  static Map<String, dynamic>? getTrade(String tradeId) {
    final tradeData = _tradeBox.get(tradeId);
    return tradeData != null ? Map<String, dynamic>.from(tradeData) : null;
  }
  
  static List<Map<String, dynamic>> getAllTrades() {
    return _tradeBox.values
        .map((trade) => Map<String, dynamic>.from(trade))
        .toList();
  }
  
  static Future<void> deleteTrade(String tradeId) async {
    await _tradeBox.delete(tradeId);
    AppLogger.info('Trade deleted: $tradeId');
  }
  
  /// Chat Storage Methods
  static Future<void> saveChat(String chatId, Map<String, dynamic> chatData) async {
    await _chatBox.put(chatId, chatData);
    AppLogger.info('Chat saved: $chatId');
  }
  
  static Map<String, dynamic>? getChat(String chatId) {
    final chatData = _chatBox.get(chatId);
    return chatData != null ? Map<String, dynamic>.from(chatData) : null;
  }
  
  static List<Map<String, dynamic>> getAllChats() {
    return _chatBox.values
        .map((chat) => Map<String, dynamic>.from(chat))
        .toList();
  }
  
  static Future<void> saveMessage(String chatId, String messageId, Map<String, dynamic> messageData) async {
    await _chatBox.put('${chatId}_$messageId', messageData);
    AppLogger.info('Message saved: $chatId/$messageId');
  }
  
  static List<Map<String, dynamic>> getChatMessages(String chatId) {
    return _chatBox.keys
        .where((key) => key.toString().startsWith('${chatId}_'))
        .map((key) => Map<String, dynamic>.from(_chatBox.get(key)))
        .toList();
  }
  
  /// Settings Storage Methods
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBox.put(key, value);
    AppLogger.info('Setting saved: $key');
  }
  
  static T? getSetting<T>(String key) {
    return _settingsBox.get(key) as T?;
  }
  
  static Future<void> deleteSetting(String key) async {
    await _settingsBox.delete(key);
    AppLogger.info('Setting deleted: $key');
  }
  
  /// Cache Storage Methods
  static Future<void> saveToCache(String key, dynamic value, {Duration? expiry}) async {
    final cacheData = {
      'value': value,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await _cacheBox.put(key, cacheData);
    AppLogger.info('Data cached: $key');
  }
  
  static T? getFromCache<T>(String key) {
    final cacheData = _cacheBox.get(key);
    if (cacheData == null) return null;
    
    final data = Map<String, dynamic>.from(cacheData);
    final timestamp = data['timestamp'] as int;
    final expiry = data['expiry'] as int?;
    
    // Check if cache has expired
    if (expiry != null) {
      final expiryTime = timestamp + expiry;
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        _cacheBox.delete(key);
        return null;
      }
    }
    
    return data['value'] as T?;
  }
  
  static Future<void> clearCache() async {
    await _cacheBox.clear();
    AppLogger.info('Cache cleared');
  }
  
  /// Offline Operations Storage
  static Future<void> saveOfflineOperation(Map<String, dynamic> operation) async {
    final operations = getOfflineOperations();
    operations.add(operation);
    await _cacheBox.put('offline_operations', operations);
    AppLogger.offline('Operation queued', operation);
  }
  
  static List<Map<String, dynamic>> getOfflineOperations() {
    final operations = _cacheBox.get('offline_operations');
    if (operations == null) return [];
    return List<Map<String, dynamic>>.from(operations);
  }
  
  static Future<void> removeOfflineOperation(String operationId) async {
    final operations = getOfflineOperations();
    operations.removeWhere((op) => op['id'] == operationId);
    await _cacheBox.put('offline_operations', operations);
    AppLogger.offline('Operation removed', {'id': operationId});
  }
  
  static Future<void> clearOfflineOperations() async {
    await _cacheBox.delete('offline_operations');
    AppLogger.offline('All operations cleared', null);
  }
  
  /// Utility Methods
  static Future<void> clearAllData() async {
    await _userBox.clear();
    await _walletBox.clear();
    await _tradeBox.clear();
    await _chatBox.clear();
    await _settingsBox.clear();
    await _cacheBox.clear();
    await _prefs.clear();
    AppLogger.info('All local data cleared');
  }
  
  static Future<int> getStorageSize() async {
    // Calculate approximate storage size
    int size = 0;
    size += _userBox.length;
    size += _walletBox.length;
    size += _tradeBox.length;
    size += _chatBox.length;
    size += _settingsBox.length;
    size += _cacheBox.length;
    return size;
  }
  
  /// Check if storage is initialized
  static bool get isInitialized => _isInitialized;
}
