import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'dart:convert';
import 'dart:typed_data';

import '../utils/logger.dart';

/// Secure Storage Service
/// Handles sensitive data storage with encryption
class SecureStorage {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'kryptopesa_secure_prefs',
      preferencesKeyPrefix: 'kryptopesa_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.kryptopesa.mobile',
      accountName: 'KryptoPesa',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  
  static late Encrypter _encrypter;
  static late IV _iv;
  static bool _isInitialized = false;
  
  /// Initialize secure storage
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('Initializing secure storage...');
      
      // Initialize encryption
      await _initializeEncryption();
      
      _isInitialized = true;
      AppLogger.info('Secure storage initialized');
      
    } catch (error, stackTrace) {
      AppLogger.error('Failed to initialize secure storage', error, stackTrace);
      rethrow;
    }
  }
  
  /// Initialize encryption
  static Future<void> _initializeEncryption() async {
    // Get or create encryption key
    String? keyString = await _secureStorage.read(key: 'encryption_key');
    
    if (keyString == null) {
      // Generate new encryption key
      final key = Key.fromSecureRandom(32);
      keyString = key.base64;
      await _secureStorage.write(key: 'encryption_key', value: keyString);
      AppLogger.info('New encryption key generated');
    }
    
    final key = Key.fromBase64(keyString);
    _encrypter = Encrypter(AES(key));
    _iv = IV.fromSecureRandom(16);
  }
  
  /// Encrypt data
  static String _encrypt(String data) {
    final encrypted = _encrypter.encrypt(data, iv: _iv);
    return '${_iv.base64}:${encrypted.base64}';
  }
  
  /// Decrypt data
  static String _decrypt(String encryptedData) {
    final parts = encryptedData.split(':');
    if (parts.length != 2) throw Exception('Invalid encrypted data format');
    
    final iv = IV.fromBase64(parts[0]);
    final encrypted = Encrypted.fromBase64(parts[1]);
    return _encrypter.decrypt(encrypted, iv: iv);
  }
  
  /// Store encrypted data
  static Future<void> write(String key, String value) async {
    if (!_isInitialized) throw Exception('Secure storage not initialized');
    
    try {
      final encryptedValue = _encrypt(value);
      await _secureStorage.write(key: key, value: encryptedValue);
      AppLogger.info('Secure data written: $key');
    } catch (error, stackTrace) {
      AppLogger.error('Failed to write secure data: $key', error, stackTrace);
      rethrow;
    }
  }
  
  /// Read encrypted data
  static Future<String?> read(String key) async {
    if (!_isInitialized) throw Exception('Secure storage not initialized');
    
    try {
      final encryptedValue = await _secureStorage.read(key: key);
      if (encryptedValue == null) return null;
      
      return _decrypt(encryptedValue);
    } catch (error, stackTrace) {
      AppLogger.error('Failed to read secure data: $key', error, stackTrace);
      return null;
    }
  }
  
  /// Delete data
  static Future<void> delete(String key) async {
    if (!_isInitialized) throw Exception('Secure storage not initialized');
    
    try {
      await _secureStorage.delete(key: key);
      AppLogger.info('Secure data deleted: $key');
    } catch (error, stackTrace) {
      AppLogger.error('Failed to delete secure data: $key', error, stackTrace);
      rethrow;
    }
  }
  
  /// Store JSON data
  static Future<void> writeJson(String key, Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    await write(key, jsonString);
  }
  
  /// Read JSON data
  static Future<Map<String, dynamic>?> readJson(String key) async {
    final jsonString = await read(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (error) {
      AppLogger.error('Failed to parse JSON for key: $key', error);
      return null;
    }
  }
  
  /// Wallet-specific methods
  static Future<void> storeMnemonic(String mnemonic) async {
    await write('wallet_mnemonic', mnemonic);
    AppLogger.security('Mnemonic stored', {'action': 'store_mnemonic'});
  }
  
  static Future<String?> getMnemonic() async {
    final mnemonic = await read('wallet_mnemonic');
    if (mnemonic != null) {
      AppLogger.security('Mnemonic accessed', {'action': 'get_mnemonic'});
    }
    return mnemonic;
  }
  
  static Future<void> storePrivateKey(String address, String privateKey) async {
    await write('private_key_$address', privateKey);
    AppLogger.security('Private key stored', {'address': address});
  }
  
  static Future<String?> getPrivateKey(String address) async {
    final privateKey = await read('private_key_$address');
    if (privateKey != null) {
      AppLogger.security('Private key accessed', {'address': address});
    }
    return privateKey;
  }
  
  static Future<void> storeWalletPassword(String password) async {
    // Hash the password before storing
    final hashedPassword = _hashPassword(password);
    await write('wallet_password_hash', hashedPassword);
    AppLogger.security('Wallet password stored', {'action': 'store_password'});
  }
  
  static Future<bool> verifyWalletPassword(String password) async {
    final storedHash = await read('wallet_password_hash');
    if (storedHash == null) return false;
    
    final inputHash = _hashPassword(password);
    final isValid = storedHash == inputHash;
    
    AppLogger.security('Wallet password verified', {
      'action': 'verify_password',
      'success': isValid,
    });
    
    return isValid;
  }
  
  /// Authentication-specific methods
  static Future<void> storeBiometricKey(String key) async {
    await write('biometric_key', key);
    AppLogger.security('Biometric key stored', {'action': 'store_biometric'});
  }
  
  static Future<String?> getBiometricKey() async {
    final key = await read('biometric_key');
    if (key != null) {
      AppLogger.security('Biometric key accessed', {'action': 'get_biometric'});
    }
    return key;
  }

  /// Check if biometric authentication is enabled
  static Future<bool> isBiometricEnabled() async {
    final biometricKey = await getBiometricKey();
    return biometricKey != null;
  }
  
  static Future<void> storePinHash(String pin) async {
    final hashedPin = _hashPassword(pin);
    await write('pin_hash', hashedPin);
    AppLogger.security('PIN stored', {'action': 'store_pin'});
  }
  
  static Future<bool> verifyPin(String pin) async {
    final storedHash = await read('pin_hash');
    if (storedHash == null) return false;
    
    final inputHash = _hashPassword(pin);
    final isValid = storedHash == inputHash;
    
    AppLogger.security('PIN verified', {
      'action': 'verify_pin',
      'success': isValid,
    });
    
    return isValid;
  }
  
  /// Trading-specific methods
  static Future<void> storeEscrowKey(String tradeId, String key) async {
    await write('escrow_key_$tradeId', key);
    AppLogger.security('Escrow key stored', {'tradeId': tradeId});
  }
  
  static Future<String?> getEscrowKey(String tradeId) async {
    final key = await read('escrow_key_$tradeId');
    if (key != null) {
      AppLogger.security('Escrow key accessed', {'tradeId': tradeId});
    }
    return key;
  }
  
  static Future<void> deleteEscrowKey(String tradeId) async {
    await delete('escrow_key_$tradeId');
    AppLogger.security('Escrow key deleted', {'tradeId': tradeId});
  }
  
  /// API keys and tokens
  static Future<void> storeApiKey(String service, String apiKey) async {
    await write('api_key_$service', apiKey);
    AppLogger.security('API key stored', {'service': service});
  }
  
  static Future<String?> getApiKey(String service) async {
    return await read('api_key_$service');
  }
  
  static Future<void> storeEncryptedBackup(Map<String, dynamic> backupData) async {
    await writeJson('encrypted_backup', backupData);
    AppLogger.security('Encrypted backup stored', {'action': 'store_backup'});
  }
  
  static Future<Map<String, dynamic>?> getEncryptedBackup() async {
    final backup = await readJson('encrypted_backup');
    if (backup != null) {
      AppLogger.security('Encrypted backup accessed', {'action': 'get_backup'});
    }
    return backup;
  }
  
  /// Utility methods
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  static Future<List<String>> getAllKeys() async {
    try {
      final allKeys = await _secureStorage.readAll();
      return allKeys.keys.toList();
    } catch (error) {
      AppLogger.error('Failed to get all keys', error);
      return [];
    }
  }
  
  static Future<void> clearAll() async {
    try {
      await _secureStorage.deleteAll();
      AppLogger.security('All secure data cleared', {'action': 'clear_all'});
    } catch (error, stackTrace) {
      AppLogger.error('Failed to clear secure storage', error, stackTrace);
      rethrow;
    }
  }
  
  static Future<bool> containsKey(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      return value != null;
    } catch (error) {
      return false;
    }
  }
  
  /// Check if secure storage is initialized
  static bool get isInitialized => _isInitialized;
}
