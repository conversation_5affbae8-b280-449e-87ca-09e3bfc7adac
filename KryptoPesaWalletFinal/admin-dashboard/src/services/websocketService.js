/**
 * Enhanced WebSocket Service for Admin Dashboard
 * Provides real-time updates, notifications, and system monitoring
 */

import io from 'socket.io-client';
import { store } from '../store';
import { addNotification, updateSystemHealth, updateLiveStats } from '../store/slices/adminSlice';
import crashReporting from './crashReporting';

class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 1000;
    this.heartbeatInterval = null;
    this.subscriptions = new Map();
    this.messageQueue = [];
    this.connectionListeners = [];
    
    // Event handlers
    this.eventHandlers = {
      'admin:notification': this.handleNotification.bind(this),
      'admin:system_health': this.handleSystemHealth.bind(this),
      'admin:live_stats': this.handleLiveStats.bind(this),
      'admin:trade_update': this.handleTradeUpdate.bind(this),
      'admin:user_activity': this.handleUserActivity.bind(this),
      'admin:security_alert': this.handleSecurityAlert.bind(this),
      'admin:system_alert': this.handleSystemAlert.bind(this),
      'admin:performance_metrics': this.handlePerformanceMetrics.bind(this)
    };
  }

  // Initialize WebSocket connection
  async initialize() {
    try {
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('No admin token available');
      }

      const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:3000';
      
      this.socket = io(wsUrl, {
        auth: {
          token,
          type: 'admin'
        },
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: true,
        timeout: 20000,
        forceNew: true
      });

      this.setupEventListeners();
      this.startHeartbeat();
      
      console.log('WebSocket service initialized');
      return true;
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
      crashReporting.captureError(error, { context: 'websocket_init' });
      return false;
    }
  }

  // Setup event listeners
  setupEventListeners() {
    // Connection events
    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Process queued messages
      this.processMessageQueue();
      
      // Notify listeners
      this.notifyConnectionListeners('connected');
      
      // Subscribe to admin channels
      this.subscribeToAdminChannels();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      this.stopHeartbeat();
      
      // Notify listeners
      this.notifyConnectionListeners('disconnected', reason);
      
      // Attempt reconnection for certain reasons
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        console.log('Server disconnected client, not attempting reconnection');
      } else {
        this.attemptReconnection();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      crashReporting.captureError(error, { context: 'websocket_connection' });
      
      this.attemptReconnection();
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      crashReporting.captureError(error, { context: 'websocket_error' });
    });

    // Admin-specific events
    Object.keys(this.eventHandlers).forEach(event => {
      this.socket.on(event, this.eventHandlers[event]);
    });

    // Heartbeat response
    this.socket.on('pong', (data) => {
      console.log('Heartbeat response received:', data);
    });
  }

  // Subscribe to admin channels
  subscribeToAdminChannels() {
    const channels = [
      'admin:notifications',
      'admin:system_health',
      'admin:live_stats',
      'admin:trade_updates',
      'admin:user_activity',
      'admin:security_alerts',
      'admin:system_alerts',
      'admin:performance_metrics'
    ];

    channels.forEach(channel => {
      this.subscribe(channel);
    });
  }

  // Subscribe to a channel
  subscribe(channel, callback = null) {
    if (!this.isConnected) {
      console.warn('Cannot subscribe - WebSocket not connected');
      return false;
    }

    this.socket.emit('subscribe', { channel });
    
    if (callback) {
      this.subscriptions.set(channel, callback);
    }
    
    console.log(`Subscribed to channel: ${channel}`);
    return true;
  }

  // Unsubscribe from a channel
  unsubscribe(channel) {
    if (!this.isConnected) {
      return false;
    }

    this.socket.emit('unsubscribe', { channel });
    this.subscriptions.delete(channel);
    
    console.log(`Unsubscribed from channel: ${channel}`);
    return true;
  }

  // Send message
  send(event, data) {
    if (!this.isConnected) {
      // Queue message for later
      this.messageQueue.push({ event, data, timestamp: Date.now() });
      console.log('Message queued - WebSocket not connected');
      return false;
    }

    this.socket.emit(event, data);
    return true;
  }

  // Process queued messages
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      
      // Check if message is not too old (5 minutes)
      if (Date.now() - message.timestamp < 5 * 60 * 1000) {
        this.socket.emit(message.event, message.data);
      }
    }
  }

  // Start heartbeat
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    }, 30000); // Every 30 seconds
  }

  // Stop heartbeat
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  // Attempt reconnection
  attemptReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000);
    
    console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.socket.connect();
      }
    }, delay);
  }

  // Event handlers
  handleNotification(data) {
    console.log('Received notification:', data);
    store.dispatch(addNotification(data));
    
    // Show browser notification if permission granted
    this.showBrowserNotification(data);
  }

  handleSystemHealth(data) {
    console.log('System health update:', data);
    store.dispatch(updateSystemHealth(data));
  }

  handleLiveStats(data) {
    console.log('Live stats update:', data);
    store.dispatch(updateLiveStats(data));
  }

  handleTradeUpdate(data) {
    console.log('Trade update:', data);
    // Handle trade-specific updates
    store.dispatch(addNotification({
      type: 'trade_update',
      title: 'Trade Update',
      message: `Trade ${data.tradeId} status changed to ${data.status}`,
      data
    }));
  }

  handleUserActivity(data) {
    console.log('User activity:', data);
    // Handle user activity updates
  }

  handleSecurityAlert(data) {
    console.log('Security alert:', data);
    store.dispatch(addNotification({
      type: 'security_alert',
      title: 'Security Alert',
      message: data.message,
      severity: 'error',
      data
    }));
    
    // Log security event
    crashReporting.captureError(new Error('Security Alert'), {
      context: 'security_alert',
      alert: data
    });
  }

  handleSystemAlert(data) {
    console.log('System alert:', data);
    store.dispatch(addNotification({
      type: 'system_alert',
      title: 'System Alert',
      message: data.message,
      severity: data.severity || 'warning',
      data
    }));
  }

  handlePerformanceMetrics(data) {
    console.log('Performance metrics:', data);
    // Handle performance metrics updates
  }

  // Show browser notification
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title || 'KryptoPesa Admin', {
        body: notification.message,
        icon: '/icon-192x192.png',
        tag: notification.type || 'admin',
        requireInteraction: notification.severity === 'error'
      });
    }
  }

  // Add connection listener
  addConnectionListener(callback) {
    this.connectionListeners.push(callback);
  }

  // Remove connection listener
  removeConnectionListener(callback) {
    this.connectionListeners = this.connectionListeners.filter(cb => cb !== callback);
  }

  // Notify connection listeners
  notifyConnectionListeners(status, data = null) {
    this.connectionListeners.forEach(callback => {
      try {
        callback(status, data);
      } catch (error) {
        console.error('Connection listener error:', error);
      }
    });
  }

  // Get connection status
  getStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
      subscriptions: Array.from(this.subscriptions.keys())
    };
  }

  // Disconnect
  disconnect() {
    if (this.socket) {
      this.stopHeartbeat();
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  // Cleanup
  cleanup() {
    this.disconnect();
    this.subscriptions.clear();
    this.messageQueue = [];
    this.connectionListeners = [];
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

// React hook for WebSocket functionality
export const useWebSocket = () => {
  const [status, setStatus] = React.useState(websocketService.getStatus());

  React.useEffect(() => {
    const updateStatus = () => setStatus(websocketService.getStatus());

    websocketService.addConnectionListener(updateStatus);

    // Update status periodically
    const interval = setInterval(updateStatus, 5000);

    return () => {
      websocketService.removeConnectionListener(updateStatus);
      clearInterval(interval);
    };
  }, []);

  return {
    ...status,
    send: websocketService.send.bind(websocketService),
    subscribe: websocketService.subscribe.bind(websocketService),
    unsubscribe: websocketService.unsubscribe.bind(websocketService)
  };
};

export default websocketService;
