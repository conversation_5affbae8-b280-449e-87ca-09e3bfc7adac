import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? 'https://api.kryptopesa.com/api'
    : 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const adminAPI = {
  // Authentication
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    logout: () => api.post('/auth/logout'),
    getCurrentUser: () => api.get('/auth/me'),
  },

  // Dashboard
  dashboard: {
    getStats: () => api.get('/admin/dashboard/stats'),
    getRealtimeMetrics: () => api.get('/metrics/realtime'),
    getApplicationMetrics: () => api.get('/metrics/application'),
  },

  // User Management
  users: {
    getUsers: (params) => api.get('/admin/users', { params }),
    getUserById: (id) => api.get(`/admin/users/${id}`),
    updateUser: (id, data) => api.put(`/admin/users/${id}`, data),
    suspendUser: (id, reason) => api.post(`/admin/users/${id}/suspend`, { reason }),
    unsuspendUser: (id) => api.post(`/admin/users/${id}/unsuspend`),
    banUser: (id, reason) => api.post(`/admin/users/${id}/ban`, { reason }),
    unbanUser: (id) => api.post(`/admin/users/${id}/unban`),
    verifyUser: (id) => api.post(`/admin/users/${id}/verify`),
    unverifyUser: (id) => api.post(`/admin/users/${id}/unverify`),
  },

  // Trade Management
  trades: {
    getTrades: (params) => api.get('/admin/trades', { params }),
    getTradeById: (id) => api.get(`/admin/trades/${id}`),
    updateTrade: (id, data) => api.put(`/admin/trades/${id}`, data),
    cancelTrade: (id, reason) => api.post(`/admin/trades/${id}/cancel`, { reason }),
    completeTrade: (id) => api.post(`/admin/trades/${id}/complete`),
  },

  // Offer Management
  offers: {
    getOffers: (params) => api.get('/admin/offers', { params }),
    getOfferById: (id) => api.get(`/admin/offers/${id}`),
    updateOffer: (id, data) => api.put(`/admin/offers/${id}`, data),
    deactivateOffer: (id, reason) => api.post(`/admin/offers/${id}/deactivate`, { reason }),
    activateOffer: (id) => api.post(`/admin/offers/${id}/activate`),
  },

  // Dispute Management
  disputes: {
    getDisputes: (params) => api.get('/admin/disputes', { params }),
    getDisputeById: (id) => api.get(`/admin/disputes/${id}`),
    updateDispute: (id, data) => api.put(`/admin/disputes/${id}`, data),
    assignDispute: (id, adminId) => api.post(`/admin/disputes/${id}/assign`, { adminId }),
    resolveDispute: (id, resolution) => api.post(`/admin/disputes/${id}/resolve`, resolution),
    escalateDispute: (id, reason) => api.post(`/admin/disputes/${id}/escalate`, { reason }),
  },

  // System Management
  system: {
    getSystemHealth: () => api.get('/health/detailed'),
    getMetrics: () => api.get('/metrics'),
    getPrometheusMetrics: () => api.get('/metrics/prometheus'),
    resetMetrics: () => api.post('/metrics/reset'),
  },

  // Analytics
  analytics: {
    getUserAnalytics: (params) => api.get('/admin/analytics/users', { params }),
    getTradeAnalytics: (params) => api.get('/admin/analytics/trades', { params }),
    getRevenueAnalytics: (params) => api.get('/admin/analytics/revenue', { params }),
    getPerformanceAnalytics: (params) => api.get('/admin/analytics/performance', { params }),
  },
};

// WebSocket connection for real-time updates
export class AdminWebSocket {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const token = localStorage.getItem('admin_token');
    if (!token) {
      console.error('No admin token available for WebSocket connection');
      return;
    }

    const wsUrl = process.env.NODE_ENV === 'production'
      ? 'wss://api.kryptopesa.com'
      : 'ws://localhost:3000';

    try {
      this.socket = new WebSocket(`${wsUrl}?token=${token}`);

      this.socket.onopen = () => {
        console.log('Admin WebSocket connected');
        this.reconnectAttempts = 0;
        this.emit('connected');
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.emit(data.type, data.payload);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.socket.onclose = () => {
        console.log('Admin WebSocket disconnected');
        this.emit('disconnected');
        this.attemptReconnect();
      };

      this.socket.onerror = (error) => {
        console.error('Admin WebSocket error:', error);
        this.emit('error', error);
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      setTimeout(() => {
        console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebSocket event callback:', error);
        }
      });
    }
  }

  send(data) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket not connected, cannot send data');
    }
  }
}

// Create singleton WebSocket instance
export const adminWebSocket = new AdminWebSocket();

// Utility functions
export const formatError = (error) => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const handleApiError = (error, defaultMessage = 'Operation failed') => {
  const message = formatError(error);
  console.error('API Error:', message);
  return message;
};

export default api;
