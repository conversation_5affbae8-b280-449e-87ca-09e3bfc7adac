import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  CheckCircle as CompleteIcon,
  Cancel as CancelIcon,
  Warning as DisputeIcon,
} from '@mui/icons-material';
import axios from 'axios';

const TradesPage = () => {
  const [trades, setTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalTrades, setTotalTrades] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedTrade, setSelectedTrade] = useState(null);
  const [tradeDetailOpen, setTradeDetailOpen] = useState(false);

  // Fetch trades data
  const fetchTrades = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };

      const response = await axios.get('/admin/trades', { params });

      if (response.data.success) {
        setTrades(response.data.data.trades);
        setTotalTrades(response.data.data.pagination.totalTrades);
      }
    } catch (error) {
      console.error('Error fetching trades:', error);
      setError('Failed to fetch trades');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrades();
  }, [page, rowsPerPage, searchTerm, statusFilter]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleViewTrade = (trade) => {
    setSelectedTrade(trade);
    setTradeDetailOpen(true);
  };

  const handleTradeAction = async (trade, action) => {
    try {
      const response = await axios.post(`/admin/trades/${trade.tradeId}/intervene`, {
        action,
        reason: `Admin ${action} action`
      });

      if (response.data.success) {
        fetchTrades(); // Refresh the list
      }
    } catch (error) {
      console.error('Error performing trade action:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      case 'disputed': return 'warning';
      case 'active': return 'info';
      case 'funded': return 'primary';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CompleteIcon />;
      case 'cancelled': return <CancelIcon />;
      case 'disputed': return <DisputeIcon />;
      default: return null;
    }
  };

  const formatAmount = (amount, currency) => {
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Trade Management
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Trades
              </Typography>
              <Typography variant="h4">
                {totalTrades.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Trades
              </Typography>
              <Typography variant="h4">
                {trades.filter(t => ['created', 'funded', 'payment_sent'].includes(t.status)).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed Today
              </Typography>
              <Typography variant="h4">
                {trades.filter(t =>
                  t.status === 'completed' &&
                  new Date(t.updatedAt).toDateString() === new Date().toDateString()
                ).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Disputes
              </Typography>
              <Typography variant="h4" color="warning.main">
                {trades.filter(t => t.status === 'disputed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search trades..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="created">Created</MenuItem>
                <MenuItem value="funded">Funded</MenuItem>
                <MenuItem value="payment_sent">Payment Sent</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="disputed">Disputed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              variant="outlined"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
              }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Trades Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Trade ID</TableCell>
              <TableCell>Participants</TableCell>
              <TableCell>Amount</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Updated</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {trades.map((trade) => (
              <TableRow key={trade._id} hover>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {trade.tradeId}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      <strong>Seller:</strong> {trade.seller?.username || 'Unknown'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Buyer:</strong> {trade.buyer?.username || 'Unknown'}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      {formatAmount(trade.cryptocurrency?.amount, trade.cryptocurrency?.symbol)}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ≈ {formatAmount(trade.fiat?.amount, trade.fiat?.currency)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(trade.status)}
                    label={trade.status.replace('_', ' ')}
                    color={getStatusColor(trade.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(trade.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(trade.updatedAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewTrade(trade)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>

                    {trade.status === 'disputed' && (
                      <Tooltip title="Force Complete">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={() => handleTradeAction(trade, 'force_complete')}
                        >
                          <CompleteIcon />
                        </IconButton>
                      </Tooltip>
                    )}

                    {['created', 'funded', 'payment_sent'].includes(trade.status) && (
                      <Tooltip title="Cancel Trade">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleTradeAction(trade, 'cancel')}
                        >
                          <BlockIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={totalTrades}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Trade Detail Dialog */}
      <Dialog
        open={tradeDetailOpen}
        onClose={() => setTradeDetailOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Trade Details - {selectedTrade?.tradeId}
        </DialogTitle>
        <DialogContent>
          {selectedTrade && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Trade Information
                  </Typography>
                  <Typography><strong>Status:</strong> {selectedTrade.status}</Typography>
                  <Typography><strong>Created:</strong> {formatDate(selectedTrade.createdAt)}</Typography>
                  <Typography><strong>Updated:</strong> {formatDate(selectedTrade.updatedAt)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Amount Details
                  </Typography>
                  <Typography>
                    <strong>Crypto:</strong> {formatAmount(selectedTrade.cryptocurrency?.amount, selectedTrade.cryptocurrency?.symbol)}
                  </Typography>
                  <Typography>
                    <strong>Fiat:</strong> {formatAmount(selectedTrade.fiat?.amount, selectedTrade.fiat?.currency)}
                  </Typography>
                  <Typography>
                    <strong>Rate:</strong> {selectedTrade.fiat?.exchangeRate}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Participants
                  </Typography>
                  <Typography>
                    <strong>Seller:</strong> {selectedTrade.seller?.username} ({selectedTrade.seller?.email})
                  </Typography>
                  <Typography>
                    <strong>Buyer:</strong> {selectedTrade.buyer?.username} ({selectedTrade.buyer?.email})
                  </Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTradeDetailOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TradesPage;
