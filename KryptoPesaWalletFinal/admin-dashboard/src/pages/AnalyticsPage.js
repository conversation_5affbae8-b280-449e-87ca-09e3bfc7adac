import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  SwapHoriz as SwapHorizIcon,
  AccountBalance as AccountBalanceIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import axios from 'axios';

const AnalyticsPage = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');

  // Fetch analytics data
  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/admin/dashboard/stats');

      if (response.data.success) {
        setAnalytics(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  // Mock data for demonstration (replace with real data from API)
  const mockChartData = {
    userGrowth: [
      { date: '2024-01-01', users: 100 },
      { date: '2024-01-02', users: 120 },
      { date: '2024-01-03', users: 150 },
      { date: '2024-01-04', users: 180 },
      { date: '2024-01-05', users: 200 },
      { date: '2024-01-06', users: 230 },
      { date: '2024-01-07', users: 250 }
    ],
    tradeVolume: [
      { date: '2024-01-01', volume: 10000 },
      { date: '2024-01-02', volume: 15000 },
      { date: '2024-01-03', volume: 12000 },
      { date: '2024-01-04', volume: 18000 },
      { date: '2024-01-05', volume: 22000 },
      { date: '2024-01-06', volume: 25000 },
      { date: '2024-01-07', volume: 28000 }
    ]
  };

  const StatCard = ({ title, value, change, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {change && (
              <Box display="flex" alignItems="center" mt={1}>
                {change > 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change > 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Analytics Dashboard
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Analytics Dashboard
        </Typography>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" gutterBottom>
          Analytics Dashboard
        </Typography>

        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="24h">Last 24 Hours</MenuItem>
            <MenuItem value="7d">Last 7 Days</MenuItem>
            <MenuItem value="30d">Last 30 Days</MenuItem>
            <MenuItem value="90d">Last 90 Days</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={analytics?.statistics?.users?.total?.toLocaleString() || '0'}
            change={5.2}
            icon={<PeopleIcon fontSize="large" />}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Trades"
            value={analytics?.statistics?.trades?.active || '0'}
            change={-2.1}
            icon={<SwapHorizIcon fontSize="large" />}
            color="info"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Volume (USD)"
            value={`$${(analytics?.statistics?.trades?.volume || 0).toLocaleString()}`}
            change={12.5}
            icon={<AccountBalanceIcon fontSize="large" />}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Disputes"
            value={analytics?.statistics?.disputes?.pending || '0'}
            change={0}
            icon={<WarningIcon fontSize="large" />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              User Growth
            </Typography>
            <Box height={300} display="flex" alignItems="center" justifyContent="center">
              <Typography color="textSecondary">
                Chart visualization would be implemented here using a library like Chart.js or Recharts
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Trading Volume
            </Typography>
            <Box height={300} display="flex" alignItems="center" justifyContent="center">
              <Typography color="textSecondary">
                Chart visualization would be implemented here using a library like Chart.js or Recharts
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activity Tables */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Trades
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Trade ID</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {analytics?.recentActivity?.trades?.map((trade) => (
                    <TableRow key={trade._id}>
                      <TableCell>{trade.tradeId}</TableCell>
                      <TableCell>
                        {trade.cryptocurrency?.amount} {trade.cryptocurrency?.symbol}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trade.status}
                          size="small"
                          color={
                            trade.status === 'completed' ? 'success' :
                            trade.status === 'disputed' ? 'error' :
                            'default'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(trade.createdAt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  )) || (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography color="textSecondary">No recent trades</Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Disputes
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Dispute ID</TableCell>
                    <TableCell>Trade ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {analytics?.recentActivity?.disputes?.map((dispute) => (
                    <TableRow key={dispute._id}>
                      <TableCell>{dispute.disputeId}</TableCell>
                      <TableCell>{dispute.trade?.tradeId}</TableCell>
                      <TableCell>
                        <Chip
                          label={dispute.status}
                          size="small"
                          color={
                            dispute.status === 'resolved' ? 'success' :
                            dispute.status === 'investigating' ? 'warning' :
                            'error'
                          }
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(dispute.createdAt).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  )) || (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        <Typography color="textSecondary">No recent disputes</Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Additional Analytics */}
      <Grid container spacing={3} mt={2}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Top Cryptocurrencies
            </Typography>
            <Box>
              {['USDT', 'USDC', 'BTC', 'ETH', 'DAI'].map((crypto, index) => (
                <Box key={crypto} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">{crypto}</Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.random() * 100}
                      sx={{ width: 60, height: 6 }}
                    />
                    <Typography variant="body2" color="textSecondary">
                      {Math.floor(Math.random() * 100)}%
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Top Fiat Currencies
            </Typography>
            <Box>
              {['KES', 'USD', 'TZS', 'UGX', 'RWF'].map((currency, index) => (
                <Box key={currency} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">{currency}</Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.random() * 100}
                      sx={{ width: 60, height: 6 }}
                    />
                    <Typography variant="body2" color="textSecondary">
                      {Math.floor(Math.random() * 100)}%
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              System Health
            </Typography>
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2">API Response Time</Typography>
                <Chip label="Good" color="success" size="small" />
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2">Database Performance</Typography>
                <Chip label="Excellent" color="success" size="small" />
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2">WebSocket Connections</Typography>
                <Chip label="Stable" color="success" size="small" />
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="body2">Error Rate</Typography>
                <Chip label="Low" color="success" size="small" />
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsPage;
