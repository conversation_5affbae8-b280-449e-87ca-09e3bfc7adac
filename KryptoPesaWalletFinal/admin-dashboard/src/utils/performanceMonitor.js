/**
 * Performance Monitoring Service for Admin Dashboard
 * Tracks performance metrics, user interactions, and system health
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoads: [],
      apiCalls: [],
      userInteractions: [],
      errors: [],
      memoryUsage: [],
      renderTimes: []
    };
    
    this.thresholds = {
      pageLoadTime: 3000, // 3 seconds
      apiResponseTime: 1000, // 1 second
      memoryUsage: 100 * 1024 * 1024, // 100MB
      renderTime: 16 // 16ms for 60fps
    };

    this.isMonitoring = false;
    this.reportingInterval = null;
  }

  // Initialize performance monitoring
  initialize() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.setupPerformanceObservers();
    this.startMemoryMonitoring();
    this.startReporting();
    
    console.log('Performance monitoring initialized');
  }

  // Setup performance observers
  setupPerformanceObservers() {
    // Navigation timing
    if ('performance' in window && 'getEntriesByType' in performance) {
      // Page load performance
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          if (navigation) {
            this.recordPageLoad(navigation);
          }
        }, 0);
      });

      // Resource loading performance
      const resourceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.entryType === 'resource') {
            this.recordResourceLoad(entry);
          }
        });
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          this.recordLCP(entry);
        });
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          this.recordFID(entry);
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
    }
  }

  // Record page load metrics
  recordPageLoad(navigation) {
    const metrics = {
      timestamp: Date.now(),
      loadTime: navigation.loadEventEnd - navigation.navigationStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      url: window.location.href
    };

    this.metrics.pageLoads.push(metrics);
    
    // Alert if load time exceeds threshold
    if (metrics.loadTime > this.thresholds.pageLoadTime) {
      this.reportSlowPageLoad(metrics);
    }

    this.trimMetrics('pageLoads');
  }

  // Record resource loading
  recordResourceLoad(entry) {
    if (entry.duration > 1000) { // Only track slow resources
      const metrics = {
        timestamp: Date.now(),
        name: entry.name,
        duration: entry.duration,
        size: entry.transferSize,
        type: this.getResourceType(entry.name)
      };

      this.metrics.apiCalls.push(metrics);
      this.trimMetrics('apiCalls');
    }
  }

  // Record API call performance
  recordApiCall(url, method, duration, status, size = 0) {
    const metrics = {
      timestamp: Date.now(),
      url,
      method,
      duration,
      status,
      size,
      isSuccess: status >= 200 && status < 300
    };

    this.metrics.apiCalls.push(metrics);
    
    // Alert if API call is slow
    if (duration > this.thresholds.apiResponseTime) {
      this.reportSlowApiCall(metrics);
    }

    this.trimMetrics('apiCalls');
  }

  // Record user interaction
  recordUserInteraction(type, target, duration = 0) {
    const metrics = {
      timestamp: Date.now(),
      type,
      target,
      duration,
      url: window.location.href
    };

    this.metrics.userInteractions.push(metrics);
    this.trimMetrics('userInteractions');
  }

  // Record error
  recordError(error, context = {}) {
    const metrics = {
      timestamp: Date.now(),
      message: error.message,
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      context
    };

    this.metrics.errors.push(metrics);
    this.trimMetrics('errors');
    
    // Report critical errors immediately
    if (this.isCriticalError(error)) {
      this.reportCriticalError(metrics);
    }
  }

  // Record render time
  recordRenderTime(componentName, duration) {
    const metrics = {
      timestamp: Date.now(),
      component: componentName,
      duration
    };

    this.metrics.renderTimes.push(metrics);
    
    // Alert if render time is slow
    if (duration > this.thresholds.renderTime) {
      this.reportSlowRender(metrics);
    }

    this.trimMetrics('renderTimes');
  }

  // Start memory monitoring
  startMemoryMonitoring() {
    if (!('memory' in performance)) return;

    const monitorMemory = () => {
      const memory = performance.memory;
      const metrics = {
        timestamp: Date.now(),
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      };

      this.metrics.memoryUsage.push(metrics);
      
      // Alert if memory usage is high
      if (metrics.used > this.thresholds.memoryUsage) {
        this.reportHighMemoryUsage(metrics);
      }

      this.trimMetrics('memoryUsage');
    };

    // Monitor every 30 seconds
    setInterval(monitorMemory, 30000);
    monitorMemory(); // Initial measurement
  }

  // Start periodic reporting
  startReporting() {
    // Report metrics every 5 minutes
    this.reportingInterval = setInterval(() => {
      this.generateReport();
    }, 5 * 60 * 1000);
  }

  // Generate performance report
  generateReport() {
    const report = {
      timestamp: Date.now(),
      summary: this.generateSummary(),
      metrics: {
        pageLoads: this.metrics.pageLoads.slice(-10), // Last 10 page loads
        apiCalls: this.getSlowApiCalls(),
        errors: this.metrics.errors.slice(-5), // Last 5 errors
        memoryUsage: this.metrics.memoryUsage.slice(-5) // Last 5 memory readings
      },
      health: this.calculateHealthScore()
    };

    // Send report to monitoring service
    this.sendReport(report);
    
    return report;
  }

  // Generate performance summary
  generateSummary() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    // Filter metrics from last hour
    const recentPageLoads = this.metrics.pageLoads.filter(m => now - m.timestamp < oneHour);
    const recentApiCalls = this.metrics.apiCalls.filter(m => now - m.timestamp < oneHour);
    const recentErrors = this.metrics.errors.filter(m => now - m.timestamp < oneHour);

    return {
      pageLoads: {
        count: recentPageLoads.length,
        averageTime: this.calculateAverage(recentPageLoads, 'loadTime'),
        slowLoads: recentPageLoads.filter(m => m.loadTime > this.thresholds.pageLoadTime).length
      },
      apiCalls: {
        count: recentApiCalls.length,
        averageTime: this.calculateAverage(recentApiCalls, 'duration'),
        slowCalls: recentApiCalls.filter(m => m.duration > this.thresholds.apiResponseTime).length,
        errorRate: recentApiCalls.filter(m => !m.isSuccess).length / recentApiCalls.length * 100
      },
      errors: {
        count: recentErrors.length,
        criticalErrors: recentErrors.filter(e => this.isCriticalError(e)).length
      },
      memory: {
        current: this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]?.used || 0,
        peak: Math.max(...this.metrics.memoryUsage.map(m => m.used))
      }
    };
  }

  // Calculate health score (0-100)
  calculateHealthScore() {
    const summary = this.generateSummary();
    let score = 100;

    // Deduct points for performance issues
    if (summary.pageLoads.averageTime > this.thresholds.pageLoadTime) {
      score -= 20;
    }
    
    if (summary.apiCalls.averageTime > this.thresholds.apiResponseTime) {
      score -= 15;
    }
    
    if (summary.apiCalls.errorRate > 5) {
      score -= 25;
    }
    
    if (summary.errors.count > 0) {
      score -= summary.errors.count * 5;
    }
    
    if (summary.memory.current > this.thresholds.memoryUsage) {
      score -= 10;
    }

    return Math.max(0, score);
  }

  // Helper methods
  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcp ? fcp.startTime : 0;
  }

  recordLCP(entry) {
    // Record Largest Contentful Paint
    console.log('LCP:', entry.startTime);
  }

  recordFID(entry) {
    // Record First Input Delay
    console.log('FID:', entry.processingStart - entry.startTime);
  }

  getResourceType(url) {
    if (url.includes('/api/')) return 'api';
    if (url.match(/\.(js|jsx)$/)) return 'script';
    if (url.match(/\.(css)$/)) return 'stylesheet';
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
    return 'other';
  }

  getSlowApiCalls() {
    return this.metrics.apiCalls.filter(call => 
      call.duration > this.thresholds.apiResponseTime
    ).slice(-10);
  }

  calculateAverage(array, property) {
    if (array.length === 0) return 0;
    const sum = array.reduce((acc, item) => acc + (item[property] || 0), 0);
    return sum / array.length;
  }

  isCriticalError(error) {
    const criticalPatterns = [
      /network error/i,
      /unauthorized/i,
      /forbidden/i,
      /internal server error/i,
      /cannot read property/i,
      /undefined is not a function/i
    ];
    
    return criticalPatterns.some(pattern => 
      pattern.test(error.message || error.toString())
    );
  }

  trimMetrics(type, maxLength = 100) {
    if (this.metrics[type].length > maxLength) {
      this.metrics[type] = this.metrics[type].slice(-maxLength);
    }
  }

  // Reporting methods
  reportSlowPageLoad(metrics) {
    console.warn('Slow page load detected:', metrics);
  }

  reportSlowApiCall(metrics) {
    console.warn('Slow API call detected:', metrics);
  }

  reportSlowRender(metrics) {
    console.warn('Slow render detected:', metrics);
  }

  reportHighMemoryUsage(metrics) {
    console.warn('High memory usage detected:', metrics);
  }

  reportCriticalError(metrics) {
    console.error('Critical error detected:', metrics);
  }

  sendReport(report) {
    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send to monitoring service (e.g., DataDog, New Relic)
      console.log('Performance report:', report);
    }
  }

  // Public API
  getMetrics() {
    return { ...this.metrics };
  }

  getCurrentHealth() {
    return this.calculateHealthScore();
  }

  stop() {
    this.isMonitoring = false;
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
    }
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export const usePerformanceMonitor = () => {
  const recordRender = (componentName, startTime) => {
    const duration = performance.now() - startTime;
    performanceMonitor.recordRenderTime(componentName, duration);
  };

  const recordInteraction = (type, target) => {
    performanceMonitor.recordUserInteraction(type, target);
  };

  return {
    recordRender,
    recordInteraction,
    getHealth: () => performanceMonitor.getCurrentHealth(),
    getMetrics: () => performanceMonitor.getMetrics()
  };
};

export default performanceMonitor;
