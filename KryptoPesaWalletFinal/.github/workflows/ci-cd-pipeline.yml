name: KryptoPesa CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  MONGODB_VERSION: '6.0'
  REDIS_VERSION: '7.0'

jobs:
  # Security and Code Quality Checks
  security-scan:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: |
            backend/package-lock.json
            mobile/package-lock.json
            admin-dashboard/package-lock.json

      - name: Install Backend Dependencies
        run: |
          cd backend
          npm ci

      - name: Security Audit - Backend
        run: |
          cd backend
          npm audit --audit-level=high

      - name: Lint Backend Code
        run: |
          cd backend
          npm run lint || true

      - name: Install Mobile Dependencies
        run: |
          cd mobile
          npm ci --legacy-peer-deps

      - name: Security Audit - Mobile
        run: |
          cd mobile
          npm audit --audit-level=high

      - name: Install Admin Dashboard Dependencies
        run: |
          cd admin-dashboard
          npm ci

      - name: Security Audit - Admin Dashboard
        run: |
          cd admin-dashboard
          npm audit --audit-level=high

      - name: CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Backend Testing
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: security-scan
    
    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install Dependencies
        run: |
          cd backend
          npm ci

      - name: Run Unit Tests
        run: |
          cd backend
          npm run test:unit
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Run Integration Tests
        run: |
          cd backend
          npm run test:integration
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Run E2E Tests
        run: |
          cd backend
          node scripts/run-e2e-tests.js
        env:
          NODE_ENV: test
          MONGODB_TEST_URI: mongodb://localhost:27017/kryptopesa_test
          REDIS_TEST_URL: redis://localhost:6379/1

      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: backend/coverage
          flags: backend

  # Frontend Testing
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: security-scan
    
    strategy:
      matrix:
        app: [mobile, admin-dashboard]

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ matrix.app }}/package-lock.json

      - name: Install Dependencies
        run: |
          cd ${{ matrix.app }}
          npm ci ${{ matrix.app == 'mobile' && '--legacy-peer-deps' || '' }}

      - name: Run Tests
        run: |
          cd ${{ matrix.app }}
          npm test -- --coverage --watchAll=false
        env:
          CI: true

      - name: Build Application
        run: |
          cd ${{ matrix.app }}
          npm run build
        env:
          CI: true

      - name: Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: ${{ matrix.app }}/coverage
          flags: ${{ matrix.app }}

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [backend-tests]
    
    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
      redis:
        image: redis:7.0
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Dependencies
        run: |
          cd backend
          npm ci

      - name: Start Backend Server
        run: |
          cd backend
          npm start &
          sleep 10
        env:
          NODE_ENV: production
          MONGODB_URI: mongodb://localhost:27017/kryptopesa_perf
          REDIS_URL: redis://localhost:6379

      - name: Install Artillery
        run: npm install -g artillery

      - name: Run Load Tests
        run: |
          cd backend/tests/performance
          artillery run load-test.yml

      - name: Upload Performance Results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: backend/tests/performance/results/

  # Build and Deploy
  build-and-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, performance-tests]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      # Backend Build
      - name: Build Backend
        run: |
          cd backend
          npm ci
          npm run build

      # Mobile App Build
      - name: Setup Expo CLI
        run: npm install -g @expo/cli

      - name: Build Mobile App
        run: |
          cd mobile
          npm ci --legacy-peer-deps
          expo export --platform android

      # Admin Dashboard Build
      - name: Build Admin Dashboard
        run: |
          cd admin-dashboard
          npm ci
          npm run build

      # Docker Build
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CONTAINER_REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build and Push Backend Image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:latest
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-backend:${{ github.sha }}

      - name: Build and Push Admin Dashboard Image
        uses: docker/build-push-action@v5
        with:
          context: ./admin-dashboard
          push: true
          tags: |
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-admin:latest
            ${{ secrets.CONTAINER_REGISTRY }}/kryptopesa-admin:${{ github.sha }}

      # Deploy to Staging
      - name: Deploy to Staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "Deploying to staging environment..."
          # Add staging deployment commands here

      # Deploy to Production
      - name: Deploy to Production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Deploying to production environment..."
          # Add production deployment commands here

      # Health Check
      - name: Production Health Check
        if: github.ref == 'refs/heads/main'
        run: |
          sleep 30
          curl -f ${{ secrets.PRODUCTION_URL }}/health || exit 1

  # Notification
  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: always()
    
    steps:
      - name: Notify Success
        if: needs.build-and-deploy.result == 'success'
        run: |
          echo "✅ Deployment successful!"
          # Add Slack/Discord notification here

      - name: Notify Failure
        if: needs.build-and-deploy.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          # Add failure notification here
