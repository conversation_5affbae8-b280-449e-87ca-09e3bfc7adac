# KryptoPesa Backend Production Environment Configuration
# Critical: This file contains production settings for enterprise deployment

# Environment
NODE_ENV=production

# Server Configuration
PORT=3000
HOST=0.0.0.0

# Database Configuration (Production)
MONGODB_URI=mongodb://mongodb:27017/kryptopesa_production
MONGODB_OPTIONS=retryWrites=true&w=majority&readPreference=primary

# Redis Configuration (Production)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-for-production-min-32-chars
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Encryption Keys
ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_ROUNDS=12

# API Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# CORS Configuration
CORS_ORIGIN=https://app.kryptopesa.com,https://admin.kryptopesa.com
CORS_CREDENTIALS=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Blockchain Configuration
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-project-id
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id
PRIVATE_KEY=your-wallet-private-key-for-smart-contracts

# Smart Contract Addresses
ESCROW_CONTRACT_ADDRESS=******************************************
USDT_CONTRACT_ADDRESS=******************************************

# External Services
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Push Notifications
FCM_SERVER_KEY=your-firebase-server-key
FCM_PROJECT_ID=kryptopesa-production

# Monitoring & Logging
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_TRACKING=true

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_TTL=3600

# Security Headers
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Database Connection Pool
DB_MIN_POOL_SIZE=5
DB_MAX_POOL_SIZE=20
DB_CONNECTION_TIMEOUT=30000

# Session Configuration
SESSION_SECRET=your-session-secret-key-for-production
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTP_ONLY=true
SESSION_COOKIE_SAME_SITE=strict

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
PROMETHEUS_ENABLED=true
