#!/usr/bin/env node

/**
 * KryptoPesa Database Seeder
 * Creates initial admin user and sample data for development/testing
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const logger = require('../utils/logger');

// Load environment variables
require('dotenv').config({ path: '../../../.env' });

// Admin user configuration
const ADMIN_CONFIG = {
  username: 'admin',
  email: '<EMAIL>',
  phone: '+254700000000',
  password: 'KryptoPesa2024!',
  profile: {
    firstName: 'System',
    lastName: 'Administrator',
    location: {
      country: 'KE',
      city: 'Nairobi'
    }
  },
  role: 'admin',
  status: 'active',
  verification: {
    email: { verified: true },
    phone: { verified: true },
    identity: { verified: true }
  }
};

// Test users configuration
const TEST_USERS = [
  {
    username: 'testbuyer',
    email: '<EMAIL>',
    phone: '+254700000001',
    password: 'TestBuyer123!',
    profile: {
      firstName: 'Test',
      lastName: 'Buyer',
      location: { country: 'KE', city: 'Nairobi' }
    },
    role: 'user',
    status: 'active',
    verification: {
      email: { verified: true },
      phone: { verified: true }
    }
  },
  {
    username: 'testseller',
    email: '<EMAIL>',
    phone: '+254700000002',
    password: 'TestSeller123!',
    profile: {
      firstName: 'Test',
      lastName: 'Seller',
      location: { country: 'UG', city: 'Kampala' }
    },
    role: 'user',
    status: 'active',
    verification: {
      email: { verified: true },
      phone: { verified: true }
    }
  },
  {
    username: 'moderator',
    email: '<EMAIL>',
    phone: '+254700000003',
    password: 'Moderator123!',
    profile: {
      firstName: 'Test',
      lastName: 'Moderator',
      location: { country: 'TZ', city: 'Dar es Salaam' }
    },
    role: 'moderator',
    status: 'active',
    verification: {
      email: { verified: true },
      phone: { verified: true }
    }
  }
];

/**
 * Connect to database
 */
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa';
    await mongoose.connect(mongoURI);
    logger.info('Connected to MongoDB for seeding');
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
}

/**
 * Create admin user
 */
async function createAdminUser() {
  try {
    // Check if admin already exists
    const existingAdmin = await User.findOne({ 
      $or: [
        { email: ADMIN_CONFIG.email },
        { username: ADMIN_CONFIG.username }
      ]
    });

    if (existingAdmin) {
      logger.info('Admin user already exists');
      return existingAdmin;
    }

    // Create admin user
    const adminUser = new User(ADMIN_CONFIG);
    await adminUser.save();

    logger.info('✅ Admin user created successfully');
    logger.info(`📧 Email: ${ADMIN_CONFIG.email}`);
    logger.info(`🔑 Password: ${ADMIN_CONFIG.password}`);
    logger.info(`👤 Username: ${ADMIN_CONFIG.username}`);
    
    return adminUser;
  } catch (error) {
    logger.error('Failed to create admin user:', error);
    throw error;
  }
}

/**
 * Create test users
 */
async function createTestUsers() {
  try {
    logger.info('Creating test users...');
    
    for (const userData of TEST_USERS) {
      const existingUser = await User.findOne({
        $or: [
          { email: userData.email },
          { username: userData.username }
        ]
      });

      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        logger.info(`✅ Created test user: ${userData.username}`);
      } else {
        logger.info(`⚠️ Test user already exists: ${userData.username}`);
      }
    }
  } catch (error) {
    logger.error('Failed to create test users:', error);
    throw error;
  }
}

/**
 * Display seeding results
 */
function displayResults() {
  console.log('\n🎉 DATABASE SEEDING COMPLETED!');
  console.log('=====================================');
  console.log('\n📱 ADMIN DASHBOARD ACCESS:');
  console.log('URL: http://localhost:3001');
  console.log(`Email: ${ADMIN_CONFIG.email}`);
  console.log(`Password: ${ADMIN_CONFIG.password}`);
  console.log('\n👥 TEST USERS CREATED:');
  TEST_USERS.forEach(user => {
    console.log(`• ${user.username} (${user.email}) - Role: ${user.role}`);
  });
  console.log('\n🔗 SYSTEM ENDPOINTS:');
  console.log('• Backend API: http://localhost:3000');
  console.log('• Admin Dashboard: http://localhost:3001');
  console.log('• Health Check: http://localhost:3000/health');
  console.log('\n🧪 READY FOR TESTING!');
}

/**
 * Main seeding function
 */
async function seedDatabase() {
  try {
    logger.info('🌱 Starting database seeding...');
    
    await connectDB();
    await createAdminUser();
    await createTestUsers();
    
    displayResults();
    
  } catch (error) {
    logger.error('Seeding failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, ADMIN_CONFIG, TEST_USERS };
