const winston = require('winston');
const path = require('path');

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'info'; // Changed from 'warn' to 'info' for production
};

// Define format for logs
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define transports based on environment
const transports = [];

// Always add console transport
transports.push(
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  })
);

// Production file transports
if (process.env.NODE_ENV === 'production') {
  // Error logs
  transports.push(
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );

  // Application logs
  transports.push(
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/app.log'),
      level: 'info',
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );

  // Security logs
  transports.push(
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/security.log'),
      level: 'warn',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );
} else {
  // Development file transport
  transports.push(
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/combined.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );
}

// Create logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  exitOnError: false,
});

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Enhanced logging methods for specific use cases
logger.security = (message, metadata = {}) => {
  logger.warn(message, {
    type: 'SECURITY',
    timestamp: new Date().toISOString(),
    ...metadata
  });
};

logger.audit = (action, userId, resource, metadata = {}) => {
  logger.info(`AUDIT: ${action}`, {
    type: 'AUDIT',
    userId,
    resource,
    timestamp: new Date().toISOString(),
    ...metadata
  });
};

logger.performance = (operation, duration, metadata = {}) => {
  logger.info(`PERFORMANCE: ${operation} took ${duration}ms`, {
    type: 'PERFORMANCE',
    operation,
    duration,
    timestamp: new Date().toISOString(),
    ...metadata
  });
};

logger.trade = (tradeId, action, userId, metadata = {}) => {
  logger.info(`TRADE: ${action} for trade ${tradeId}`, {
    type: 'TRADE',
    tradeId,
    action,
    userId,
    timestamp: new Date().toISOString(),
    ...metadata
  });
};

logger.blockchain = (network, action, transactionHash, metadata = {}) => {
  logger.info(`BLOCKCHAIN: ${action} on ${network}`, {
    type: 'BLOCKCHAIN',
    network,
    action,
    transactionHash,
    timestamp: new Date().toISOString(),
    ...metadata
  });
};

// Log startup information
logger.info('Logger initialized', {
  environment: process.env.NODE_ENV,
  logLevel: level(),
  transports: transports.length
});

module.exports = logger;
