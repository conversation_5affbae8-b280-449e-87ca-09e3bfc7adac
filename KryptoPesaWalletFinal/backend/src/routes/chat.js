const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { verifyToken } = require('./auth');
const Chat = require('../models/Chat');
const Trade = require('../models/Trade');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// All chat routes require authentication
router.use(verifyToken);

// Get chat messages for a trade
router.get('/:tradeId/messages', [
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get chat
    const chat = await Chat.findOne({ trade: trade._id })
      .populate('participants.user', 'username profile')
      .populate('messages.sender', 'username profile');

    if (!chat) {
      throw new AppError('Chat not found', 404);
    }

    // Get paginated messages
    const messages = chat.messages
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice(offset, offset + limit)
      .reverse(); // Reverse to show oldest first

    const total = chat.messages.length;

    res.json({
      success: true,
      data: {
        chat: {
          _id: chat._id,
          trade: chat.trade,
          participants: chat.participants,
          status: chat.status
        },
        messages,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Send message in trade chat
router.post('/:tradeId/messages', [
  body('content').notEmpty().withMessage('Message content is required'),
  body('type').optional().isIn(['text', 'image', 'file']),
  body('attachments').optional().isArray()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { tradeId } = req.params;
    const { content, type = 'text', attachments = [] } = req.body;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get or create chat
    let chat = await Chat.findOne({ trade: trade._id });
    if (!chat) {
      chat = await Chat.createForTrade(trade._id, trade.seller, trade.buyer);
    }

    // Check if chat is locked
    if (chat.status === 'locked') {
      throw new AppError('Chat is locked', 403);
    }

    // Add message
    await chat.addMessage(req.user._id, content, type, attachments);

    // Get the newly added message with populated sender
    await chat.populate('messages.sender', 'username profile');
    const newMessage = chat.messages[chat.messages.length - 1];

    // Emit real-time message via socket
    const io = req.app.get('io');
    if (io) {
      io.to(`trade:${tradeId}`).emit('new_message', {
        tradeId,
        message: newMessage,
        sender: {
          id: req.user._id,
          username: req.user.username
        }
      });
    }

    logger.info(`Message sent in trade ${tradeId} by user ${req.user.username}`);

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: { message: newMessage }
    });

  } catch (error) {
    next(error);
  }
});

// Mark messages as read
router.post('/:tradeId/read', [
  body('messageIds').optional().isArray()
], async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const { messageIds = [] } = req.body;

    // Find trade and verify user is participant
    const trade = await Trade.findOne({ tradeId });
    if (!trade) {
      throw new AppError('Trade not found', 404);
    }

    if (!trade.isParticipant(req.user._id)) {
      throw new AppError('Access denied', 403);
    }

    // Get chat
    const chat = await Chat.findOne({ trade: trade._id });
    if (!chat) {
      throw new AppError('Chat not found', 404);
    }

    // Mark messages as read
    await chat.markAsRead(req.user._id, messageIds);

    res.json({
      success: true,
      message: 'Messages marked as read'
    });

  } catch (error) {
    next(error);
  }
});

// Get unread message count for user's trades
router.get('/unread-count', async (req, res, next) => {
  try {
    // Get user's active trades
    const trades = await Trade.find({
      $or: [{ seller: req.user._id }, { buyer: req.user._id }],
      status: { $in: ['created', 'funded', 'payment_sent'] }
    });

    let totalUnread = 0;
    const unreadByTrade = {};

    for (const trade of trades) {
      const chat = await Chat.findOne({ trade: trade._id });
      if (chat) {
        const unreadMessages = chat.getUnreadMessages(req.user._id);
        const unreadCount = unreadMessages.length;
        totalUnread += unreadCount;

        if (unreadCount > 0) {
          unreadByTrade[trade.tradeId] = unreadCount;
        }
      }
    }

    res.json({
      success: true,
      data: {
        totalUnread,
        unreadByTrade
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;