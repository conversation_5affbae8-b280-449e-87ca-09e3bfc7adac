const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { verifyToken } = require('./auth');
const Offer = require('../models/Offer');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Payment method validation schemas
const paymentMethodSchemas = {
  mobile_money: {
    required: ['provider', 'mobileNumber'],
    optional: ['accountName'],
    providers: ['M-Pesa', 'Airtel Money', 'Tigo Pesa', 'MTN Mobile Money']
  },
  bank_transfer: {
    required: ['bankName', 'accountNumber', 'accountName'],
    optional: ['branchCode', 'swiftCode']
  },
  cash: {
    required: ['meetingLocation'],
    optional: ['instructions']
  },
  other: {
    required: ['instructions'],
    optional: []
  }
};

// Validate payment method details
const validatePaymentMethod = (method, details) => {
  const schema = paymentMethodSchemas[method];
  if (!schema) {
    throw new AppError(`Invalid payment method: ${method}`, 400);
  }

  // Check required fields
  for (const field of schema.required) {
    if (!details[field] || details[field].trim() === '') {
      throw new AppError(`${field} is required for ${method}`, 400);
    }
  }

  // Validate specific fields
  if (method === 'mobile_money') {
    if (!schema.providers.includes(details.provider)) {
      throw new AppError(`Invalid mobile money provider: ${details.provider}`, 400);
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(details.mobileNumber.replace(/\s/g, ''))) {
      throw new AppError('Invalid mobile number format', 400);
    }
  }

  if (method === 'bank_transfer') {
    // Validate account number (basic validation)
    if (details.accountNumber.length < 8 || details.accountNumber.length > 20) {
      throw new AppError('Account number must be between 8 and 20 characters', 400);
    }
  }

  return true;
};

const router = express.Router();

// Get all offers (public endpoint with optional auth)
router.get('/', [
  query('type').optional().isIn(['buy', 'sell']),
  query('cryptocurrency').optional().isIn(['USDT', 'USDC', 'DAI', 'BTC', 'ETH']),
  query('fiatCurrency').optional().isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']),
  query('country').optional().isIn(['KE', 'TZ', 'UG', 'RW']),
  query('paymentMethod').optional().isIn(['bank_transfer', 'mobile_money', 'cash', 'other']),
  query('minAmount').optional().isNumeric(),
  query('maxAmount').optional().isNumeric(),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 }),
  query('sortBy').optional().isIn(['price', 'reputation', 'created', 'volume']),
  query('sortOrder').optional().isIn(['asc', 'desc'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      type,
      cryptocurrency,
      fiatCurrency,
      country,
      paymentMethod,
      minAmount,
      maxAmount,
      limit = 20,
      offset = 0,
      sortBy = 'created',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {
      status: 'active',
      expiresAt: { $gt: new Date() }
    };

    if (type) query.type = type;
    if (cryptocurrency) query['cryptocurrency.symbol'] = cryptocurrency;
    if (fiatCurrency) query['fiat.currency'] = fiatCurrency;
    if (country) query['location.country'] = country;
    if (paymentMethod) query['paymentMethods.method'] = paymentMethod;

    // Amount filtering
    if (minAmount) {
      query['cryptocurrency.maxAmount'] = { $gte: minAmount.toString() };
    }
    if (maxAmount) {
      query['cryptocurrency.minAmount'] = { $lte: maxAmount.toString() };
    }

    // Build sort
    let sort = {};
    switch (sortBy) {
      case 'price':
        sort['fiat.effectivePrice'] = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'reputation':
        sort['creator.reputation.score'] = sortOrder === 'asc' ? 1 : -1;
        break;
      case 'volume':
        sort['statistics.totalVolume'] = sortOrder === 'asc' ? 1 : -1;
        break;
      default:
        sort.createdAt = sortOrder === 'asc' ? 1 : -1;
    }

    const offers = await Offer.find(query)
      .populate('creator', 'username profile reputation verification')
      .sort(sort)
      .limit(parseInt(limit))
      .skip(parseInt(offset));

    const total = await Offer.countDocuments(query);

    // Increment view count for each offer
    const offerIds = offers.map(offer => offer._id);
    await Offer.updateMany(
      { _id: { $in: offerIds } },
      { $inc: { 'statistics.views': 1 } }
    );

    res.json({
      success: true,
      data: {
        offers,
        pagination: {
          total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < total
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get user's own offers (requires auth)
router.get('/my', verifyToken, async (req, res, next) => {
  try {
    const offers = await Offer.find({ creator: req.user._id })
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: { offers }
    });

  } catch (error) {
    next(error);
  }
});

// Create new offer (requires auth)
router.post('/', verifyToken, [
  body('type').isIn(['buy', 'sell']).withMessage('Type must be buy or sell'),
  body('cryptocurrency.symbol').isIn(['USDT', 'USDC', 'DAI', 'BTC', 'ETH']).withMessage('Invalid cryptocurrency'),
  body('cryptocurrency.minAmount').isNumeric().withMessage('Min amount must be numeric'),
  body('cryptocurrency.maxAmount').isNumeric().withMessage('Max amount must be numeric'),
  body('cryptocurrency.availableAmount').isNumeric().withMessage('Available amount must be numeric'),
  body('fiat.currency').isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']).withMessage('Invalid fiat currency'),
  body('fiat.priceType').isIn(['fixed', 'market', 'margin']).withMessage('Invalid price type'),
  body('paymentMethods').isArray({ min: 1 }).withMessage('At least one payment method required'),
  body('paymentMethods.*.method').isIn(['bank_transfer', 'mobile_money', 'cash', 'other']).withMessage('Invalid payment method')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const offerData = req.body;

    // Validate payment methods
    for (const paymentMethod of offerData.paymentMethods) {
      try {
        validatePaymentMethod(paymentMethod.method, paymentMethod.details || {});
      } catch (error) {
        throw new AppError(`Payment method validation failed: ${error.message}`, 400);
      }
    }

    // Validate amounts
    const minAmount = parseFloat(offerData.cryptocurrency.minAmount);
    const maxAmount = parseFloat(offerData.cryptocurrency.maxAmount);
    const availableAmount = parseFloat(offerData.cryptocurrency.availableAmount);

    if (minAmount >= maxAmount) {
      throw new AppError('Min amount must be less than max amount', 400);
    }

    if (availableAmount < maxAmount) {
      throw new AppError('Available amount must be at least equal to max amount', 400);
    }

    // Set contract address based on cryptocurrency
    if (['USDT', 'USDC', 'DAI', 'ETH'].includes(offerData.cryptocurrency.symbol)) {
      const contractAddresses = {
        'USDT': process.env.USDT_CONTRACT_ADDRESS,
        'USDC': process.env.USDC_CONTRACT_ADDRESS,
        'DAI': process.env.DAI_CONTRACT_ADDRESS,
        'ETH': null // Native token
      };
      offerData.cryptocurrency.contractAddress = contractAddresses[offerData.cryptocurrency.symbol];
      offerData.cryptocurrency.network = 'polygon';
    } else if (offerData.cryptocurrency.symbol === 'BTC') {
      offerData.cryptocurrency.network = 'bitcoin';
    }

    // Set location from user profile
    offerData.location = {
      country: req.user.profile.location.country,
      city: req.user.profile.location.city
    };

    // Set creator
    offerData.creator = req.user._id;

    // Create offer
    const offer = new Offer(offerData);
    await offer.save();

    // Populate creator info for response
    await offer.populate('creator', 'username profile reputation verification');

    logger.info(`Offer created: ${offer.offerId} by user ${req.user.username}`);

    res.status(201).json({
      success: true,
      message: 'Offer created successfully',
      data: { offer }
    });

  } catch (error) {
    next(error);
  }
});

// Get specific offer
router.get('/:offerId', async (req, res, next) => {
  try {
    const { offerId } = req.params;

    const offer = await Offer.findOne({ offerId })
      .populate('creator', 'username profile reputation verification');

    if (!offer) {
      throw new AppError('Offer not found', 404);
    }

    // Increment view count
    await offer.incrementStats('view');

    res.json({
      success: true,
      data: { offer }
    });

  } catch (error) {
    next(error);
  }
});

// Update offer (requires auth and ownership)
router.put('/:offerId', verifyToken, [
  body('cryptocurrency.minAmount').optional().isNumeric(),
  body('cryptocurrency.maxAmount').optional().isNumeric(),
  body('cryptocurrency.availableAmount').optional().isNumeric(),
  body('fiat.priceType').optional().isIn(['fixed', 'market', 'margin']),
  body('fiat.fixedPrice').optional().isNumeric(),
  body('fiat.marginPercentage').optional().isNumeric(),
  body('paymentMethods').optional().isArray(),
  body('terms.timeLimit').optional().isInt({ min: 15, max: 120 }),
  body('terms.instructions').optional().isLength({ max: 1000 }),
  body('status').optional().isIn(['active', 'paused', 'inactive'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { offerId } = req.params;
    const updates = req.body;

    const offer = await Offer.findOne({ offerId });

    if (!offer) {
      throw new AppError('Offer not found', 404);
    }

    // Check ownership
    if (offer.creator.toString() !== req.user._id.toString()) {
      throw new AppError('You can only update your own offers', 403);
    }

    // Validate amount updates
    if (updates.cryptocurrency) {
      const currentMin = parseFloat(offer.cryptocurrency.minAmount);
      const currentMax = parseFloat(offer.cryptocurrency.maxAmount);
      const currentAvailable = parseFloat(offer.cryptocurrency.availableAmount);

      const newMin = updates.cryptocurrency.minAmount ? parseFloat(updates.cryptocurrency.minAmount) : currentMin;
      const newMax = updates.cryptocurrency.maxAmount ? parseFloat(updates.cryptocurrency.maxAmount) : currentMax;
      const newAvailable = updates.cryptocurrency.availableAmount ? parseFloat(updates.cryptocurrency.availableAmount) : currentAvailable;

      if (newMin >= newMax) {
        throw new AppError('Min amount must be less than max amount', 400);
      }

      if (newAvailable < newMax) {
        throw new AppError('Available amount must be at least equal to max amount', 400);
      }
    }

    // Apply updates
    Object.keys(updates).forEach(key => {
      if (key.includes('.')) {
        // Handle nested updates
        const [parent, child] = key.split('.');
        if (!offer[parent]) offer[parent] = {};
        offer[parent][child] = updates[key];
      } else {
        offer[key] = updates[key];
      }
    });

    offer.lastPriceUpdate = new Date();
    await offer.save();

    logger.info(`Offer updated: ${offer.offerId} by user ${req.user.username}`);

    res.json({
      success: true,
      message: 'Offer updated successfully',
      data: { offer }
    });

  } catch (error) {
    next(error);
  }
});

// Delete offer (requires auth and ownership)
router.delete('/:offerId', verifyToken, async (req, res, next) => {
  try {
    const { offerId } = req.params;

    const offer = await Offer.findOne({ offerId });

    if (!offer) {
      throw new AppError('Offer not found', 404);
    }

    // Check ownership
    if (offer.creator.toString() !== req.user._id.toString()) {
      throw new AppError('You can only delete your own offers', 403);
    }

    // Check if offer has active trades
    const Trade = require('../models/Trade');
    const activeTrades = await Trade.find({
      offer: offer._id,
      status: { $in: ['created', 'funded', 'payment_sent'] }
    });

    if (activeTrades.length > 0) {
      throw new AppError('Cannot delete offer with active trades', 400);
    }

    await Offer.findByIdAndDelete(offer._id);

    logger.info(`Offer deleted: ${offer.offerId} by user ${req.user.username}`);

    res.json({
      success: true,
      message: 'Offer deleted successfully'
    });

  } catch (error) {
    next(error);
  }
});

// Respond to offer (create trade)
router.post('/:offerId/respond', verifyToken, [
  body('amount').isNumeric().withMessage('Amount must be numeric'),
  body('paymentMethod').notEmpty().withMessage('Payment method is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { offerId } = req.params;
    const { amount, paymentMethod } = req.body;

    const offer = await Offer.findOne({ offerId, status: 'active' })
      .populate('creator');

    if (!offer) {
      throw new AppError('Offer not found or inactive', 404);
    }

    // Check if user is trying to respond to their own offer
    if (offer.creator._id.toString() === req.user._id.toString()) {
      throw new AppError('Cannot respond to your own offer', 400);
    }

    // Validate amount
    const amountValidation = offer.isAmountValid(parseFloat(amount));
    if (!amountValidation.valid) {
      throw new AppError(amountValidation.reason, 400);
    }

    // Check if user meets requirements
    const requirementCheck = offer.meetsRequirements(req.user);
    if (!requirementCheck.meets) {
      throw new AppError(requirementCheck.reason, 403);
    }

    // Validate payment method
    const validPaymentMethod = offer.paymentMethods.find(pm => pm.method === paymentMethod);
    if (!validPaymentMethod) {
      throw new AppError('Invalid payment method for this offer', 400);
    }

    // Increment response count
    await offer.incrementStats('response');

    // Redirect to trade creation
    req.body.offerId = offerId;

    // Forward to trade creation route
    const tradeRouter = require('./trade');
    return tradeRouter.handle(req, res, next);

  } catch (error) {
    next(error);
  }
});

// Get matching offers for specific criteria
router.post('/search', [
  body('type').isIn(['buy', 'sell']).withMessage('Type must be buy or sell'),
  body('cryptocurrency').isIn(['USDT', 'USDC', 'DAI', 'BTC', 'ETH']).withMessage('Invalid cryptocurrency'),
  body('fiatCurrency').isIn(['KES', 'TZS', 'UGX', 'RWF', 'USD']).withMessage('Invalid fiat currency'),
  body('amount').optional().isNumeric(),
  body('country').isIn(['KE', 'TZ', 'UG', 'RW']).withMessage('Invalid country'),
  body('paymentMethod').optional().isIn(['bank_transfer', 'mobile_money', 'cash', 'other'])
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const searchCriteria = req.body;
    const offers = await Offer.findMatching(searchCriteria);

    res.json({
      success: true,
      data: { offers }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;