const express = require('express');
const { performanceMonitor } = require('../middleware/performanceMonitoring');
const User = require('../models/User');
const Trade = require('../models/Trade');
const Offer = require('../models/Offer');
const Dispute = require('../models/Dispute');

const router = express.Router();

// Middleware to restrict access to metrics (optional authentication)
const authenticateMetrics = (req, res, next) => {
  const metricsToken = process.env.METRICS_TOKEN;
  
  if (metricsToken) {
    const providedToken = req.headers['x-metrics-token'] || req.query.token;
    
    if (providedToken !== metricsToken) {
      return res.status(401).json({
        error: 'Unauthorized access to metrics endpoint'
      });
    }
  }
  
  next();
};

// Apply authentication middleware
router.use(authenticateMetrics);

// Basic metrics endpoint
router.get('/', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    res.json(metrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve metrics',
      message: error.message
    });
  }
});

// Prometheus-style metrics endpoint
router.get('/prometheus', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const prometheusMetrics = formatPrometheusMetrics(metrics);
    
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } catch (error) {
    res.status(500).send(`# Error generating metrics: ${error.message}`);
  }
});

// Application-specific metrics
router.get('/application', async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalTrades,
      activeTrades,
      completedTrades,
      totalOffers,
      activeOffers,
      totalDisputes,
      openDisputes
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ 
        lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
      }),
      Trade.countDocuments(),
      Trade.countDocuments({ 
        status: { $in: ['created', 'funded', 'payment_sent'] } 
      }),
      Trade.countDocuments({ status: 'completed' }),
      Offer.countDocuments(),
      Offer.countDocuments({ status: 'active' }),
      Dispute.countDocuments(),
      Dispute.countDocuments({ 
        status: { $in: ['open', 'investigating'] } 
      })
    ]);

    // Calculate volume metrics
    const volumeMetrics = await Trade.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: null,
          totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },
          totalFiatVolume: { $sum: '$fiat.amount' },
          avgTradeSize: { $avg: { $toDouble: '$cryptocurrency.amount' } }
        }
      }
    ]);

    // Recent activity metrics
    const recentMetrics = await Promise.all([
      Trade.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),
      User.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }),
      Offer.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    ]);

    const applicationMetrics = {
      users: {
        total: totalUsers,
        active24h: activeUsers,
        new24h: recentMetrics[1],
        activityRate: totalUsers > 0 ? (activeUsers / totalUsers * 100).toFixed(2) : 0
      },
      trades: {
        total: totalTrades,
        active: activeTrades,
        completed: completedTrades,
        new24h: recentMetrics[0],
        completionRate: totalTrades > 0 ? (completedTrades / totalTrades * 100).toFixed(2) : 0,
        volume: volumeMetrics[0] || { totalVolume: 0, totalFiatVolume: 0, avgTradeSize: 0 }
      },
      offers: {
        total: totalOffers,
        active: activeOffers,
        new24h: recentMetrics[2],
        activeRate: totalOffers > 0 ? (activeOffers / totalOffers * 100).toFixed(2) : 0
      },
      disputes: {
        total: totalDisputes,
        open: openDisputes,
        disputeRate: totalTrades > 0 ? (totalDisputes / totalTrades * 100).toFixed(2) : 0
      },
      timestamp: new Date().toISOString()
    };

    res.json(applicationMetrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve application metrics',
      message: error.message
    });
  }
});

// Health score endpoint
router.get('/health-score', (req, res) => {
  try {
    const healthScore = performanceMonitor.getHealthScore();
    const metrics = performanceMonitor.getMetrics();
    
    let status = 'healthy';
    if (healthScore < 70) status = 'degraded';
    if (healthScore < 50) status = 'unhealthy';
    if (healthScore < 30) status = 'critical';

    res.json({
      score: healthScore,
      status,
      factors: {
        errorRate: metrics.errors.errorRate,
        avgResponseTime: metrics.responseTime.average,
        p95ResponseTime: metrics.responseTime.p95,
        memoryUsage: metrics.memory.current.heapUsed,
        activeConnections: metrics.activeConnections
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to calculate health score',
      message: error.message
    });
  }
});

// Reset metrics endpoint (for testing/debugging)
router.post('/reset', (req, res) => {
  try {
    performanceMonitor.reset();
    res.json({
      message: 'Metrics reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to reset metrics',
      message: error.message
    });
  }
});

// Real-time metrics endpoint (for dashboards)
router.get('/realtime', (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    const currentMemory = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const realtimeMetrics = {
      timestamp: new Date().toISOString(),
      requests: {
        total: metrics.requests.total,
        rps: metrics.requests.requestsPerSecond.toFixed(2),
        active: metrics.activeConnections
      },
      performance: {
        avgResponseTime: metrics.responseTime.average.toFixed(2),
        p95ResponseTime: metrics.responseTime.p95.toFixed(2),
        errorRate: (metrics.errors.errorRate * 100).toFixed(2)
      },
      system: {
        memory: {
          heapUsed: (currentMemory.heapUsed / 1024 / 1024).toFixed(2),
          heapTotal: (currentMemory.heapTotal / 1024 / 1024).toFixed(2),
          rss: (currentMemory.rss / 1024 / 1024).toFixed(2)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: process.uptime()
      },
      health: {
        score: performanceMonitor.getHealthScore(),
        status: performanceMonitor.getHealthScore() >= 70 ? 'healthy' : 'degraded'
      }
    };

    res.json(realtimeMetrics);
  } catch (error) {
    res.status(500).json({
      error: 'Failed to retrieve real-time metrics',
      message: error.message
    });
  }
});

// Helper function to format metrics for Prometheus
function formatPrometheusMetrics(metrics) {
  const lines = [];
  
  // Request metrics
  lines.push('# HELP http_requests_total Total number of HTTP requests');
  lines.push('# TYPE http_requests_total counter');
  lines.push(`http_requests_total ${metrics.requests.total}`);
  
  lines.push('# HELP http_requests_successful_total Total number of successful HTTP requests');
  lines.push('# TYPE http_requests_successful_total counter');
  lines.push(`http_requests_successful_total ${metrics.requests.successful}`);
  
  lines.push('# HELP http_requests_failed_total Total number of failed HTTP requests');
  lines.push('# TYPE http_requests_failed_total counter');
  lines.push(`http_requests_failed_total ${metrics.requests.failed}`);
  
  // Response time metrics
  lines.push('# HELP http_request_duration_ms HTTP request duration in milliseconds');
  lines.push('# TYPE http_request_duration_ms histogram');
  lines.push(`http_request_duration_ms_avg ${metrics.responseTime.average}`);
  lines.push(`http_request_duration_ms_p95 ${metrics.responseTime.p95}`);
  lines.push(`http_request_duration_ms_p99 ${metrics.responseTime.p99}`);
  
  // Memory metrics
  const currentMemory = process.memoryUsage();
  lines.push('# HELP process_memory_heap_used_bytes Process heap memory used in bytes');
  lines.push('# TYPE process_memory_heap_used_bytes gauge');
  lines.push(`process_memory_heap_used_bytes ${currentMemory.heapUsed}`);
  
  lines.push('# HELP process_memory_rss_bytes Process RSS memory in bytes');
  lines.push('# TYPE process_memory_rss_bytes gauge');
  lines.push(`process_memory_rss_bytes ${currentMemory.rss}`);
  
  // Error metrics
  lines.push('# HELP application_errors_total Total number of application errors');
  lines.push('# TYPE application_errors_total counter');
  lines.push(`application_errors_total ${metrics.errors.total}`);
  
  // Active connections
  lines.push('# HELP http_active_connections Current number of active HTTP connections');
  lines.push('# TYPE http_active_connections gauge');
  lines.push(`http_active_connections ${metrics.activeConnections}`);
  
  // Health score
  lines.push('# HELP application_health_score Application health score (0-100)');
  lines.push('# TYPE application_health_score gauge');
  lines.push(`application_health_score ${performanceMonitor.getHealthScore()}`);
  
  return lines.join('\n') + '\n';
}

module.exports = router;
