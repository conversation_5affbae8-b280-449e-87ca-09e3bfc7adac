const mongoose = require('mongoose');
const logger = require('../utils/logger');

// Database connection health monitoring
class DatabaseMonitor {
  constructor() {
    this.connectionHealth = {
      active: 0,
      available: 0,
      total: 0,
      lastCheck: null
    };
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 1000; // Start with 1 second
    this.isMonitoring = false;
  }

  startMonitoring() {
    if (this.isMonitoring) return;
    this.isMonitoring = true;

    setInterval(() => {
      this.checkConnectionHealth();
    }, 30000); // Check every 30 seconds
  }

  async checkConnectionHealth() {
    try {
      if (mongoose.connection.readyState !== 1) {
        logger.warn('MongoDB connection not ready', {
          readyState: mongoose.connection.readyState
        });
        return;
      }

      const db = mongoose.connection.db;
      const adminDb = db.admin();

      // Get server status for connection pool info
      const serverStatus = await adminDb.serverStatus();

      this.connectionHealth = {
        active: serverStatus.connections?.current || 0,
        available: serverStatus.connections?.available || 0,
        total: serverStatus.connections?.totalCreated || 0,
        lastCheck: new Date().toISOString()
      };

      // Log warning if connection pool is getting low
      if (this.connectionHealth.available < 10) {
        logger.warn('Low database connections available', this.connectionHealth);
      }

      // Log info every 5 minutes
      if (Date.now() % (5 * 60 * 1000) < 30000) {
        logger.info('Database connection health', this.connectionHealth);
      }

    } catch (error) {
      logger.error('Database health check failed:', error);
    }
  }

  async reconnectWithBackoff() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, giving up');
      process.exit(1);
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    logger.info(`Attempting to reconnect to MongoDB (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(async () => {
      try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa', getConnectionOptions());
        logger.info('MongoDB reconnection successful');
        this.reconnectAttempts = 0; // Reset on successful connection
      } catch (error) {
        logger.error('MongoDB reconnection failed:', error);
        this.reconnectWithBackoff();
      }
    }, delay);
  }

  getHealthStatus() {
    return {
      ...this.connectionHealth,
      reconnectAttempts: this.reconnectAttempts,
      isConnected: mongoose.connection.readyState === 1
    };
  }
}

// Create global database monitor instance
const dbMonitor = new DatabaseMonitor();

// Enhanced connection options for production
function getConnectionOptions() {
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    // Connection Pool Settings
    maxPoolSize: isProduction ? 100 : 20, // Increased for production
    minPoolSize: isProduction ? 10 : 5,
    maxIdleTimeMS: 30000,

    // Timeout Settings
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    connectTimeoutMS: 10000,

    // Reliability Settings
    bufferCommands: false,
    retryWrites: true,
    retryReads: true,

    // Read Preference for Replica Sets
    readPreference: isProduction ? 'secondaryPreferred' : 'primary',

    // Write Concern for Data Safety
    writeConcern: {
      w: isProduction ? 'majority' : 1,
      j: true, // Journal acknowledgment
      wtimeout: 10000
    },

    // Monitoring
    monitorCommands: true,
    heartbeatFrequencyMS: 10000,

    // Compression
    compressors: ['zlib'],
    zlibCompressionLevel: 6
  };
}

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/kryptopesa';
    const options = getConnectionOptions();

    logger.info('Connecting to MongoDB...', {
      uri: mongoURI.replace(/\/\/.*@/, '//***:***@'), // Hide credentials in logs
      options: { ...options, writeConcern: undefined } // Don't log write concern details
    });

    const conn = await mongoose.connect(mongoURI, options);

    logger.info(`MongoDB Connected: ${conn.connection.host}`, {
      readyState: conn.connection.readyState,
      name: conn.connection.name
    });

    // Start connection monitoring
    dbMonitor.startMonitoring();

    // Enhanced connection event handlers
    mongoose.connection.on('error', async (err) => {
      logger.error('MongoDB connection error:', err);

      // Attempt reconnection for certain errors
      if (err.name === 'MongoNetworkError' || err.name === 'MongoServerSelectionError') {
        await dbMonitor.reconnectWithBackoff();
      }
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
      dbMonitor.reconnectWithBackoff();
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
      dbMonitor.reconnectAttempts = 0; // Reset reconnection attempts
    });

    mongoose.connection.on('close', () => {
      logger.info('MongoDB connection closed');
    });

    // Monitor slow operations
    mongoose.connection.on('commandStarted', (event) => {
      if (process.env.NODE_ENV === 'development') {
        logger.debug('MongoDB command started:', {
          command: event.commandName,
          collection: event.command?.collection || event.command?.find,
          requestId: event.requestId
        });
      }
    });

    mongoose.connection.on('commandSucceeded', (event) => {
      // Log slow queries (>100ms)
      if (event.duration > 100) {
        logger.warn('Slow MongoDB query detected:', {
          command: event.commandName,
          duration: `${event.duration}ms`,
          requestId: event.requestId
        });
      }
    });

    mongoose.connection.on('commandFailed', (event) => {
      logger.error('MongoDB command failed:', {
        command: event.commandName,
        error: event.failure,
        duration: `${event.duration}ms`,
        requestId: event.requestId
      });
    });

    // Graceful shutdown handlers
    const gracefulShutdown = async (signal) => {
      logger.info(`${signal} received, closing MongoDB connection...`);
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (error) {
        logger.error('Error during MongoDB shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

  } catch (error) {
    logger.error('Database connection failed:', error);

    // In production, attempt reconnection instead of immediate exit
    if (process.env.NODE_ENV === 'production') {
      setTimeout(() => {
        logger.info('Retrying database connection...');
        connectDB();
      }, 5000);
    } else {
      process.exit(1);
    }
  }
};

// Export database monitor for health checks
module.exports = {
  connectDB,
  dbMonitor,
  getConnectionOptions
};
