const mongoose = require('mongoose');

const tradeSchema = new mongoose.Schema({
  tradeId: {
    type: String,
    required: true,
    unique: true
  },
  blockchainTradeId: {
    type: Number,
    required: true,
    unique: true
  },
  seller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  buyer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  offer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Offer',
    required: true
  },
  cryptocurrency: {
    symbol: {
      type: String,
      required: true,
      enum: ['USDT', 'USDC', 'DAI', 'BTC', 'ETH']
    },
    contractAddress: {
      type: String,
      required: function() {
        return ['USDT', 'USDC', 'DAI', 'ETH'].includes(this.cryptocurrency.symbol);
      }
    },
    network: {
      type: String,
      required: true,
      enum: ['polygon', 'ethereum', 'bitcoin']
    },
    amount: {
      type: String, // Use string to avoid precision issues
      required: true
    },
    decimals: {
      type: Number,
      required: true,
      default: 18
    }
  },
  fiat: {
    currency: {
      type: String,
      required: true,
      enum: ['KES', 'TZS', 'UGX', 'RWF', 'USD']
    },
    amount: {
      type: Number,
      required: true
    },
    exchangeRate: {
      type: Number,
      required: true
    }
  },
  payment: {
    method: {
      type: String,
      required: true,
      enum: ['bank_transfer', 'mobile_money', 'cash', 'other']
    },
    details: {
      bankName: String,
      accountNumber: String,
      accountName: String,
      mobileNumber: String,
      provider: String, // M-Pesa, Airtel Money, etc.
      instructions: String
    },
    reference: String,
    proofOfPayment: [String] // Array of image URLs
  },
  status: {
    type: String,
    enum: [
      'created',
      'funded',
      'payment_sent',
      'payment_confirmed',
      'completed',
      'disputed',
      'cancelled',
      'refunded',
      'expired'
    ],
    default: 'created'
  },
  timeline: [{
    status: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    actor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    note: String,
    transactionHash: String
  }],
  escrow: {
    contractAddress: {
      type: String,
      required: true
    },
    transactionHash: String,
    blockNumber: Number,
    gasUsed: Number,
    funded: {
      type: Boolean,
      default: false
    },
    fundingTxHash: String,
    releaseTxHash: String
  },
  commission: {
    rate: {
      type: Number,
      required: true,
      default: 0.005 // 0.5%
    },
    amount: {
      type: String,
      required: true
    },
    currency: {
      type: String,
      required: true
    }
  },
  confirmations: {
    seller: {
      paymentReceived: {
        type: Boolean,
        default: false
      },
      timestamp: Date
    },
    buyer: {
      paymentSent: {
        type: Boolean,
        default: false
      },
      timestamp: Date
    }
  },
  dispute: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dispute',
    default: null
  },
  chat: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: true
  },
  expiresAt: {
    type: Date,
    required: true,
    default: function() {
      return new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    }
  },
  completedAt: Date,
  cancelledAt: Date,
  cancelReason: String,
  metadata: {
    userAgent: String,
    ipAddress: String,
    location: {
      country: String,
      city: String
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
tradeSchema.index({ tradeId: 1 });
tradeSchema.index({ blockchainTradeId: 1 });
tradeSchema.index({ seller: 1, status: 1 });
tradeSchema.index({ buyer: 1, status: 1 });
tradeSchema.index({ status: 1, createdAt: -1 });
tradeSchema.index({ 'cryptocurrency.symbol': 1 });
tradeSchema.index({ 'fiat.currency': 1 });
tradeSchema.index({ expiresAt: 1 });
tradeSchema.index({ createdAt: -1 });

// Compound indexes
tradeSchema.index({ seller: 1, buyer: 1, status: 1 });
tradeSchema.index({ 'cryptocurrency.symbol': 1, 'fiat.currency': 1, status: 1 });

// Virtual for trade duration
tradeSchema.virtual('duration').get(function() {
  if (this.completedAt) {
    return this.completedAt - this.createdAt;
  }
  return Date.now() - this.createdAt;
});

// Virtual for time remaining
tradeSchema.virtual('timeRemaining').get(function() {
  if (this.status === 'completed' || this.status === 'cancelled') {
    return 0;
  }
  return Math.max(0, this.expiresAt - Date.now());
});

// Virtual for is expired
tradeSchema.virtual('isExpired').get(function() {
  return Date.now() > this.expiresAt && !['completed', 'cancelled', 'refunded'].includes(this.status);
});

// Pre-save middleware to generate trade ID
tradeSchema.pre('save', function(next) {
  if (!this.tradeId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.tradeId = `TRD-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Method to add timeline entry
tradeSchema.methods.addTimelineEntry = function(status, actor, note = '', transactionHash = '') {
  this.timeline.push({
    status,
    actor,
    note,
    transactionHash,
    timestamp: new Date()
  });
  return this.save();
};

// Method to update status
tradeSchema.methods.updateStatus = function(newStatus, actor, note = '', transactionHash = '') {
  const oldStatus = this.status;
  this.status = newStatus;
  
  // Add timeline entry
  this.timeline.push({
    status: newStatus,
    actor,
    note: note || `Status changed from ${oldStatus} to ${newStatus}`,
    transactionHash,
    timestamp: new Date()
  });
  
  // Update completion/cancellation timestamps
  if (newStatus === 'completed') {
    this.completedAt = new Date();
  } else if (newStatus === 'cancelled') {
    this.cancelledAt = new Date();
  }
  
  return this.save();
};

// Method to check if user is participant
tradeSchema.methods.isParticipant = function(userId) {
  return this.seller.toString() === userId.toString() || this.buyer.toString() === userId.toString();
};

// Method to get counterparty
tradeSchema.methods.getCounterparty = function(userId) {
  if (this.seller.toString() === userId.toString()) {
    return this.buyer;
  } else if (this.buyer.toString() === userId.toString()) {
    return this.seller;
  }
  return null;
};

// Method to calculate fees
tradeSchema.methods.calculateFees = function() {
  const cryptoAmount = parseFloat(this.cryptocurrency.amount);
  const commissionAmount = cryptoAmount * this.commission.rate;
  const sellerReceives = cryptoAmount - commissionAmount;
  
  return {
    cryptoAmount,
    commissionAmount,
    sellerReceives,
    commissionRate: this.commission.rate
  };
};

// Static method to find active trades for user
tradeSchema.statics.findActiveTradesForUser = function(userId) {
  return this.find({
    $or: [{ seller: userId }, { buyer: userId }],
    status: { $in: ['created', 'funded', 'payment_sent'] }
  }).populate('seller buyer offer chat');
};

// Static method to find trade statistics
tradeSchema.statics.getTradeStats = function(userId) {
  return this.aggregate([
    {
      $match: {
        $or: [{ seller: userId }, { buyer: userId }],
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        totalTrades: { $sum: 1 },
        totalVolume: { $sum: { $toDouble: '$cryptocurrency.amount' } },
        averageTradeSize: { $avg: { $toDouble: '$cryptocurrency.amount' } },
        currencies: { $addToSet: '$cryptocurrency.symbol' }
      }
    }
  ]);
};

module.exports = mongoose.model('Trade', tradeSchema);
