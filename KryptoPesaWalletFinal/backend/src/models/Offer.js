const mongoose = require('mongoose');

const offerSchema = new mongoose.Schema({
  offerId: {
    type: String,
    required: true,
    unique: true
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['buy', 'sell'],
    required: true
  },
  cryptocurrency: {
    symbol: {
      type: String,
      required: true,
      enum: ['USDT', 'USDC', 'DAI', 'BTC', 'ETH']
    },
    contractAddress: {
      type: String,
      required: function() {
        return ['USDT', 'USDC', 'DAI', 'ETH'].includes(this.cryptocurrency.symbol);
      }
    },
    network: {
      type: String,
      required: true,
      enum: ['polygon', 'ethereum', 'bitcoin']
    },
    minAmount: {
      type: String,
      required: true
    },
    maxAmount: {
      type: String,
      required: true
    },
    availableAmount: {
      type: String,
      required: true
    }
  },
  fiat: {
    currency: {
      type: String,
      required: true,
      enum: ['KES', 'TZS', 'UGX', 'RWF', 'USD']
    },
    priceType: {
      type: String,
      enum: ['fixed', 'market', 'margin'],
      required: true
    },
    fixedPrice: {
      type: Number,
      required: function() {
        return this.fiat.priceType === 'fixed';
      }
    },
    marginPercentage: {
      type: Number,
      required: function() {
        return this.fiat.priceType === 'margin';
      },
      min: -50,
      max: 50
    },
    marketPrice: Number, // Current market price (updated periodically)
    effectivePrice: Number // Calculated effective price
  },
  paymentMethods: [{
    method: {
      type: String,
      enum: ['bank_transfer', 'mobile_money', 'cash', 'other'],
      required: true
    },
    details: {
      bankName: String,
      accountNumber: String,
      accountName: String,
      mobileNumber: String,
      provider: String,
      instructions: String
    },
    isPreferred: {
      type: Boolean,
      default: false
    }
  }],
  terms: {
    timeLimit: {
      type: Number,
      default: 30, // minutes
      min: 15,
      max: 120
    },
    instructions: {
      type: String,
      maxlength: 1000
    },
    autoReply: {
      type: String,
      maxlength: 500
    }
  },
  requirements: {
    minReputation: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    minCompletedTrades: {
      type: Number,
      default: 0,
      min: 0
    },
    verifiedUsersOnly: {
      type: Boolean,
      default: false
    },
    allowedCountries: [{
      type: String,
      enum: ['KE', 'TZ', 'UG', 'RW']
    }]
  },
  status: {
    type: String,
    enum: ['active', 'paused', 'inactive', 'expired'],
    default: 'active'
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'friends'],
    default: 'public'
  },
  statistics: {
    views: {
      type: Number,
      default: 0
    },
    responses: {
      type: Number,
      default: 0
    },
    completedTrades: {
      type: Number,
      default: 0
    },
    totalVolume: {
      type: String,
      default: '0'
    }
  },
  location: {
    country: {
      type: String,
      required: true,
      enum: ['KE', 'TZ', 'UG', 'RW']
    },
    city: String,
    region: String
  },
  expiresAt: {
    type: Date,
    required: true,
    default: function() {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
    }
  },
  lastPriceUpdate: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
offerSchema.index({ offerId: 1 });
offerSchema.index({ creator: 1, status: 1 });
offerSchema.index({ type: 1, status: 1 });
offerSchema.index({ 'cryptocurrency.symbol': 1, status: 1 });
offerSchema.index({ 'fiat.currency': 1, status: 1 });
offerSchema.index({ 'location.country': 1, status: 1 });
offerSchema.index({ status: 1, createdAt: -1 });
offerSchema.index({ expiresAt: 1 });

// Compound indexes for efficient querying
offerSchema.index({ 
  type: 1, 
  'cryptocurrency.symbol': 1, 
  'fiat.currency': 1, 
  status: 1,
  'location.country': 1 
});

// Text index for search
offerSchema.index({
  'terms.instructions': 'text',
  'terms.autoReply': 'text'
});

// Virtual for is expired
offerSchema.virtual('isExpired').get(function() {
  return Date.now() > this.expiresAt;
});

// Virtual for available amount in fiat
offerSchema.virtual('availableAmountFiat').get(function() {
  if (this.fiat.effectivePrice) {
    return parseFloat(this.cryptocurrency.availableAmount) * this.fiat.effectivePrice;
  }
  return 0;
});

// Pre-save middleware to generate offer ID
offerSchema.pre('save', function(next) {
  if (!this.offerId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.offerId = `OFF-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Pre-save middleware to calculate effective price
offerSchema.pre('save', function(next) {
  if (this.fiat.priceType === 'fixed') {
    this.fiat.effectivePrice = this.fiat.fixedPrice;
  } else if (this.fiat.priceType === 'market') {
    this.fiat.effectivePrice = this.fiat.marketPrice;
  } else if (this.fiat.priceType === 'margin' && this.fiat.marketPrice) {
    const margin = 1 + (this.fiat.marginPercentage / 100);
    this.fiat.effectivePrice = this.fiat.marketPrice * margin;
  }
  next();
});

// Method to check if user meets requirements
offerSchema.methods.meetsRequirements = function(user) {
  // Check reputation
  if (user.reputation.score < this.requirements.minReputation) {
    return { meets: false, reason: 'Insufficient reputation score' };
  }
  
  // Check completed trades
  if (user.reputation.completedTrades < this.requirements.minCompletedTrades) {
    return { meets: false, reason: 'Insufficient completed trades' };
  }
  
  // Check verification
  if (this.requirements.verifiedUsersOnly && !user.verification.identity.verified) {
    return { meets: false, reason: 'Identity verification required' };
  }
  
  // Check country
  if (this.requirements.allowedCountries.length > 0 && 
      !this.requirements.allowedCountries.includes(user.profile.location.country)) {
    return { meets: false, reason: 'Country not allowed' };
  }
  
  return { meets: true };
};

// Method to check if amount is within limits
offerSchema.methods.isAmountValid = function(amount) {
  const minAmount = parseFloat(this.cryptocurrency.minAmount);
  const maxAmount = parseFloat(this.cryptocurrency.maxAmount);
  const availableAmount = parseFloat(this.cryptocurrency.availableAmount);
  
  if (amount < minAmount) {
    return { valid: false, reason: `Minimum amount is ${minAmount} ${this.cryptocurrency.symbol}` };
  }
  
  if (amount > maxAmount) {
    return { valid: false, reason: `Maximum amount is ${maxAmount} ${this.cryptocurrency.symbol}` };
  }
  
  if (amount > availableAmount) {
    return { valid: false, reason: `Available amount is ${availableAmount} ${this.cryptocurrency.symbol}` };
  }
  
  return { valid: true };
};

// Method to update available amount
offerSchema.methods.updateAvailableAmount = function(amountUsed) {
  const currentAvailable = parseFloat(this.cryptocurrency.availableAmount);
  const newAvailable = currentAvailable - amountUsed;
  
  if (newAvailable < 0) {
    throw new Error('Insufficient available amount');
  }
  
  this.cryptocurrency.availableAmount = newAvailable.toString();
  
  if (newAvailable === 0) {
    this.status = 'inactive';
  }
  
  return this.save();
};

// Method to increment statistics
offerSchema.methods.incrementStats = function(type, value = 1) {
  switch (type) {
    case 'view':
      this.statistics.views += 1;
      break;
    case 'response':
      this.statistics.responses += 1;
      break;
    case 'trade':
      this.statistics.completedTrades += 1;
      if (value) {
        const currentVolume = parseFloat(this.statistics.totalVolume);
        this.statistics.totalVolume = (currentVolume + value).toString();
      }
      break;
  }
  
  return this.save();
};

// Static method to find matching offers
offerSchema.statics.findMatching = function(criteria) {
  const {
    type,
    cryptocurrency,
    fiatCurrency,
    amount,
    country,
    paymentMethod,
    minReputation,
    verifiedOnly
  } = criteria;
  
  const query = {
    status: 'active',
    type: type === 'buy' ? 'sell' : 'buy', // Opposite type
    'cryptocurrency.symbol': cryptocurrency,
    'fiat.currency': fiatCurrency,
    'location.country': country,
    expiresAt: { $gt: new Date() }
  };
  
  if (amount) {
    query['cryptocurrency.minAmount'] = { $lte: amount.toString() };
    query['cryptocurrency.maxAmount'] = { $gte: amount.toString() };
    query['cryptocurrency.availableAmount'] = { $gte: amount.toString() };
  }
  
  if (paymentMethod) {
    query['paymentMethods.method'] = paymentMethod;
  }
  
  return this.find(query)
    .populate('creator', 'username profile reputation verification')
    .sort({ 'fiat.effectivePrice': type === 'buy' ? 1 : -1 }); // Best prices first
};

module.exports = mongoose.model('Offer', offerSchema);
