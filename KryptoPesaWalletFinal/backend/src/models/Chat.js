const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },
  type: {
    type: String,
    enum: ['text', 'image', 'file', 'system'],
    default: 'text'
  },
  attachments: [{
    url: String,
    filename: String,
    size: Number,
    mimeType: String
  }],
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  edited: {
    isEdited: {
      type: Boolean,
      default: false
    },
    editedAt: Date,
    originalContent: String
  }
}, {
  timestamps: true
});

const chatSchema = new mongoose.Schema({
  trade: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Trade',
    required: true,
    unique: true
  },
  participants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    role: {
      type: String,
      enum: ['seller', 'buyer', 'admin'],
      required: true
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    lastSeen: {
      type: Date,
      default: Date.now
    }
  }],
  messages: [messageSchema],
  status: {
    type: String,
    enum: ['active', 'archived', 'locked'],
    default: 'active'
  },
  metadata: {
    totalMessages: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    },
    autoMessages: {
      welcomeSent: {
        type: Boolean,
        default: false
      },
      remindersSent: {
        type: Number,
        default: 0
      }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
chatSchema.index({ trade: 1 });
chatSchema.index({ 'participants.user': 1 });
chatSchema.index({ status: 1, 'metadata.lastActivity': -1 });
chatSchema.index({ 'messages.sender': 1 });
chatSchema.index({ 'messages.createdAt': -1 });

// Virtual for unread message count per user
chatSchema.virtual('unreadCount').get(function() {
  // This would be calculated per user in the application logic
  return 0;
});

// Method to add message
chatSchema.methods.addMessage = function(senderId, content, type = 'text', attachments = []) {
  const message = {
    sender: senderId,
    content,
    type,
    attachments,
    readBy: [{ user: senderId, readAt: new Date() }]
  };
  
  this.messages.push(message);
  this.metadata.totalMessages += 1;
  this.metadata.lastActivity = new Date();
  
  return this.save();
};

// Method to add system message
chatSchema.methods.addSystemMessage = function(content) {
  return this.addMessage(null, content, 'system');
};

// Method to mark messages as read
chatSchema.methods.markAsRead = function(userId, messageIds = []) {
  let updated = false;
  
  this.messages.forEach(message => {
    if (messageIds.length === 0 || messageIds.includes(message._id.toString())) {
      const readEntry = message.readBy.find(r => r.user.toString() === userId.toString());
      if (!readEntry) {
        message.readBy.push({ user: userId, readAt: new Date() });
        updated = true;
      }
    }
  });
  
  if (updated) {
    // Update participant's last seen
    const participant = this.participants.find(p => p.user.toString() === userId.toString());
    if (participant) {
      participant.lastSeen = new Date();
    }
    
    return this.save();
  }
  
  return Promise.resolve(this);
};

// Method to get unread messages for user
chatSchema.methods.getUnreadMessages = function(userId) {
  return this.messages.filter(message => {
    const readEntry = message.readBy.find(r => r.user.toString() === userId.toString());
    return !readEntry;
  });
};

// Method to check if user is participant
chatSchema.methods.isParticipant = function(userId) {
  return this.participants.some(p => p.user.toString() === userId.toString());
};

// Method to get participant role
chatSchema.methods.getParticipantRole = function(userId) {
  const participant = this.participants.find(p => p.user.toString() === userId.toString());
  return participant ? participant.role : null;
};

// Static method to create chat for trade
chatSchema.statics.createForTrade = function(tradeId, sellerId, buyerId) {
  return this.create({
    trade: tradeId,
    participants: [
      { user: sellerId, role: 'seller' },
      { user: buyerId, role: 'buyer' }
    ],
    status: 'active'
  });
};

module.exports = mongoose.model('Chat', chatSchema);
