/**
 * Graceful Degradation Service
 * Provides fallback functionality when services are unavailable
 */

const logger = require('../utils/logger');
const { circuitBreakers } = require('../utils/circuitBreaker');

class GracefulDegradationService {
  constructor() {
    this.serviceStatus = {
      redis: 'healthy',
      blockchain: 'healthy',
      database: 'healthy',
      external: 'healthy'
    };
    
    this.fallbackData = new Map();
    this.degradationLevel = 'none'; // none, partial, severe
    this.lastHealthCheck = Date.now();
    
    this.startHealthMonitoring();
  }

  /**
   * Check overall system health and adjust degradation level
   */
  async checkSystemHealth() {
    const healthChecks = {
      redis: await this.checkRedisHealth(),
      blockchain: await this.checkBlockchainHealth(),
      database: await this.checkDatabaseHealth(),
      external: await this.checkExternalServicesHealth()
    };

    // Update service status
    this.serviceStatus = healthChecks;
    
    // Determine degradation level
    const unhealthyServices = Object.values(healthChecks).filter(status => status !== 'healthy').length;
    
    if (unhealthyServices === 0) {
      this.degradationLevel = 'none';
    } else if (unhealthyServices <= 2) {
      this.degradationLevel = 'partial';
    } else {
      this.degradationLevel = 'severe';
    }

    this.lastHealthCheck = Date.now();
    
    if (this.degradationLevel !== 'none') {
      logger.warn('System degradation detected:', {
        level: this.degradationLevel,
        unhealthyServices,
        serviceStatus: this.serviceStatus
      });
    }

    return {
      degradationLevel: this.degradationLevel,
      serviceStatus: this.serviceStatus,
      lastCheck: this.lastHealthCheck
    };
  }

  /**
   * Get cached data with fallback
   */
  async getCachedData(key, fetchFunction, ttl = 300000) {
    // Try Redis first
    if (this.serviceStatus.redis === 'healthy') {
      try {
        const { getCache } = require('../config/redis');
        const cached = await getCache(key);
        if (cached) {
          return JSON.parse(cached);
        }
      } catch (error) {
        logger.warn('Redis cache failed, using memory fallback:', error.message);
      }
    }

    // Use memory fallback
    const memoryData = this.fallbackData.get(key);
    if (memoryData && Date.now() - memoryData.timestamp < ttl) {
      logger.debug(`Using memory cache for key: ${key}`);
      return memoryData.data;
    }

    // Fetch fresh data
    try {
      const data = await fetchFunction();
      
      // Store in memory fallback
      this.fallbackData.set(key, {
        data,
        timestamp: Date.now()
      });

      // Try to store in Redis if available
      if (this.serviceStatus.redis === 'healthy') {
        try {
          const { setCache } = require('../config/redis');
          await setCache(key, data, ttl / 1000);
        } catch (error) {
          logger.debug('Failed to cache in Redis:', error.message);
        }
      }

      return data;
    } catch (error) {
      // Return stale data if available
      if (memoryData) {
        logger.warn(`Returning stale data for key ${key} due to fetch error:`, error.message);
        return memoryData.data;
      }
      throw error;
    }
  }

  /**
   * Execute database operation with fallback
   */
  async executeDatabaseOperation(operation, fallbackData = null) {
    if (this.serviceStatus.database === 'healthy') {
      try {
        return await operation();
      } catch (error) {
        logger.warn('Database operation failed, checking for fallback:', error.message);
        
        if (fallbackData) {
          logger.info('Using fallback data for database operation');
          return fallbackData;
        }
        throw error;
      }
    } else {
      if (fallbackData) {
        logger.warn('Database unhealthy, using fallback data');
        return fallbackData;
      }
      throw new Error('Database unavailable and no fallback data provided');
    }
  }

  /**
   * Execute blockchain operation with fallback
   */
  async executeBlockchainOperation(operation, fallbackResponse = null) {
    if (this.serviceStatus.blockchain === 'healthy') {
      try {
        return await circuitBreakers.blockchain.execute(operation);
      } catch (error) {
        logger.warn('Blockchain operation failed:', error.message);
        
        if (fallbackResponse) {
          logger.info('Using fallback response for blockchain operation');
          return fallbackResponse;
        }
        throw error;
      }
    } else {
      if (fallbackResponse) {
        logger.warn('Blockchain unhealthy, using fallback response');
        return fallbackResponse;
      }
      throw new Error('Blockchain unavailable and no fallback response provided');
    }
  }

  /**
   * Get degraded feature set based on current system health
   */
  getAvailableFeatures() {
    const features = {
      trading: true,
      walletOperations: true,
      realTimeUpdates: true,
      notifications: true,
      analytics: true,
      reporting: true
    };

    switch (this.degradationLevel) {
      case 'partial':
        features.realTimeUpdates = this.serviceStatus.redis === 'healthy';
        features.analytics = false; // Disable non-critical features
        break;
        
      case 'severe':
        features.realTimeUpdates = false;
        features.notifications = false;
        features.analytics = false;
        features.reporting = false;
        features.walletOperations = this.serviceStatus.blockchain === 'healthy';
        break;
    }

    return features;
  }

  /**
   * Get user-friendly status message
   */
  getStatusMessage() {
    switch (this.degradationLevel) {
      case 'none':
        return 'All systems operational';
      case 'partial':
        return 'Some features may be temporarily limited';
      case 'severe':
        return 'Limited functionality available - core trading features only';
      default:
        return 'System status unknown';
    }
  }

  /**
   * Health check methods
   */
  async checkRedisHealth() {
    try {
      const { getRedisClient } = require('../config/redis');
      const client = getRedisClient();
      
      if (!client || !client.isReady) {
        return 'unhealthy';
      }
      
      await client.ping();
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkBlockchainHealth() {
    try {
      // Check circuit breaker state
      const blockchainCB = circuitBreakers.blockchain;
      if (blockchainCB.getState().state === 'OPEN') {
        return 'unhealthy';
      }
      
      // Simple health check - get latest block
      const ethereumService = require('./blockchain/ethereumService');
      const provider = ethereumService.getProvider('polygon');
      await provider.getBlockNumber();
      
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkDatabaseHealth() {
    try {
      const mongoose = require('mongoose');
      
      if (mongoose.connection.readyState !== 1) {
        return 'unhealthy';
      }
      
      // Simple ping test
      await mongoose.connection.db.admin().ping();
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  async checkExternalServicesHealth() {
    // For now, assume external services are healthy
    // In production, implement actual health checks for external APIs
    return 'healthy';
  }

  /**
   * Start background health monitoring
   */
  startHealthMonitoring() {
    // Check health every 30 seconds
    setInterval(async () => {
      try {
        await this.checkSystemHealth();
      } catch (error) {
        logger.error('Health monitoring error:', error);
      }
    }, 30000);

    // Clean up old fallback data every 5 minutes
    setInterval(() => {
      this.cleanupFallbackData();
    }, 300000);

    logger.info('Graceful degradation health monitoring started');
  }

  /**
   * Clean up expired fallback data
   */
  cleanupFallbackData() {
    const now = Date.now();
    const maxAge = 3600000; // 1 hour
    
    for (const [key, data] of this.fallbackData.entries()) {
      if (now - data.timestamp > maxAge) {
        this.fallbackData.delete(key);
      }
    }
  }

  /**
   * Force degradation level (for testing)
   */
  forceDegradationLevel(level) {
    this.degradationLevel = level;
    logger.warn(`Degradation level manually set to: ${level}`);
  }

  /**
   * Get current system status
   */
  getSystemStatus() {
    return {
      degradationLevel: this.degradationLevel,
      serviceStatus: this.serviceStatus,
      availableFeatures: this.getAvailableFeatures(),
      statusMessage: this.getStatusMessage(),
      lastHealthCheck: this.lastHealthCheck
    };
  }
}

// Create singleton instance
const gracefulDegradation = new GracefulDegradationService();

module.exports = {
  GracefulDegradationService,
  gracefulDegradation
};
