const AWS = require('aws-sdk');
const { ethers } = require('ethers');
const crypto = require('crypto');
const logger = require('../../utils/logger');

class SecureKeyService {
  constructor() {
    this.kms = new AWS.KMS({
      region: process.env.AWS_REGION || 'us-east-1'
    });
    this.encryptedKeys = new Map();
    this.decryptedKeys = new Map();
    this.keyRotationInterval = 24 * 60 * 60 * 1000; // 24 hours
  }

  /**
   * Initialize secure key management
   */
  async initialize() {
    try {
      // Load encrypted keys from secure storage
      await this.loadEncryptedKeys();
      
      // Set up key rotation schedule
      this.setupKeyRotation();
      
      logger.info('Secure key service initialized');
    } catch (error) {
      logger.error('Failed to initialize secure key service:', error);
      throw error;
    }
  }

  /**
   * Get wallet instance with secure key management
   */
  async getSecureWallet(network = 'polygon') {
    try {
      let privateKey = this.decryptedKeys.get(network);
      
      if (!privateKey) {
        privateKey = await this.decryptPrivateKey(network);
        this.decryptedKeys.set(network, privateKey);
        
        // Auto-clear from memory after 1 hour
        setTimeout(() => {
          this.decryptedKeys.delete(network);
        }, 60 * 60 * 1000);
      }
      
      const provider = this.getProvider(network);
      return new ethers.Wallet(privateKey, provider);
    } catch (error) {
      logger.error(`Failed to get secure wallet for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Decrypt private key using AWS KMS
   */
  async decryptPrivateKey(network) {
    try {
      const encryptedKey = this.encryptedKeys.get(network);
      if (!encryptedKey) {
        throw new Error(`No encrypted key found for network: ${network}`);
      }

      const params = {
        CiphertextBlob: Buffer.from(encryptedKey, 'base64'),
        EncryptionContext: {
          network: network,
          service: 'kryptopesa-escrow'
        }
      };

      const result = await this.kms.decrypt(params).promise();
      return result.Plaintext.toString('utf8');
    } catch (error) {
      logger.error(`Failed to decrypt private key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Encrypt and store private key using AWS KMS
   */
  async encryptPrivateKey(network, privateKey) {
    try {
      const params = {
        KeyId: process.env.AWS_KMS_KEY_ID,
        Plaintext: privateKey,
        EncryptionContext: {
          network: network,
          service: 'kryptopesa-escrow'
        }
      };

      const result = await this.kms.encrypt(params).promise();
      const encryptedKey = result.CiphertextBlob.toString('base64');
      
      this.encryptedKeys.set(network, encryptedKey);
      
      // Store in secure database or AWS Secrets Manager
      await this.storeEncryptedKey(network, encryptedKey);
      
      logger.info(`Private key encrypted and stored for network: ${network}`);
    } catch (error) {
      logger.error(`Failed to encrypt private key for ${network}:`, error);
      throw error;
    }
  }

  /**
   * Load encrypted keys from secure storage
   */
  async loadEncryptedKeys() {
    try {
      // In production, load from AWS Secrets Manager or secure database
      const networks = ['polygon', 'ethereum'];
      
      for (const network of networks) {
        const encryptedKey = await this.getStoredEncryptedKey(network);
        if (encryptedKey) {
          this.encryptedKeys.set(network, encryptedKey);
        }
      }
    } catch (error) {
      logger.error('Failed to load encrypted keys:', error);
      throw error;
    }
  }

  /**
   * Store encrypted key in secure storage
   */
  async storeEncryptedKey(network, encryptedKey) {
    // Implementation depends on chosen secure storage
    // Options: AWS Secrets Manager, HashiCorp Vault, or encrypted database
    
    if (process.env.USE_AWS_SECRETS_MANAGER === 'true') {
      const secretsManager = new AWS.SecretsManager({
        region: process.env.AWS_REGION
      });
      
      const params = {
        SecretId: `kryptopesa-${network}-private-key`,
        SecretString: encryptedKey
      };
      
      await secretsManager.updateSecret(params).promise();
    } else {
      // Fallback to encrypted database storage
      // Implementation would go here
    }
  }

  /**
   * Get stored encrypted key
   */
  async getStoredEncryptedKey(network) {
    if (process.env.USE_AWS_SECRETS_MANAGER === 'true') {
      const secretsManager = new AWS.SecretsManager({
        region: process.env.AWS_REGION
      });
      
      try {
        const result = await secretsManager.getSecretValue({
          SecretId: `kryptopesa-${network}-private-key`
        }).promise();
        
        return result.SecretString;
      } catch (error) {
        if (error.code === 'ResourceNotFoundException') {
          return null;
        }
        throw error;
      }
    }
    
    return null;
  }

  /**
   * Set up automatic key rotation
   */
  setupKeyRotation() {
    setInterval(async () => {
      try {
        await this.rotateKeys();
      } catch (error) {
        logger.error('Key rotation failed:', error);
      }
    }, this.keyRotationInterval);
  }

  /**
   * Rotate encryption keys
   */
  async rotateKeys() {
    logger.info('Starting key rotation process');
    
    // Clear decrypted keys from memory
    this.decryptedKeys.clear();
    
    // Re-encrypt with new KMS key if needed
    // Implementation depends on key rotation policy
    
    logger.info('Key rotation completed');
  }

  /**
   * Get provider for network
   */
  getProvider(network) {
    const providers = {
      polygon: new ethers.providers.JsonRpcProvider(process.env.POLYGON_RPC_URL),
      ethereum: new ethers.providers.JsonRpcProvider(process.env.ETHEREUM_RPC_URL)
    };
    
    return providers[network];
  }

  /**
   * Secure cleanup on shutdown
   */
  cleanup() {
    // Clear all decrypted keys from memory
    this.decryptedKeys.clear();
    this.encryptedKeys.clear();
    
    logger.info('Secure key service cleaned up');
  }
}

module.exports = new SecureKeyService();
