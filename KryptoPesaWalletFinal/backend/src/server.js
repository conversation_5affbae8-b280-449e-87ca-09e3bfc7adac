const express = require('express');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');

const { connectDB } = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');
const chatService = require('./services/chatService');

// Import routes
const { router: authRoutes } = require('./routes/auth');
const userRoutes = require('./routes/user');
const walletRoutes = require('./routes/wallet');
const tradeRoutes = require('./routes/trade');
const offerRoutes = require('./routes/offer');
const tradingRoutes = require('./routes/trading');
const chatRoutes = require('./routes/chat');
const adminRoutes = require('./routes/admin');
const healthRoutes = require('./routes/health');
const metricsRoutes = require('./routes/metrics');

// Import socket handlers
const socketHandler = require('./services/socketService');

// Import security middleware
const {
  authLimiter,
  tradingLimiter,
  walletLimiter,
  chatLimiter,
  generalLimiter,
  publicLimiter,
  progressiveLimiter,
  createBodySizeLimiter
} = require('./middleware/advancedRateLimit');

const {
  securityValidation,
  validateTradeData,
  validateUserData
} = require('./middleware/securityValidation');

// Import monitoring middleware
const {
  requestMonitoring,
  errorMonitoring
} = require('./middleware/performanceMonitoring');

// Import response optimization middleware
const {
  responseFilterMiddleware,
  mobileOptimizeMiddleware
} = require('./utils/responseFilter');

require('dotenv').config({ path: '../.env' });

// Validate environment variables before starting
const { validateEnvironment, getEnvSummary } = require('./utils/validateEnv');
validateEnvironment();

// Log environment summary
logger.info('Environment configuration:', getEnvSummary());

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://kryptopesa.com', 'https://admin.kryptopesa.com']
      : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:19006'],
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true
  }
});

// Connect to databases
connectDB();
// Connect to Redis
connectRedis();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://kryptopesa.com', 'https://admin.kryptopesa.com']
    : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:19006'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // 24 hours
}));

// Progressive rate limiting (applies to all requests)
app.use(progressiveLimiter);

// Security validation middleware
app.use(securityValidation);

// Performance monitoring middleware
app.use(requestMonitoring());

// Response optimization middleware
app.use(responseFilterMiddleware);
app.use(mobileOptimizeMiddleware);

// Body parsing middleware with size limits
app.use(compression());
app.use(express.json({
  limit: '1mb',
  verify: (req, res, buf) => {
    // Store raw body for webhook verification if needed
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '1mb' }));

// Logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
}

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health and monitoring routes
app.use('/health', publicLimiter, healthRoutes);
app.use('/metrics', metricsRoutes);

// API routes with specific rate limiting and validation
app.use('/api/auth', authLimiter, validateUserData, authRoutes);
app.use('/api/users', generalLimiter, userRoutes);
app.use('/api/wallet', walletLimiter, createBodySizeLimiter(5 * 1024 * 1024), walletRoutes); // 5MB for wallet operations
app.use('/api/trades', tradingLimiter, validateTradeData, createBodySizeLimiter(10 * 1024 * 1024), tradeRoutes); // 10MB for trade data with images
app.use('/api/offers', tradingLimiter, validateTradeData, offerRoutes);
app.use('/api/trading', tradingLimiter, validateTradeData, createBodySizeLimiter(10 * 1024 * 1024), tradingRoutes); // 10MB for trading data
app.use('/api/chat', chatLimiter, createBodySizeLimiter(2 * 1024 * 1024), chatRoutes); // 2MB for chat with images
app.use('/api/admin', generalLimiter, adminRoutes);

// Socket.io setup
socketHandler(io);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error monitoring middleware
app.use(errorMonitoring());

// Error handling middleware
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  logger.info(`KryptoPesa API server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

module.exports = { app, server, io };
