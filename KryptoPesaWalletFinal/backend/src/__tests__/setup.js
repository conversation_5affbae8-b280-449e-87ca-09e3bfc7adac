const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// Setup test database
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
}, 30000); // 30 second timeout

// Clean up after each test
afterEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Close database connection after all tests
afterAll(async () => {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
});

// Mock external services
jest.mock('../services/socketService', () => ({
  emit: jest.fn(),
  to: jest.fn().mockReturnThis(),
  broadcast: jest.fn(),
}));

// Note: notificationService mock removed as service doesn't exist yet

// Note: blockchainService mock removed as service structure may differ

// Global test utilities
global.createTestUser = async (userData = {}) => {
  const User = require('../models/User');
  const bcrypt = require('bcryptjs');
  
  const defaultUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password: await bcrypt.hash('password123', 10),
    phone: '+254700000000',
    profile: {
      firstName: 'Test',
      lastName: 'User',
      location: {
        country: 'KE',
        city: 'Nairobi'
      }
    },
    verification: {
      email: { verified: true },
      phone: { verified: true }
    }
  };
  
  const user = new User({ ...defaultUser, ...userData });
  await user.save();
  return user;
};

global.createTestOffer = async (userId, offerData = {}) => {
  const Offer = require('../models/Offer');
  
  const defaultOffer = {
    creator: userId,
    type: 'sell',
    cryptocurrency: {
      symbol: 'USDT',
      amount: '100',
      network: 'TRC20'
    },
    fiat: {
      currency: 'KES',
      amount: 13000,
      paymentMethods: ['M-Pesa', 'Bank Transfer']
    },
    terms: {
      minAmount: 1000,
      maxAmount: 13000,
      timeLimit: 30,
      instructions: 'Test instructions'
    },
    status: 'active'
  };
  
  const offer = new Offer({ ...defaultOffer, ...offerData });
  await offer.save();
  return offer;
};

global.createTestTrade = async (sellerId, buyerId, offerId, tradeData = {}) => {
  const Trade = require('../models/Trade');
  
  const defaultTrade = {
    tradeId: `TRD${Date.now()}`,
    seller: sellerId,
    buyer: buyerId,
    offer: offerId,
    cryptocurrency: {
      symbol: 'USDT',
      amount: '50',
      network: 'TRC20'
    },
    fiat: {
      currency: 'KES',
      amount: 6500
    },
    status: 'created',
    expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
  };
  
  const trade = new Trade({ ...defaultTrade, ...tradeData });
  await trade.save();
  return trade;
};

global.generateAuthToken = (userId) => {
  const jwt = require('jsonwebtoken');
  return jwt.sign({ userId }, process.env.JWT_SECRET || 'test-secret', { expiresIn: '1h' });
};

// Test data generators
global.testData = {
  validUser: {
    username: 'testuser123',
    email: '<EMAIL>',
    password: 'Password123!',
    phone: '+254700000000',
    profile: {
      firstName: 'Test',
      lastName: 'User'
    }
  },
  
  validOffer: {
    type: 'sell',
    cryptocurrency: {
      symbol: 'USDT',
      amount: '100',
      network: 'TRC20'
    },
    fiat: {
      currency: 'KES',
      amount: 13000,
      paymentMethods: ['M-Pesa']
    },
    terms: {
      minAmount: 1000,
      maxAmount: 13000,
      timeLimit: 30,
      instructions: 'Test payment instructions'
    }
  },
  
  validTrade: {
    cryptocurrency: {
      symbol: 'USDT',
      amount: '50',
      network: 'TRC20'
    },
    fiat: {
      currency: 'KES',
      amount: 6500
    }
  }
};

// Environment setup
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.BCRYPT_ROUNDS = '1'; // Faster hashing for tests
