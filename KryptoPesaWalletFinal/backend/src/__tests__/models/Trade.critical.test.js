/**
 * Critical Trade Model Tests
 * Tests for trade state transitions, concurrency, and data integrity
 */

const mongoose = require('mongoose');
const Trade = require('../../models/Trade');
const User = require('../../models/User');
const Offer = require('../../models/Offer');
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('Trade Model - Critical Business Logic', () => {
  let mongoServer;
  let seller, buyer, offer;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean collections
    await Trade.deleteMany({});
    await User.deleteMany({});
    await Offer.deleteMany({});

    // Create test users
    seller = await createTestUser({
      username: 'seller',
      email: '<EMAIL>'
    });

    buyer = await createTestUser({
      username: 'buyer',
      email: '<EMAIL>'
    });

    // Create test offer
    offer = await createTestOffer(seller._id);
  });

  describe('Trade State Transitions', () => {
    test('should follow valid state transition sequence', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Initial state
      expect(trade.status).toBe('created');
      
      // created -> funded
      await trade.updateStatus('funded', seller._id, 'Trade funded');
      expect(trade.status).toBe('funded');
      expect(trade.timeline).toHaveLength(2); // created + funded
      
      // funded -> payment_sent
      await trade.updateStatus('payment_sent', buyer._id, 'Payment sent');
      expect(trade.status).toBe('payment_sent');
      
      // payment_sent -> completed
      await trade.updateStatus('completed', seller._id, 'Payment confirmed');
      expect(trade.status).toBe('completed');
      expect(trade.completedAt).toBeDefined();
    });

    test('should reject invalid state transitions', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Cannot go directly from created to completed
      await expect(
        trade.updateStatus('completed', seller._id, 'Invalid transition')
      ).rejects.toThrow('Invalid status transition');
      
      // Cannot go backwards
      await trade.updateStatus('funded', seller._id, 'Trade funded');
      await expect(
        trade.updateStatus('created', seller._id, 'Going backwards')
      ).rejects.toThrow('Invalid status transition');
    });

    test('should handle dispute state transitions correctly', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      await trade.updateStatus('funded', seller._id, 'Trade funded');
      
      // Can dispute from funded state
      await trade.updateStatus('disputed', buyer._id, 'Payment issue');
      expect(trade.status).toBe('disputed');
      
      // Can resolve dispute to completed
      await trade.updateStatus('completed', seller._id, 'Dispute resolved');
      expect(trade.status).toBe('completed');
    });

    test('should handle cancellation from valid states', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Can cancel from created state
      await trade.updateStatus('cancelled', buyer._id, 'Changed mind');
      expect(trade.status).toBe('cancelled');
      
      // Create new trade for funded state test
      const trade2 = await createTestTrade(seller._id, buyer._id, offer._id);
      await trade2.updateStatus('funded', seller._id, 'Trade funded');
      
      // Can cancel from funded state (with refund)
      await trade2.updateStatus('cancelled', seller._id, 'Buyer not responding');
      expect(trade2.status).toBe('cancelled');
    });
  });

  describe('Concurrent Trade Creation', () => {
    test('should handle concurrent trade creation on same offer', async () => {
      // Create multiple buyers
      const buyer2 = await createTestUser({
        username: 'buyer2',
        email: '<EMAIL>'
      });

      // Attempt to create trades concurrently
      const tradePromises = [
        createTestTrade(seller._id, buyer._id, offer._id, {
          cryptocurrency: { amount: '50' }
        }),
        createTestTrade(seller._id, buyer2._id, offer._id, {
          cryptocurrency: { amount: '60' }
        })
      ];

      const trades = await Promise.all(tradePromises);
      
      // Both trades should be created successfully
      expect(trades).toHaveLength(2);
      expect(trades[0].tradeId).not.toBe(trades[1].tradeId);
      
      // Verify offer amount is properly tracked
      const updatedOffer = await Offer.findById(offer._id);
      expect(parseFloat(updatedOffer.cryptocurrency.availableAmount)).toBe(
        parseFloat(offer.cryptocurrency.availableAmount) - 50 - 60
      );
    });

    test('should prevent overselling offer amount', async () => {
      // Create offer with limited amount
      const limitedOffer = await createTestOffer(seller._id, {
        cryptocurrency: {
          symbol: 'USDT',
          amount: '100',
          availableAmount: '100',
          network: 'polygon'
        }
      });

      const buyer2 = await createTestUser({
        username: 'buyer2',
        email: '<EMAIL>'
      });

      // First trade takes 80
      await createTestTrade(seller._id, buyer._id, limitedOffer._id, {
        cryptocurrency: { amount: '80' }
      });

      // Second trade tries to take 50 (should fail - only 20 left)
      await expect(
        createTestTrade(seller._id, buyer2._id, limitedOffer._id, {
          cryptocurrency: { amount: '50' }
        })
      ).rejects.toThrow('Insufficient offer amount');
    });
  });

  describe('Trade Expiration Handling', () => {
    test('should automatically expire trades after timeout', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id, {
        expiresAt: new Date(Date.now() - 1000) // Already expired
      });

      // Check if trade is expired
      expect(trade.isExpired()).toBe(true);
      
      // Attempting to update expired trade should fail
      await expect(
        trade.updateStatus('funded', seller._id, 'Too late')
      ).rejects.toThrow('Trade has expired');
    });

    test('should handle trade extension correctly', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      const originalExpiry = trade.expiresAt;
      
      // Extend trade by 30 minutes
      await trade.extendExpiry(30);
      
      expect(trade.expiresAt.getTime()).toBeGreaterThan(originalExpiry.getTime());
      expect(trade.timeline.some(event => event.action === 'extended')).toBe(true);
    });
  });

  describe('Data Integrity and Validation', () => {
    test('should validate trade amounts against offer limits', async () => {
      const offerWithLimits = await createTestOffer(seller._id, {
        terms: {
          minAmount: 1000,
          maxAmount: 5000
        }
      });

      // Amount too small
      await expect(
        createTestTrade(seller._id, buyer._id, offerWithLimits._id, {
          fiat: { amount: 500 }
        })
      ).rejects.toThrow('validation failed');

      // Amount too large
      await expect(
        createTestTrade(seller._id, buyer._id, offerWithLimits._id, {
          fiat: { amount: 10000 }
        })
      ).rejects.toThrow('validation failed');
    });

    test('should maintain referential integrity', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Verify all references are valid
      expect(trade.seller.toString()).toBe(seller._id.toString());
      expect(trade.buyer.toString()).toBe(buyer._id.toString());
      expect(trade.offer.toString()).toBe(offer._id.toString());
      
      // Populate references
      await trade.populate(['seller', 'buyer', 'offer']);
      expect(trade.seller.username).toBe('seller');
      expect(trade.buyer.username).toBe('buyer');
      expect(trade.offer.type).toBe('sell');
    });

    test('should calculate commission correctly', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id, {
        fiat: { amount: 10000 }
      });

      const commission = trade.calculateCommission();
      
      // Default commission rate is 1% (100 basis points)
      expect(commission.amount).toBe(100); // 1% of 10000
      expect(commission.rate).toBe(100); // basis points
      expect(commission.currency).toBe('KES');
    });
  });

  describe('Trade Timeline and Audit Trail', () => {
    test('should maintain complete timeline of events', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Initial timeline should have creation event
      expect(trade.timeline).toHaveLength(1);
      expect(trade.timeline[0].action).toBe('created');
      expect(trade.timeline[0].actor.toString()).toBe(buyer._id.toString());
      
      // Add status updates
      await trade.updateStatus('funded', seller._id, 'Escrow funded');
      await trade.updateStatus('payment_sent', buyer._id, 'Payment sent via M-Pesa');
      
      // Timeline should reflect all changes
      expect(trade.timeline).toHaveLength(3);
      expect(trade.timeline.map(event => event.action)).toEqual([
        'created', 'funded', 'payment_sent'
      ]);
    });

    test('should record blockchain transaction hashes', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      const txHash = '0x1234567890abcdef';
      await trade.updateStatus('funded', seller._id, 'Blockchain funding', txHash);
      
      const fundingEvent = trade.timeline.find(event => event.action === 'funded');
      expect(fundingEvent.blockchainTxHash).toBe(txHash);
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle database transaction failures gracefully', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Simulate database error during status update
      const originalSave = trade.save;
      trade.save = jest.fn().mockRejectedValue(new Error('Database error'));
      
      await expect(
        trade.updateStatus('funded', seller._id, 'Should fail')
      ).rejects.toThrow('Database error');
      
      // Restore original save method
      trade.save = originalSave;
      
      // Verify trade status wasn't changed
      const freshTrade = await Trade.findById(trade._id);
      expect(freshTrade.status).toBe('created');
    });

    test('should validate user permissions for status updates', async () => {
      const trade = await createTestTrade(seller._id, buyer._id, offer._id);
      
      // Create unauthorized user
      const unauthorizedUser = await createTestUser({
        username: 'unauthorized',
        email: '<EMAIL>'
      });
      
      // Unauthorized user cannot fund trade
      await expect(
        trade.updateStatus('funded', unauthorizedUser._id, 'Unauthorized')
      ).rejects.toThrow('Unauthorized');
      
      // Buyer cannot fund trade (only seller can)
      await expect(
        trade.updateStatus('funded', buyer._id, 'Wrong user')
      ).rejects.toThrow('Only seller can fund trade');
    });
  });

  // Helper functions
  async function createTestUser(userData = {}) {
    const defaultUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'hashedpassword',
      phone: '+254700000000',
      profile: {
        firstName: 'Test',
        lastName: 'User',
        location: { country: 'KE', city: 'Nairobi' }
      },
      verification: {
        email: { verified: true },
        phone: { verified: true }
      }
    };
    
    const user = new User({ ...defaultUser, ...userData });
    await user.save();
    return user;
  }

  async function createTestOffer(userId, offerData = {}) {
    const defaultOffer = {
      creator: userId,
      type: 'sell',
      cryptocurrency: {
        symbol: 'USDT',
        amount: '1000',
        availableAmount: '1000',
        network: 'polygon'
      },
      fiat: {
        currency: 'KES',
        amount: 130000,
        paymentMethods: ['M-Pesa']
      },
      terms: {
        minAmount: 1000,
        maxAmount: 130000,
        timeLimit: 30
      },
      status: 'active'
    };
    
    const offer = new Offer({ ...defaultOffer, ...offerData });
    await offer.save();
    return offer;
  }

  async function createTestTrade(sellerId, buyerId, offerId, tradeData = {}) {
    const defaultTrade = {
      tradeId: `TRD${Date.now()}${Math.random().toString(36).substr(2, 5)}`,
      seller: sellerId,
      buyer: buyerId,
      offer: offerId,
      cryptocurrency: {
        symbol: 'USDT',
        amount: '100',
        network: 'polygon'
      },
      fiat: {
        currency: 'KES',
        amount: 13000
      },
      status: 'created',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
    };
    
    const trade = new Trade({ ...defaultTrade, ...tradeData });
    await trade.save();
    return trade;
  }
});
