const User = require('../../models/User');
const bcrypt = require('bcryptjs');

describe('User Model', () => {
  describe('User Creation', () => {
    it('should create a valid user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000000',
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      };

      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.username).toBe(userData.username);
      expect(savedUser.email).toBe(userData.email);
      expect(savedUser.phone).toBe(userData.phone);
      expect(savedUser.profile.firstName).toBe(userData.profile.firstName);
      expect(savedUser.profile.lastName).toBe(userData.profile.lastName);
      expect(savedUser.role).toBe('user'); // default role
      expect(savedUser.status).toBe('active'); // default status
    });

    it('should hash password before saving', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'plainpassword',
        phone: '+254700000001'
      };

      const user = new User(userData);
      await user.save();

      expect(user.password).not.toBe('plainpassword');
      expect(user.password.length).toBeGreaterThan(20);
      
      // Verify password can be compared
      const isMatch = await bcrypt.compare('plainpassword', user.password);
      expect(isMatch).toBe(true);
    });

    it('should require username, email, password, and phone', async () => {
      const user = new User({});
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.username).toBeDefined();
      expect(error.errors.email).toBeDefined();
      expect(error.errors.password).toBeDefined();
      expect(error.errors.phone).toBeDefined();
    });

    it('should enforce unique username', async () => {
      const userData1 = {
        username: 'uniqueuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000001'
      };

      const userData2 = {
        username: 'uniqueuser', // same username
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000002'
      };

      await new User(userData1).save();

      let error;
      try {
        await new User(userData2).save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.code).toBe(11000); // MongoDB duplicate key error
    });

    it('should enforce unique email', async () => {
      const userData1 = {
        username: 'user1',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000001'
      };

      const userData2 = {
        username: 'user2',
        email: '<EMAIL>', // same email
        password: 'password123',
        phone: '+254700000002'
      };

      await new User(userData1).save();

      let error;
      try {
        await new User(userData2).save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.code).toBe(11000); // MongoDB duplicate key error
    });

    it('should validate email format', async () => {
      const userData = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'password123',
        phone: '+254700000000'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.email).toBeDefined();
    });

    it('should validate phone format', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: 'invalid-phone'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.phone).toBeDefined();
    });
  });

  describe('User Methods', () => {
    let user;

    beforeEach(async () => {
      user = await createTestUser();
    });

    it('should compare password correctly', async () => {
      const isMatch = await user.comparePassword('password123');
      expect(isMatch).toBe(true);

      const isNotMatch = await user.comparePassword('wrongpassword');
      expect(isNotMatch).toBe(false);
    });

    it('should update reputation score', async () => {
      expect(user.reputation.score).toBe(50); // default score

      user.updateReputation(5, 'positive');
      expect(user.reputation.score).toBe(55);
      expect(user.reputation.totalTrades).toBe(1);
      expect(user.reputation.positiveRatings).toBe(1);

      user.updateReputation(3, 'negative');
      expect(user.reputation.score).toBe(52);
      expect(user.reputation.totalTrades).toBe(2);
      expect(user.reputation.negativeRatings).toBe(1);
    });

    it('should get full name', () => {
      user.profile.firstName = 'John';
      user.profile.lastName = 'Doe';
      
      expect(user.getFullName()).toBe('John Doe');
    });

    it('should check if user is verified', () => {
      expect(user.isVerified()).toBe(true); // created with verified status

      user.verification.email.verified = false;
      expect(user.isVerified()).toBe(false);
    });

    it('should update last active timestamp', async () => {
      const originalLastActive = user.lastActive;
      
      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));
      
      user.updateLastActive();
      expect(user.lastActive.getTime()).toBeGreaterThan(originalLastActive.getTime());
    });
  });

  describe('User Validation', () => {
    it('should validate role enum', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000000',
        role: 'invalid-role'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.role).toBeDefined();
    });

    it('should validate status enum', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+254700000000',
        status: 'invalid-status'
      };

      const user = new User(userData);
      
      let error;
      try {
        await user.save();
      } catch (err) {
        error = err;
      }

      expect(error).toBeDefined();
      expect(error.errors.status).toBeDefined();
    });
  });

  describe('User Indexes', () => {
    it('should have proper indexes', async () => {
      const indexes = await User.collection.getIndexes();
      
      // Check for username index
      expect(indexes).toHaveProperty('username_1');
      
      // Check for email index
      expect(indexes).toHaveProperty('email_1');
      
      // Check for phone index
      expect(indexes).toHaveProperty('phone_1');
    });
  });
});
