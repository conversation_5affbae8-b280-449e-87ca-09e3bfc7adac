const { ethers } = require("hardhat");

async function main() {
  console.log("Deploying KryptoPesa Escrow Contract...");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Deploy the contract
  const KryptoPesaEscrow = await ethers.getContractFactory("KryptoPesaEscrow");
  
  // Set fee collector and dispute resolver addresses
  const feeCollector = deployer.address; // In production, use a dedicated address
  const disputeResolver = deployer.address; // In production, use a dedicated admin address
  
  console.log("Fee Collector:", feeCollector);
  console.log("Dispute Resolver:", disputeResolver);
  
  const escrow = await KryptoPesaEscrow.deploy(feeCollector, disputeResolver);
  await escrow.deployed();

  console.log("KryptoPesaEscrow deployed to:", escrow.address);
  console.log("Transaction hash:", escrow.deployTransaction.hash);

  // Wait for a few confirmations
  console.log("Waiting for confirmations...");
  await escrow.deployTransaction.wait(5);

  // Verify contract on Polygonscan (if on Polygon network)
  if (network.name === "polygon" || network.name === "mumbai") {
    console.log("Verifying contract on Polygonscan...");
    try {
      await hre.run("verify:verify", {
        address: escrow.address,
        constructorArguments: [feeCollector, disputeResolver],
      });
      console.log("Contract verified successfully");
    } catch (error) {
      console.log("Verification failed:", error.message);
    }
  }

  // Save deployment info
  const deploymentInfo = {
    network: network.name,
    contractAddress: escrow.address,
    feeCollector: feeCollector,
    disputeResolver: disputeResolver,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    transactionHash: escrow.deployTransaction.hash,
    blockNumber: escrow.deployTransaction.blockNumber,
  };

  console.log("\n=== Deployment Summary ===");
  console.log(JSON.stringify(deploymentInfo, null, 2));

  // Save to file
  const fs = require("fs");
  const path = require("path");
  
  const deploymentsDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir);
  }
  
  const deploymentFile = path.join(deploymentsDir, `${network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  
  console.log(`Deployment info saved to: ${deploymentFile}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
