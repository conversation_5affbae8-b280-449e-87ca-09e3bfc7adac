{"name": "kryptopesa-smart-contracts", "version": "1.0.0", "description": "Smart contracts for KryptoPesa P2P trading platform", "main": "index.js", "scripts": {"compile": "npx hardhat compile", "test": "npx hardhat test", "deploy:local": "npx hardhat run scripts/deploy.js --network localhost", "deploy:polygon": "npx hardhat run scripts/deploy.js --network polygon", "deploy:mumbai": "npx hardhat run scripts/deploy.js --network mumbai", "verify": "npx hardhat verify --network polygon", "node": "npx hardhat node", "clean": "npx hardhat clean"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.2", "@openzeppelin/contracts": "^4.9.3", "hardhat": "^2.17.1", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.4"}, "dependencies": {"dotenv": "^16.3.1"}}